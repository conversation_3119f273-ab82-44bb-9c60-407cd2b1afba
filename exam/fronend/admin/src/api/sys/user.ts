import { get, post } from '~/utils/request'

const baseUrl = '/sys/user'

/**
 * 用户列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 新增用户
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑用户(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑用户(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除用户
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 重置密码
 */
export function resetPassword(id: number): Promise<any> {
  return post<any>(`${baseUrl}/resetPassword`, null, {
    id,
  })
}

/**
 * 获取用户列表
 */
export function getCommonUsers(params?: any): Promise<any> {
  return get<any>(`${baseUrl}/commonUsers`, params)
}
