package infra.auth.core;

import infra.auth.config.AuthConfigProperties;
import infra.core.security.Md5;
import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;

import jakarta.servlet.http.HttpServletRequest;
import org.redisson.client.codec.StringCodec;

import java.util.List;

/**
 * 基于 Redisson 实现的令牌管理
 */
@Slf4j
public class RedisTokenManager implements ITokenManager {
    private final AuthConfigProperties config;
    private final RScript script;

    public RedisTokenManager(AuthConfigProperties config, RedissonClient redissonClient) {
        this.config = config;
        this.script = redissonClient.getScript(StringCodec.INSTANCE);
    }

    /**
     * 创建Token
     */
    @Override
    public void createToken(Long userId, String accessToken, String refreshToken, HttpServletRequest request) {
        if (userId == null || Str.isEmpty(accessToken) || Str.isEmpty(refreshToken)) {
            throw new IllegalArgumentException("用户ID、访问令牌、刷新令牌均不能为空");
        }

        String tokenKey = getKey(userId.toString());
        String hashedAccessToken = hashToken(accessToken);
        String hashedRefreshToken = hashToken(refreshToken);

        try {
            String luaScript = """
                        redis.call('HSET', KEYS[1], ARGV[1], ARGV[2])
                        redis.call('HSET', KEYS[1], ARGV[2], '')
                        redis.call('EXPIRE', KEYS[1], ARGV[3])
                        redis.call('HEXPIRE', KEYS[1], ARGV[4], 'FIELDS', 1, ARGV[1])
                        redis.call('HEXPIRE', KEYS[1], ARGV[3], 'FIELDS', 1, ARGV[2])
                        return 1
                    """;

            script.eval(RScript.Mode.READ_WRITE,
                    luaScript,
                    RScript.ReturnType.INTEGER,
                    List.of(tokenKey),
                    hashedAccessToken,              // ARGV[1]
                    hashedRefreshToken,             // ARGV[2]
                    config.getRefreshTokenExpireSeconds(), // ARGV[3]
                    config.getAccessTokenExpireSeconds()  // ARGV[4]
            );
        } catch (Exception e) {
            log.error("创建token失败: {}", userId, e);
            throw new RuntimeException("创建Token失败", e);
        }
    }

    /**
     * 验证Token
     */
    @Override
    public boolean validAccessToken(Long userId, String accessToken, HttpServletRequest request) {
        if (userId == null || Str.isEmpty(accessToken)) return false;

        String tokenKey = getKey(userId.toString());
        String hashedAccessToken = hashToken(accessToken);

        try {
            String luaScript = "return redis.call('HEXISTS', KEYS[1], ARGV[1])";
            Long result = script.eval(
                    RScript.Mode.READ_ONLY,
                    luaScript,
                    RScript.ReturnType.INTEGER,
                    List.of(tokenKey),
                    hashedAccessToken
            );
            return result != null && result == 1L;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 使用刷新令牌
     */
    @Override
    public boolean useRefreshToken(Long userId, String refreshToken, HttpServletRequest request) {
        if (userId == null || Str.isEmpty(refreshToken)) return false;

        String tokenKey = getKey(userId.toString());
        String hashedRefreshToken = hashToken(refreshToken);

        try {
            String luaScript = """
                       -- 第一阶段：检查令牌是否存在
                       local exists = redis.call('HEXISTS', KEYS[1], ARGV[1])
                       if exists == 0 then
                           return 0
                       end
                    
                       -- 第二阶段：获取值并处理
                       local value = redis.call('HGET', KEYS[1], ARGV[1])
                       if not value or value == '' then
                           -- 首次使用，设置标记和过期时间
                           redis.call('HSET', KEYS[1], ARGV[1], 'used')
                           local ok, err = pcall(redis.call, 'HEXPIRE', KEYS[1], ARGV[2], 'FIELDS', 1, ARGV[1])
                           return 1
                       elseif value == 'used' then
                           -- 已使用过，在30秒内仍可重复使用
                           return 1
                       else
                           -- 其他状态，拒绝使用
                           return 0
                       end
                    """;

            Long result = script.eval(
                    RScript.Mode.READ_WRITE,
                    luaScript,
                    RScript.ReturnType.INTEGER,
                    List.of(tokenKey),
                    hashedRefreshToken,
                    30L);

            return result != null && result == 1L;
        } catch (Exception e) {
            log.warn("使用刷新令牌失败: {}", userId, e);
            return false;
        }
    }

    /**
     * 登出(注销令牌)
     */
    @Override
    public void logout(Long userId, String accessToken) {
        if (userId == null || Str.isEmpty(accessToken)) return;

        String tokenKey = getKey(userId.toString());
        String hashedAccessToken = hashToken(accessToken);

        try {
            String luaScript = """
                       local refreshToken = redis.call('HGET', KEYS[1], ARGV[1])
                       if refreshToken then
                           redis.call('HDEL', KEYS[1], ARGV[1])
                           if refreshToken ~= '' then
                               redis.call('HDEL', KEYS[1], refreshToken)
                           end
                           return 1
                       else
                           return 0
                       end
                    """;

            Long result = script.eval(
                    RScript.Mode.READ_WRITE,
                    luaScript,
                    RScript.ReturnType.INTEGER,
                    List.of(tokenKey),
                    hashedAccessToken);
        } catch (Exception e) {
            log.warn("注销令牌失败: {}", userId, e);
        }
    }

    /**
     * 获取存取key
     */
    private String getKey(String userId) {
        if (Str.isEmpty(userId)) {
            throw new IllegalArgumentException("userId不能为空");
        }

        return config.getRedisTokenPrefix() + userId;
    }

    /**
     * md5 hash token值
     */
    private String hashToken(String token) {
        return Md5.hashString(token).substring(0, 16);
    }
}
