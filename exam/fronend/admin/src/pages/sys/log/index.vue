<script setup lang="ts">
definePage({
  meta: {
    title: '监控日志',
    menu: true,
    order: 8,
    icon: 'mdi:math-log',
  },
})
</script>

<template>
<p>1. 日志采集：集中记录系统关键操作日志（模块操作、异常事件、业务关键节点如报告签发）、异常日志（接口失败、权限拦截）；</p>
<p>2. 日志筛选：支持按时间、模块、日志类型、操作人筛选，快速定位问题；</p>
<p>3. 日志导出：支持导出 Excel 格式日志，供运维审计；</p>
<p>4. 日志留存：按规则设置留存周期（重要日志 1 年、普通日志 3 个月），自动清理过期日志</p>

系统技术底座 “流程管控无死角”🔶21-2；检后阶段 “报告签发（记录签发信息）”🔶21-50；检查科室 “全流程质量监控（抓取各环节数据）”🔶21-84
</template>
