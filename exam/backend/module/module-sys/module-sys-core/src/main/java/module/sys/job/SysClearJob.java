package module.sys.job;

import infra.task.core.IJob;
import lombok.extern.slf4j.Slf4j;
import module.sys.config.SysConfigProperties;
import module.sys.service.AuditLogService;
import module.sys.service.ValidCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 系统模块定量清理服务
 */
@Slf4j
@Component
public class SysClearJob implements IJob {
    @Autowired
    private ValidCodeService validCodeService;
    @Autowired
    private AuditLogService auditLogService;
    @Autowired
    private SysConfigProperties config;

    @Override
    public String getJobId() {
        return "sys_clear";
    }

    @Override
    public String getJobDesc() {
        return "系统模块-清理过期数据";
    }

    @Override
    public String getCron() {
        return "0 0 3 5,15,25 * *";
    }

    @Override
    public String getCronDesc() {
        return "每5号,15号,25号凌晨3:00执行";
    }

    @Override
    public Integer getSort() {
        return 2;
    }

    @Override
    public String execute() {
        validCodeService.clearInvalid();
        log.info("已清理过期验证码");

        var before = LocalDate.now().minusDays(config.getAuditLogDays())
                .atStartOfDay();
        int clearCount = auditLogService.clearBefore(before);
        log.info("已清理{}前审计日志，共清理{}条数据", before.format(DateTimeFormatter.ISO_DATE), clearCount);

        return String.format("已清理过期验证码，已清理%s前审计日志，共清理%s条数据",
                before.format(DateTimeFormatter.ISO_DATE),
                clearCount);
    }
}
