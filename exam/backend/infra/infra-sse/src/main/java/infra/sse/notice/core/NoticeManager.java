package infra.sse.notice.core;

import infra.core.exception.BizException;
import infra.core.exception.ConfigException;
import infra.core.text.JSON;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.sse.core.SseConnectionManager;
import infra.sse.notice.config.NoticeConfigProperties;
import infra.sse.notice.model.NoticeMessage;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.UUID;

/**
 * SSE通知管理
 */
@Slf4j
public class NoticeManager implements INoticeService, DisposableBean {
    private static final String EVENT_NAME = "notice";

    private final SseConnectionManager connectionManager;
    private final INoticeStorage storage;

    // Redis相关
    private final boolean redisEnabled;
    private RedissonClient redissonClient;
    private RTopic noticeTopic;
    private String instanceId;

    public NoticeManager(SseConnectionManager connectionManager,
                         INoticeStorage storage, NoticeConfigProperties config,
                         RedissonClient redissonClient) {
        this.connectionManager = connectionManager;
        this.storage = storage;

        this.redisEnabled = config.isRedis();
        if (this.redisEnabled) {
            if (redissonClient == null) {
                throw new ConfigException("SSE启用了redis配置，但没有获取到redissonClient实例");
            }
            this.redissonClient = redissonClient;
            this.instanceId = generateInstanceId();

            registerNoticeListener(config.getRedisChannelPrefix());
        }
    }

    @Async
    @Override
    public void noticeUser(Long userId, NoticeMessage message) {
        try {
            Long entityId = storage.saveMessage(userId, message);
            message.setId(entityId);
        } catch (Exception e) {
            log.error("存储通知消息失败", e);
            throw new BizException("存储通知消息失败:" + e.getMessage(), e);
        }

        // 本地推送
        boolean localSuccess = connectionManager.sendEvent(userId, EVENT_NAME, message);
        // Redis推送
        if (redisEnabled) {
            publishRedisMessage(userId, message);
        }
    }

    @Async
    @Override
    public void noticeUsers(List<Long> userIds, NoticeMessage message) {
        List<Long> ids;
        try {
            ids = storage.saveMessage(userIds, message);
            if (ids.size() != userIds.size()) {
                throw new BizException("存储消息数据与发送数据不一致");
            }
        } catch (Exception e) {
            log.error("存储通知消息失败", e);
            throw new BizException("存储通知消息失败:" + e.getMessage(), e);
        }

        for (int i = 0; i < userIds.size(); i++) {
            try {
                message.setId(ids.get(i));
                Long userId = userIds.get(i);

                // 本地推送
                connectionManager.sendEvent(userId, EVENT_NAME, message);
                // Redis推送
                if (redisEnabled) {
                    publishRedisMessage(userId, message);
                }
            } catch (Exception e) {
                log.debug("推送消息给用户 {} 失败", userIds.get(i), e);
            }
        }
    }

    @Override
    public int getUnreadCount(Long userId) {
        return storage.getUnreadCount(userId);
    }

    @Override
    public void markAsRead(Long userId, List<Long> ids) {
        storage.markAsRead(userId, ids);
    }

    @Override
    public void markAllAsRead(Long userId) {
        storage.markAllAsRead(userId);
    }

    @Override
    public void delete(Long userId, List<Long> ids) {
        storage.delete(userId, ids);
    }

    @Override
    public void deleteAll(Long userId) {
        storage.deleteAll(userId);
    }

    @Override
    public List<NoticeMessage> getList(Long userId, int limit) {
        return storage.getList(userId, limit);
    }

    @Override
    public PageResult<NoticeMessage> getPage(Long userId, NoticeMessage query, PageParam pageParam) {
        return storage.getPage(userId, query, pageParam);
    }

    @Override
    public void destroy() {
        if (redisEnabled && noticeTopic != null) {
            try {
                noticeTopic.removeAllListeners();
                log.info("已清理Redis SSE通知订阅");
            } catch (Exception e) {
                log.warn("清理Redis SSE通知订阅时出错", e);
            }
        }
    }

    /**
     * Redis订阅
     */
    private void registerNoticeListener(String channelPrefix) {
        String topicName = channelPrefix + "broadcast";
        noticeTopic = redissonClient.getTopic(topicName);
        // 订阅Redis消息
        noticeTopic.addListener(String.class, (channel, message) -> handleRedisMessage(message));
        log.info("注册SSE Redis通知发布订阅，实例ID: {}", instanceId);
    }

    /**
     * 发布通知消息到Redis
     */
    @Async
    private void publishRedisMessage(Long userId, NoticeMessage message) {
        if (!redisEnabled || noticeTopic == null) {
            return;
        }

        try {
            // 消息格式：instanceId:userId:messageJson
            String messageJson = JSON.toJson(message);
            String redisMessage = instanceId + ":" + userId + ":" + messageJson;

            noticeTopic.publish(redisMessage);
            log.debug("发布SSE通知消息到Redis: userId={}", userId);
        } catch (Exception e) {
            log.error("发布SSE通知消息到Redis失败", e);
        }
    }

    /**
     * 处理Redis消息
     */
    @Async
    private void handleRedisMessage(String message) {
        if (Str.isEmpty(message)) {
            return;
        }

        try {
            // 解析消息格式：instanceId:userId:messageJson
            String[] parts = message.split(":", 3);
            if (parts.length < 3) {
                return;
            }

            String sourceInstanceId = parts[0];
            Long userId = Long.parseLong(parts[1]);
            String messageJson = parts[2];

            // 忽略自己发送的消息
            if (instanceId.equals(sourceInstanceId)) {
                return;
            }
            // 检查本实例是否有该用户的连接
            if (!connectionManager.isUserOnline(userId)) {
                return;
            }

            // 反序列化消息并推送
            NoticeMessage noticeMessage = JSON.fromJson(messageJson, NoticeMessage.class);
            boolean success = connectionManager.sendEvent(userId, EVENT_NAME, noticeMessage);

            if (success) {
                log.debug("收到Redis SSE通知消息并成功推送给用户: {}", userId);
            }
        } catch (Exception e) {
            log.error("处理Redis SSE消息失败: {}", message, e);
        }
    }

    /**
     * 生成实例ID
     */
    private String generateInstanceId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
}


