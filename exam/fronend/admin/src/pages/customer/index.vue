<script setup lang="ts">
definePage({
  meta: {
    title: '客户关系',
    menu: true,
    order: -7,
    icon: 'mynaui:users-group',
  },
})

// 客户关系统计数据
const customerStats = ref({
  totalCustomers: 1248,
  newToday: 23,
  pendingFollow: 45,
  reserved: 89,
  groupCustomers: 156
})

// 任务列表数据
const taskList = ref([
  { taskName: '跟进体检意向', customerName: '张三', deadline: '2024-06-10', status: '未处理', priority: 'high' },
  { taskName: '完善客户档案', customerName: '李四', deadline: '2024-06-11', status: '未处理', priority: 'medium' },
  { taskName: '确认体检排期', customerName: '王五', deadline: '2024-06-09', status: '已处理', priority: 'low' },
  { taskName: '跟进团检意向', customerName: '某科技公司', deadline: '2024-06-12', status: '未处理', priority: 'high' },
])

// 客户列表数据
const customerList = ref([
  { customerName: '张三', phone: '13800138000', customerType: '个检', intentStatus: '已预约', reserveStatus: '明日体检' },
  { customerName: '李四', phone: '13900139000', customerType: '个检', intentStatus: '跟进中', reserveStatus: '未预约' },
  { customerName: '某科技公司', phone: '020-88888888', customerType: '团检', intentStatus: '已转化', reserveStatus: '今日体检' },
  { customerName: '赵六', phone: '13700137000', customerType: '个检', intentStatus: '已预约', reserveStatus: '今日体检' },
])

// 客户转化趋势数据（最近7天）
const conversionTrend = ref([
  { date: '08-14', newCustomers: 18, converted: 12, rate: 66.7 },
  { date: '08-15', newCustomers: 22, converted: 16, rate: 72.7 },
  { date: '08-16', newCustomers: 15, converted: 9, rate: 60.0 },
  { date: '08-17', newCustomers: 28, converted: 21, rate: 75.0 },
  { date: '08-18', newCustomers: 19, converted: 14, rate: 73.7 },
  { date: '08-19', newCustomers: 25, converted: 18, rate: 72.0 },
  { date: '08-20', newCustomers: 23, converted: 17, rate: 73.9 },
])

// 功能模块数据
const modules = ref([
  { name: '联系人管理', desc: '录入/导入客户联系信息', icon: 'ep:user-filled', path: 'contact', color: 'rose' },
  { name: '意向跟踪', desc: '查看联系记录与健康指导', icon: 'ep:clock', path: 'intent', color: 'amber' },
  { name: '在售套餐查询', desc: '查看体检套餐与价格', icon: 'ep:shopping-bag', path: 'package', color: 'blue' },
  { name: '预约管理', desc: '体检预约与排期管理', icon: 'ep:calendar', path: 'reserve', color: 'emerald' }
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-rose-50 to-pink-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <!-- 客户总数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-rose-50 to-rose-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-rose-600 dark:text-rose-400">
              {{ customerStats.totalCustomers }}
            </div>
            <div class="text-sm text-rose-700 dark:text-rose-300 mt-1 font-medium">
              客户总数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-rose-400 to-rose-600 dark:from-rose-500 dark:to-rose-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ep:user-filled" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 今日新增 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ customerStats.newToday }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              今日新增
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ep:plus" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 待跟进 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-amber-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-amber-600 dark:text-amber-400">
              {{ customerStats.pendingFollow }}
            </div>
            <div class="text-sm text-amber-700 dark:text-amber-300 mt-1 font-medium">
              待跟进
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ep:clock" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 已预约 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {{ customerStats.reserved }}
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              已预约
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ep:calendar" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 团检客户 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {{ customerStats.groupCustomers }}
            </div>
            <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
              团检客户
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ep:office-building" class="text-white text-xl" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 左侧：功能模块和任务 -->
      <div class="lg:col-span-2 space-y-4">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 待处理任务 -->
        <el-card>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium">待处理任务</span>
              <el-tag type="warning" size="small">{{ taskList.filter(t => t.status === '未处理').length }} 项</el-tag>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="task in taskList.slice(0, 4)" :key="task.taskName" class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
              <div class="flex-1">
                <p class="font-medium">{{ task.taskName }}</p>
                <p class="text-sm text-gray-500">客户：{{ task.customerName }}</p>
                <p class="text-xs text-gray-400">截止：{{ task.deadline }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <el-tag
                  :type="task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'info'"
                  size="small"
                >
                  {{ task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低' }}
                </el-tag>
                <el-tag
                  :type="task.status === '已处理' ? 'success' : 'warning'"
                  size="small"
                >
                  {{ task.status }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 客户转化趋势 -->
        <el-card>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium">客户转化趋势</span>
              <span class="text-sm text-gray-500">最近7天</span>
            </div>
          </template>

          <div>
            <div class="grid grid-cols-7 gap-2 mb-4">
              <div v-for="day in conversionTrend" :key="day.date" class="text-center">
                <div class="text-xs text-gray-500 mb-1">{{ day.date }}</div>
                <div class="h-16 bg-gray-100 rounded flex flex-col justify-end p-1">
                  <div
                    class="bg-primary rounded-sm mb-1"
                    :style="{ height: `${(day.newCustomers / 30) * 100}%` }"
                    :title="`新增: ${day.newCustomers}`"
                  />
                  <div
                    class="bg-success rounded-sm"
                    :style="{ height: `${(day.converted / 30) * 100}%` }"
                    :title="`转化: ${day.converted}`"
                  />
                </div>
                <div class="text-xs font-medium mt-1 text-success">{{ day.rate }}%</div>
              </div>
            </div>

            <div class="flex items-center justify-center space-x-6 text-sm">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-primary rounded" />
                <span>新增客户</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-success rounded" />
                <span>成功转化</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：客户管理详情 -->
      <div class="space-y-4">
        <!-- 客户类型分布 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:pie-chart" class="text-primary" />
              <span class="font-medium">客户类型分布</span>
            </div>
          </template>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span>个人体检</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-gray-200 rounded-full h-2">
                  <div class="bg-primary h-2 rounded-full" style="width: 75%" />
                </div>
                <span class="text-sm font-medium">936</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span>团体体检</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-gray-200 rounded-full h-2">
                  <div class="bg-success h-2 rounded-full" style="width: 25%" />
                </div>
                <span class="text-sm font-medium">312</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span>VIP客户</span>
              <div class="flex items-center space-x-2">
                <div class="w-20 bg-gray-200 rounded-full h-2">
                  <div class="bg-warning h-2 rounded-full" style="width: 8%" />
                </div>
                <span class="text-sm font-medium">98</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 最近客户动态 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:bell" class="text-warning" />
              <span class="font-medium">最近客户动态</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="customer in customerList.slice(0, 4)" :key="customer.customerName" class="flex items-center justify-between">
              <div class="flex-1">
                <p class="font-medium">{{ customer.customerName }}</p>
                <p class="text-sm text-gray-500">{{ customer.customerType }} - {{ customer.intentStatus }}</p>
              </div>
              <el-tag
                :type="customer.reserveStatus === '今日体检' ? 'success' : customer.reserveStatus === '明日体检' ? 'warning' : 'info'"
                size="small"
              >
                {{ customer.reserveStatus }}
              </el-tag>
            </div>
          </div>
        </el-card>


      </div>
    </div>
  </div>
</template>