package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Medical;
import module.sys.mapper.MedicalMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * 病史字典服务
 */
@Slf4j
@Service
public class MedicalService extends ServiceBase<MedicalMapper, Medical> {
    @Override
    protected Function<QueryWrapper<Medical>, QueryWrapper<Medical>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<Medical>, QueryWrapper<Medical>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(Medical entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByCode(entity.getTypeId(), entity.getIcdCode(),
                isUpdate ? entity.getId() : null)) {
            throw new BizException("ICD编码已存在");
        }

        setPinyin(entity);
    }

    /**
     * 检查编码是否已存在
     */
    public boolean existsByCode(Long typeId, String code, Long excludeId) {
        if (Str.isEmpty(code) || typeId == null) {
            return false;
        }

        return exists(w -> {
            w.eq("type_id", typeId)
                    .eq("icd_code", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 根据类别id获取所有
     */
    public List<Medical> getListByType(Long typeId) {
        return getListByType(typeId, FilterOptions.DEFAULTS);
    }

    /**
     * 根据类别id获取所有
     */
    public List<Medical> getListByType(Long typeId, FilterOptions filterOptions) {
        return getList(w -> w.eq("type_id", typeId), filterOptions);
    }

    /**
     * 设置拼音
     */
    private void setPinyin(Medical value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
