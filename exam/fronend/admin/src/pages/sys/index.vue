<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { info as getRuntimeInfo } from '~/api/sys/runtime'
import { useLoading } from '~/composables'
import { formatBytesSize } from '~/utils'

definePage({
  meta: {
    title: '系统管理',
    menu: true,
    order: -1,
    icon: 'hugeicons:system-update-01',
  },
})

interface DiskInfo {
  disk: string
  mount: string
  fsType: string
  total: number
  used: number
  free: number
  usage: number
}

// 运行信息
interface RuntimeInfo {
  osInfo: string
  pythonVersion: string
  processor: string
  machine: string
  cpuCount: number
  cpuLogicalCount: number
  cpuUsage: number
  totalMemory: number
  usedMemory: number
  processMemory: number
  processCpuUsage: number
  uptime: number
  serverTime: string
  appDir: string
  disks: DiskInfo[]
}

// 状态
const data = ref<RuntimeInfo>({} as RuntimeInfo)
const { loading, withLoading } = useLoading()

// 系统管理统计数据
const sysStats = ref({
  accounts: {
    total: 89,
    active: 76,
    locked: 3,
    expiring: 8
  },
  organization: {
    departments: 12,
    positions: 45,
    activeUsers: 76,
    pendingApprovals: 5
  },
  parameters: {
    total: 156,
    modified: 23,
    alerts: 2,
    lastSync: '2024-08-22 14:30'
  },
  interfaces: {
    total: 8,
    connected: 7,
    failed: 1,
    dataSync: 2456
  },
  monitoring: {
    uptime: 99.8,
    alerts: 3,
    backups: 28,
    performance: 92.5
  }
})

// 功能模块数据
const modules = ref([
  { name: '组织架构', desc: '科室配置，岗位管理，架构同步', icon: 'hugeicons:structure-01', path: 'org', color: 'indigo' },
  { name: '账户管理', desc: '账户创建，权限分配，登录安全', icon: 'hugeicons:user-account', path: 'account', color: 'emerald' },
  { name: '参数管理', desc: '系统参数配置，校验监控', icon: 'codicon:symbol-parameter', path: 'parameter', color: 'blue' },
  { name: '知识库', desc: '术语维护，版本管理，术语关联', icon: 'hugeicons:knowledge-02', path: 'knowledge', color: 'orange' },
  { name: '文书模版', desc: '检前检中检后模板维护', icon: 'fluent-mdl2:web-template', path: 'template', color: 'purple' },
  { name: '接口管理', desc: '院内外接口配置，权限控制', icon: 'nonicons:interface-16', path: 'interface', color: 'cyan' },
  { name: '运维监控', desc: '系统状态监控，数据备份管理', icon: 'lsicon:operation-outline', path: 'operation', color: 'red' },
  { name: '系统日志', desc: '日志采集，筛选导出，留存管理', icon: 'mdi:math-log', path: 'log', color: 'slate' }
])

// 系统告警数据
const systemAlerts = ref([
  { id: 1, type: '性能告警', content: 'CPU使用率超过80%', level: 'high', time: '2024-08-22 14:25' },
  { id: 2, type: '接口异常', content: 'HIS系统连接失败', level: 'high', time: '2024-08-22 14:20' },
  { id: 3, type: '参数变更', content: '数据同步频率已调整', level: 'medium', time: '2024-08-22 14:15' },
  { id: 4, type: '账户安全', content: '3个账户即将过期', level: 'medium', time: '2024-08-22 14:10' },
  { id: 5, type: '备份完成', content: '数据库备份成功', level: 'low', time: '2024-08-22 14:00' }
])

// 系统性能数据
const performanceData = ref([
  { metric: 'CPU使用率', current: 75, threshold: 80, status: 'normal' },
  { metric: '内存使用率', current: 68, threshold: 85, status: 'normal' },
  { metric: '磁盘使用率', current: 45, threshold: 90, status: 'normal' },
  { metric: '网络延迟', current: 12, threshold: 50, status: 'normal' }
])

// 加载数据
async function fetchData() {
  await withLoading(async () => {
    const res = await getRuntimeInfo()
    data.value = res.data
  })
}

// 格式化时间
function formatDuration(seconds: number): string {
  if (!seconds)
    return ''
  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  const parts = []
  if (days > 0)
    parts.push(`${days}天`)
  if (hours > 0)
    parts.push(`${hours}小时`)
  if (minutes > 0)
    parts.push(`${minutes}分钟`)
  if (remainingSeconds > 0 || parts.length === 0)
    parts.push(`${remainingSeconds}秒`)

  return parts.join('')
}

// 计算内存使用率
function getMemoryUsagePercent(used: number, total: number) {
  return Math.round((used / total) * 100) || 0
}

// 刷新
async function handleRefresh() {
  try {
    await fetchData()
  }
  catch { }
}

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-stone-50 to-zinc-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <!-- 账户总数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-indigo-600 dark:text-indigo-400">
              {{ sysStats.accounts.total }}
            </div>
            <div class="text-sm text-indigo-700 dark:text-indigo-300 mt-1 font-medium">
              账户总数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-indigo-400 to-indigo-600 dark:from-indigo-500 dark:to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="hugeicons:user-account" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 组织架构 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ sysStats.organization.departments }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              部门数量
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="hugeicons:structure-01" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 系统参数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {{ sysStats.parameters.total }}
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              系统参数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="codicon:symbol-parameter" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 接口连接 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">
              {{ sysStats.interfaces.connected }}/{{ sysStats.interfaces.total }}
            </div>
            <div class="text-sm text-orange-700 dark:text-orange-300 mt-1 font-medium">
              接口连接
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-orange-400 to-orange-600 dark:from-orange-500 dark:to-orange-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="nonicons:interface-16" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 系统运行时间 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {{ sysStats.monitoring.uptime }}%
            </div>
            <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
              系统可用性
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="lsicon:operation-outline" class="text-white text-xl" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：核心功能模块和系统监控 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="[
                  module.color === 'indigo' ? 'bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-gray-700 dark:to-gray-600 hover:from-indigo-100 hover:to-indigo-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'emerald' ? 'bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-gray-700 dark:to-gray-600 hover:from-emerald-100 hover:to-emerald-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'blue' ? 'bg-gradient-to-r from-blue-50 to-blue-100 dark:from-gray-700 dark:to-gray-600 hover:from-blue-100 hover:to-blue-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'orange' ? 'bg-gradient-to-r from-orange-50 to-orange-100 dark:from-gray-700 dark:to-gray-600 hover:from-orange-100 hover:to-orange-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'purple' ? 'bg-gradient-to-r from-purple-50 to-purple-100 dark:from-gray-700 dark:to-gray-600 hover:from-purple-100 hover:to-purple-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'cyan' ? 'bg-gradient-to-r from-cyan-50 to-cyan-100 dark:from-gray-700 dark:to-gray-600 hover:from-cyan-100 hover:to-cyan-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  module.color === 'red' ? 'bg-gradient-to-r from-red-50 to-red-100 dark:from-gray-700 dark:to-gray-600 hover:from-red-100 hover:to-red-200 dark:hover:from-gray-600 dark:hover:to-gray-500' :
                  'bg-gradient-to-r from-slate-50 to-slate-100 dark:from-gray-700 dark:to-gray-600 hover:from-slate-100 hover:to-slate-200 dark:hover:from-gray-600 dark:hover:to-gray-500'
                ]"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="[
                    module.color === 'indigo' ? 'bg-gradient-to-br from-indigo-400 to-indigo-600' :
                    module.color === 'emerald' ? 'bg-gradient-to-br from-emerald-400 to-emerald-600' :
                    module.color === 'blue' ? 'bg-gradient-to-br from-blue-400 to-blue-600' :
                    module.color === 'orange' ? 'bg-gradient-to-br from-orange-400 to-orange-600' :
                    module.color === 'purple' ? 'bg-gradient-to-br from-purple-400 to-purple-600' :
                    module.color === 'cyan' ? 'bg-gradient-to-br from-cyan-400 to-cyan-600' :
                    module.color === 'red' ? 'bg-gradient-to-br from-red-400 to-red-600' :
                    'bg-gradient-to-br from-slate-400 to-slate-600'
                  ]"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 系统监控 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg" v-loading="loading">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-800 dark:text-gray-200">系统监控</span>
              <el-button type="primary" size="small" :loading="loading" @click="handleRefresh">
                <template #icon>
                  <icon icon="mdi:refresh" />
                </template>
                刷新
              </el-button>
            </div>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 系统信息 -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center mb-3">
                <icon icon="mdi:server" class="text-lg mr-2 text-gray-600 dark:text-gray-400" />
                <span class="font-medium text-gray-800 dark:text-gray-200">系统信息</span>
              </div>
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500">操作系统</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ data.osInfo }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">运行时间</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ formatDuration(data.uptime) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">服务器时间</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ data.serverTime }}</span>
                </div>
              </div>
            </div>

            <!-- CPU信息 -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center mb-3">
                <icon icon="mdi:cpu-64-bit" class="text-lg mr-2 text-gray-600 dark:text-gray-400" />
                <span class="font-medium text-gray-800 dark:text-gray-200">CPU信息</span>
              </div>
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500">物理核心</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ data.cpuCount }} 核</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">逻辑核心</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ data.cpuLogicalCount }} 核</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-500">CPU使用率</span>
                  <el-progress :percentage="data.cpuUsage" class="w-20" size="small" />
                </div>
              </div>
            </div>

            <!-- 内存信息 -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex items-center mb-3">
                <icon icon="mdi:memory" class="text-lg mr-2 text-gray-600 dark:text-gray-400" />
                <span class="font-medium text-gray-800 dark:text-gray-200">内存信息</span>
              </div>
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-500">总内存</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ formatBytesSize(data.totalMemory) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-500">可用内存</span>
                  <span class="text-gray-800 dark:text-gray-200">{{ formatBytesSize(data.totalMemory - data.usedMemory) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-500">内存使用率</span>
                  <el-progress :percentage="getMemoryUsagePercent(data.usedMemory, data.totalMemory)" class="w-20" size="small" />
                </div>
              </div>
            </div>
          </div>

          <!-- 磁盘信息 -->
          <div class="mt-6">
            <div class="flex items-center mb-4">
              <icon icon="mdi:harddisk" class="text-lg mr-2 text-gray-600 dark:text-gray-400" />
              <span class="font-medium text-gray-800 dark:text-gray-200">磁盘信息</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="disk in data.disks" :key="disk.disk"
                class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div class="mb-3">
                  <h4 class="font-medium text-gray-800 dark:text-gray-200">{{ disk.mount }}</h4>
                  <div class="text-sm text-gray-500">{{ disk.disk }} ({{ disk.fsType }})</div>
                </div>
                <div class="space-y-3">
                  <el-progress
                    :percentage="disk.usage"
                    :stroke-width="6"
                    :status="disk.usage >= 80 ? 'exception' : ''"
                  />
                  <div class="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <div class="text-gray-500">总计</div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">{{ formatBytesSize(disk.total) }}</div>
                    </div>
                    <div>
                      <div class="text-gray-500">已用</div>
                      <div class="font-medium text-gray-800 dark:text-gray-200">{{ formatBytesSize(disk.used) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：系统告警和性能监控 -->
      <div class="space-y-6">
        <!-- 系统告警 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:warning" class="text-warning" />
              <span class="font-medium text-gray-800 dark:text-gray-200">系统告警</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="alert in systemAlerts.slice(0, 5)" :key="alert.id" class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ alert.type }}</span>
                  <el-tag
                    :type="alert.level === 'high' ? 'danger' : alert.level === 'medium' ? 'warning' : 'info'"
                    size="small"
                  >
                    {{ alert.level === 'high' ? '严重' : alert.level === 'medium' ? '警告' : '信息' }}
                  </el-tag>
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ alert.content }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">{{ alert.time }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 性能监控 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:data-analysis" class="text-primary" />
              <span class="font-medium text-gray-800 dark:text-gray-200">性能监控</span>
            </div>
          </template>

          <div class="space-y-4">
            <div v-for="metric in performanceData" :key="metric.metric" class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">{{ metric.metric }}</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="metric.current"
                  :show-text="false"
                  class="w-20"
                  :color="metric.current > metric.threshold ? '#f56565' : '#10b981'"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200 w-12">{{ metric.current }}{{ metric.metric.includes('延迟') ? 'ms' : '%' }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 系统概览 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:monitor" class="text-success" />
              <span class="font-medium text-gray-800 dark:text-gray-200">系统概览</span>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">活跃账户率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((sysStats.accounts.active / sysStats.accounts.total) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round((sysStats.accounts.active / sysStats.accounts.total) * 100) }}%
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">接口连通率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((sysStats.interfaces.connected / sysStats.interfaces.total) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round((sysStats.interfaces.connected / sysStats.interfaces.total) * 100) }}%
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">参数稳定性</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round(((sysStats.parameters.total - sysStats.parameters.alerts) / sysStats.parameters.total) * 100)"
                  :show-text="false"
                  class="w-20"
                  color="#10b981"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round(((sysStats.parameters.total - sysStats.parameters.alerts) / sysStats.parameters.total) * 100) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
