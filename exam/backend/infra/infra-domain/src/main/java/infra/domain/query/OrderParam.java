package infra.domain.query;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 排序参数
 */
public record OrderParam(
        @Schema(description = "排序字段")
        String sort,
        @Schema(description = "是否升序")
        boolean asc
) implements Serializable {
    /**
     * 默认排序字段（id倒序）
     */
    public static final OrderParam ID_DESC = new OrderParam("id", false);
}
