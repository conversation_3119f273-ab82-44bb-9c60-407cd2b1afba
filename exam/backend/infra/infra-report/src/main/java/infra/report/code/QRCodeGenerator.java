package infra.report.code;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import infra.report.exception.CodeException;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成器
 */
@Slf4j
public class QRCodeGenerator implements ICodeGenerator {

    private final QRCodeWriter writer = new QRCodeWriter();

    @Override
    public BufferedImage generateImage(String data, CodeRenderOptions options) {
        validateInput(data, options);
        try {
            BitMatrix bitMatrix = createBitMatrix(data, options);
            BufferedImage qrImage = createBufferedImage(bitMatrix, options);

            // 如果配置了logo，则添加logo
            var logooptions = options.getLogo();
            if (logooptions != null && logooptions.getLogoImage() != null) {
                return addLogoToQRCode(qrImage, logooptions);
            }
            return qrImage;
        } catch (WriterException e) {
            log.error("生成条码失败: {}", e.getMessage(), e);
            throw new CodeException("条码生成失败", e);
        }
    }

    @Override
    public void generateToStream(String data, CodeRenderOptions options, OutputStream outputStream) {
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        try {
            BufferedImage image = generateImage(data, options);
            ImageIO.write(image, options.getFormat(), outputStream);
        } catch (IOException e) {
            log.error("输出条码到流失败: {}", e.getMessage(), e);
            throw new CodeException("输出条码失败", e);
        }
    }

    @Override
    public byte[] generateBytes(String data, CodeRenderOptions options) {
        try (ByteArrayOutputStream byteStream = new ByteArrayOutputStream()) {
            generateToStream(data, options, byteStream);
            return byteStream.toByteArray();
        } catch (IOException e) {
            log.error("生成条码字节数组失败: {}", e.getMessage(), e);
            throw new CodeException("生成条码字节数组失败", e);
        }
    }

    private void validateInput(String data, CodeRenderOptions options) {
        if (data == null || data.trim().isEmpty()) {
            throw new IllegalArgumentException("数据不能为空");
        }
        if (data.length() > 4296) {
            throw new IllegalArgumentException("二维码数据长度不能超过4296字符");
        }
        if (options == null) {
            throw new IllegalArgumentException("配置不能为空");
        }
        options.validate();
    }

    private BitMatrix createBitMatrix(String data, CodeRenderOptions options) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, options.getErrorCorrectionLevel());
        hints.put(EncodeHintType.MARGIN, options.getMargin());
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        return writer.encode(data, BarcodeFormat.QR_CODE,
                options.getWidth(), options.getHeight(), hints);
    }

    private BufferedImage createBufferedImage(BitMatrix bitMatrix, CodeRenderOptions options) {
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();

        try {
            // 设置抗锯齿
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 设置背景色
            graphics.setColor(options.getBackgroundColor());
            graphics.fillRect(0, 0, width, height);

            // 绘制
            graphics.setColor(options.getForegroundColor());
            for (int y = 0; y < height; y++) {
                int startX = -1;
                for (int x = 0; x < width; x++) {
                    if (bitMatrix.get(x, y)) {
                        if (startX == -1) {
                            startX = x;
                        }
                    } else {
                        if (startX != -1) {
                            graphics.fillRect(startX, y, x - startX, 1);
                            startX = -1;
                        }
                    }
                }
                // 处理行末的连续点
                if (startX != -1) {
                    graphics.fillRect(startX, y, width - startX, 1);
                }
            }
        } finally {
            graphics.dispose();
        }
        return image;
    }


    /**
     * 为二维码添加logo
     */
    private BufferedImage addLogoToQRCode(BufferedImage qrImage, CodeRenderOptions.LogoOptions logooptions) {
        int qrWidth = qrImage.getWidth();
        int qrHeight = qrImage.getHeight();

        // 检查Logo大小是否合理（不应超过二维码的1/4）
        int maxLogoSize = Math.min(qrWidth, qrHeight) / 4;
        int logoSize = Math.min(logooptions.getLogoSize(), maxLogoSize);

        if (logoSize != logooptions.getLogoSize()) {
            log.warn("Logo大小已自动调整为: {}", logoSize);
        }

        BufferedImage result = new BufferedImage(qrWidth, qrHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = result.createGraphics();

        // 设置渲染质量
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

        // 绘制原始二维码
        g.drawImage(qrImage, 0, 0, null);

        // 计算logo位置（居中）
        int logoX = (qrWidth - logoSize) / 2;
        int logoY = (qrHeight - logoSize) / 2;

        // 设置透明度
        if (logooptions.getOpacity() < 1.0f) {
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, logooptions.getOpacity()));
        }

        // 绘制logo背景（白色边框）
        if (logooptions.getBorderWidth() > 0) {
            g.setColor(Color.WHITE);
            if (logooptions.isRoundCorner()) {
                g.fill(new RoundRectangle2D.Float(
                        logoX - logooptions.getBorderWidth(),
                        logoY - logooptions.getBorderWidth(),
                        logoSize + 2 * logooptions.getBorderWidth(),
                        logoSize + 2 * logooptions.getBorderWidth(),
                        10, 10
                ));
            } else {
                g.fillRect(
                        logoX - logooptions.getBorderWidth(),
                        logoY - logooptions.getBorderWidth(),
                        logoSize + 2 * logooptions.getBorderWidth(),
                        logoSize + 2 * logooptions.getBorderWidth()
                );
            }
        }

        // 缩放logo图片
        BufferedImage scaledLogo = scaleImage(logooptions.getLogoImage(), logoSize, logoSize);

        // 如果需要圆角，创建圆角剪切区域
        if (logooptions.isRoundCorner()) {
            Shape originalClip = g.getClip();
            g.setClip(new RoundRectangle2D.Float(logoX, logoY, logoSize, logoSize, 8, 8));
            g.drawImage(scaledLogo, logoX, logoY, null);
            g.setClip(originalClip);
        } else {
            g.drawImage(scaledLogo, logoX, logoY, null);
        }

        g.dispose();
        return result;
    }

    /**
     * 缩放图片
     */
    private BufferedImage scaleImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        if (originalImage.getWidth() == targetWidth && originalImage.getHeight() == targetHeight) {
            return originalImage;
        }

        BufferedImage scaledImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = scaledImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g.dispose();
        return scaledImage;
    }
}
