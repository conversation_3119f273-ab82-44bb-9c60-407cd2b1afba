package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class IdBuilderDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // code
        @NotBlank(message = "关键字不能为空")
        private String code;

        // 长度
        @NotNull(message = "长度不能为空")
        private Integer len;

        // 起始编号
        private Integer startNo;

        // 说明
        private String about;
    }
}
