package infra.integration.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import infra.integration.core.IntegrationService;
import infra.integration.exception.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 集成模块自动配置类
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(IntegrationConfigProperties.class)
public class IntegrationAutoConfiguration {

    /**
     * 创建集成配置服务Bean
     *
     * @param properties 集成配置属性
     * @return 集成配置服务实例
     */
    @Bean
    @ConditionalOnMissingBean
    public IntegrationService integrationConfigService(IntegrationConfigProperties properties) {
        // 创建数据源映射
        Map<String, DataSource> dataSources = createDataSources(properties);
        // 获取API配置映射
        Map<String, IntegrationConfigProperties.ApiConfig> apiConfigs = properties.getIntegration();

        log.info("[配置] 集成配置完成，共{}个集成配置，共{}个数据源",
                apiConfigs.size(), dataSources.size());
        return new IntegrationService(dataSources, apiConfigs);
    }

    /**
     * 创建数据源映射
     *
     * @param properties 集成配置属性
     * @return 数据源映射
     */
    private Map<String, DataSource> createDataSources(IntegrationConfigProperties properties) {
        Map<String, DataSource> dataSources = new HashMap<>();
        
        properties.getIntegration().forEach((name, apiConfig) -> {
            if (apiConfig.hasDatasource()) {
                try {
                    DataSource dataSource = createDataSource(name, apiConfig.getDatasource());
                    dataSources.put(name, dataSource);
                    log.info("[配置] 创建数据源: {}", name);
                } catch (Exception e) {
                    throw new IntegrationException("创建数据源失败: " + name, e);
                }
            } else {
                log.debug("API {} 没有配置数据源", name);
            }
        });
        
        return dataSources;
    }

    /**
     * 创建单个数据源
     * 使用HikariConfig，Spring Boot会自动处理所有配置绑定
     *
     * @param name 集成名称
     * @param hikariConfig HikariCP配置
     * @return 数据源实例
     */
    private DataSource createDataSource(String name, HikariConfig hikariConfig) {
        // 设置连接池名称（如果没有设置的话）
        if (hikariConfig.getPoolName() == null) {
            hikariConfig.setPoolName("IntegrationPool-" + name);
        }
        
        // 创建HikariDataSource，所有配置都已经在HikariConfig中
        HikariDataSource dataSource = new HikariDataSource(hikariConfig);
        
        log.debug("创建HikariCP数据源: {} (URL: {})", name, hikariConfig.getJdbcUrl());
        return dataSource;
    }
}
