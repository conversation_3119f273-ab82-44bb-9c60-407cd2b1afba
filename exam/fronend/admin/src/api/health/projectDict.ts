import { downloadFile, get, post, upload } from '~/utils/request'

const baseUrl = '/sys/projectDict'

/**
 * 检查项目字典列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 新增检查项目字典
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑检查项目字典(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑检查项目字典(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除检查项目字典
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 启用检查项目字典
 */
export function enable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/enable`, ids)
}

/**
 * 禁用检查项目字典
 */
export function disable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/disable`, ids)
}

/**
 * 导出检查项目字典
 */
export function exportData(params: any): Promise<any> {
  return downloadFile(`${baseUrl}/export`, { data: params })
}

/**
 * 导入检查项目字典
 */
export function importData(file: File): Promise<any> {
  return upload(`${baseUrl}/import`, file)
}