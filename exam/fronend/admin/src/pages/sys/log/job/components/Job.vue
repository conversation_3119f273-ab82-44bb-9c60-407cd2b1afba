<script setup lang="ts">
import * as api from '~/api/sys/job'
import { useDict } from '~/composables'
import { formatDateShortTime, tips } from '~/utils'

const emits = defineEmits<{
  showLog: [jobId: string]
}>()

// 状态及数据
const { KEYS, getName } = useDict()
const satusTypes = ['primary', 'success', 'info'] as const
const tableRef = useTemplateRef<any>('tableRef')

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const getStatusName = getName(KEYS.JOB_STATUS)

// 操作任务
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 启用任务
async function handleEnable(ids: number[]) {
  await handleBy(ids, api.enable)
}

// 禁用任务
async function handleDisable(ids: number[]) {
  await handleBy(ids, api.disable)
}

// 执行任务
async function handleRun(ids: number[]) {
  await handleBy(ids, api.run)
}

// 显示日志
function handleShowLog(jobId: string) {
  emits('showLog', jobId)
}

// 格式化单元格日期
function formatCellDate(row: any, column: any, cellValue: any) {
  return formatDateShortTime(cellValue)
}
</script>

<template>
  <table-list ref="tableRef" :fetch-api="api.list" :pager="false">
    <template #left-actions>
      <el-button-group>
        <el-button v-if="$hasPerm('sys:job:enable')" title="启用选中任务" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
          <template #icon>
            <icon icon="codicon:debug-start" />
          </template>
          启用
        </el-button>
        <el-popconfirm v-if="$hasPerm('sys:job:disable')" title="确认要禁用选中任务吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
          <template #reference>
            <el-button title="禁用选中任务" :disabled="!canAction">
              <template #icon>
                <icon icon="ant-design:stop-twotone" />
              </template>
              禁用
            </el-button>
          </template>
        </el-popconfirm>
        <el-popconfirm v-if="$hasPerm('sys:job:run')" title="确认要执行选中任务吗？" placement="bottom" @confirm="handleRun(tableRef.selected)">
          <template #reference>
            <el-button title="执行选中任务" :disabled="!canAction">
              <template #icon>
                <icon icon="ic:outline-start" />
              </template>
              执行
            </el-button>
          </template>
        </el-popconfirm>
      </el-button-group>
    </template>
    <el-table-column type="selection" align="center" width="50" />
    <el-table-column prop="jobId" label="任务标识" width="120" />
    <el-table-column prop="jobDesc" label="任务说明" show-overflow-tooltip />
    <el-table-column prop="cron" label="cron表达式" width="150" />
    <el-table-column prop="cronDesc" label="cron表达式说明" show-overflow-tooltip />
    <el-table-column prop="runLastTime" label="最后运行时间" align="center" width="150" :formatter="formatCellDate" />
    <el-table-column prop="nextRunTime" label="下次运行时间" align="center" width="150" :formatter="formatCellDate" />
    <el-table-column prop="runCount" label="运时次数" align="center" width="100" />
    <el-table-column prop="runSuccess" label="成功次数" align="center" width="100" />
    <el-table-column prop="status" label="状态" align="center" width="90">
      <template #default="{ row }">
        <el-tag :type="satusTypes[row.status]">
          {{ getStatusName(row.status) }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="200">
      <template #default="{ row }">
        <div class="space-x-2">
          <el-popconfirm v-if="$hasPerm('sys:job:run')" title="确认要执行该任务吗？" placement="left" @confirm="handleRun([row.id])">
            <template #reference>
              <el-button link>
                执行
              </el-button>
            </template>
          </el-popconfirm>
          <el-button v-if="$hasPerm('sys:job:enable') && row.status === 2" type="success" link @click="handleEnable([row.id])">
            启用
          </el-button>
          <el-popconfirm v-else-if="$hasPerm('sys:job:disable')" title="确认要禁用该任务吗？" placement="left" @confirm="handleDisable([row.id])">
            <template #reference>
              <el-button type="warning" link>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-button v-if="$hasPerm('sys:job:log')" type="info" link @click="handleShowLog(row.jobId)">
            日志
          </el-button>
        </div>
      </template>
    </el-table-column>
  </table-list>
</template>
