-- =============================================
-- 配置迁移参数
-- =============================================
SET @source_db = 'zrquestions';
SET @target_db = 'zr_question_test';
SET @table_config = CONCAT(
    'sys_config:id',
    ',sys_user_dept:userId|deptId'       -- 复合主键（|分隔）
);
SET @log = '';  -- 用于存储日志信息

-- =============================================
-- 迁移执行逻辑
-- =============================================
DROP PROCEDURE IF EXISTS temp_migrate_tables;

DELIMITER //
CREATE PROCEDURE `temp_migrate_tables`()
BEGIN
    -- 声明变量
    DECLARE total_tables INT;
    DECLARE current_table INT;
    DECLARE config_item VARCHAR(255);
    DECLARE table_name VARCHAR(64);
    DECLARE id_columns VARCHAR(255);  -- 存储主键（单主键或复合主键，|分隔）
    DECLARE is_composite INT;  -- 标记是否为复合主键（1=是，0=否）
    DECLARE max_id BIGINT;
    DECLARE err_msg VARCHAR(255);
    DECLARE source_count INT;
    DECLARE target_count INT;
    
    -- 错误处理器
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1
            err_msg = MESSAGE_TEXT;
        SET @error_occurred = 1;
        SET @error_msg = CONCAT('SQL错误：', err_msg);
        SET @log = CONCAT(@log, '\n[错误] ', @error_msg);
    END;

    -- 初始化变量
    SET total_tables = 1 + (LENGTH(@table_config) - LENGTH(REPLACE(@table_config, ',', '')));
    SET current_table = 1;
    SET @error_occurred = 0;
    SET @error_msg = '';
    SET @log = CONCAT('=== 开始迁移，共需处理 ', total_tables, ' 个表 ===');

    -- 开始事务
    START TRANSACTION;
    SET FOREIGN_KEY_CHECKS = 0;

    -- 循环处理所有表
    table_loop: LOOP
        IF current_table > total_tables OR @error_occurred = 1 THEN
            LEAVE table_loop;
        END IF;

        -- 提取表配置（表名:主键1|主键2 格式）
        SET config_item = SUBSTRING_INDEX(SUBSTRING_INDEX(@table_config, ',', current_table), ',', -1);
        SET table_name = SUBSTRING_INDEX(config_item, ':', 1);
        SET id_columns = SUBSTRING_INDEX(config_item, ':', -1);  -- 可能是单主键（无|）或复合主键（有|）
        
        -- 判断是否为复合主键（主键包含 | 即为复合）
        SET is_composite = IF(LENGTH(id_columns) - LENGTH(REPLACE(id_columns, '|', '')) >= 1, 1, 0);

        -- 追加日志（显示主键类型）
        SET @log = CONCAT(
            @log, 
            '\n[', NOW(), '] 开始处理表：', table_name, 
            '（主键：', id_columns, '，', 
            IF(is_composite=1, '复合主键', '单主键'), '）'
        );

        -- 检查源表记录数
        SET @sql_str = CONCAT(
            'SELECT COUNT(*) INTO @source_count FROM `', @source_db, '`.`', table_name, '`'
        );
        PREPARE stmt_count FROM @sql_str;
        EXECUTE stmt_count;
        DEALLOCATE PREPARE stmt_count;
        SET source_count = @source_count;
        SET @log = CONCAT(@log, '\n[', NOW(), '] 源表记录数：', source_count);

        -- 源表无数据则跳过
        IF source_count = 0 THEN
            SET @log = CONCAT(@log, '\n[', NOW(), '] 源表无数据，跳过处理');
            SET current_table = current_table + 1;
            ITERATE table_loop;
        END IF;

        -- 1. 仅单主键需要：临时禁用自增属性
        IF is_composite = 0 THEN
            SET @sql_str = CONCAT(
                'ALTER TABLE `', @target_db, '`.`', table_name, '` ',
                'MODIFY COLUMN `', id_columns, '` BIGINT NOT NULL'  -- 单主键字段
            );
            PREPARE stmt_alter1 FROM @sql_str;
            EXECUTE stmt_alter1;
            DEALLOCATE PREPARE stmt_alter1;
            SET @log = CONCAT(@log, '\n[', NOW(), '] 已禁用自增属性（单主键处理）');
        ELSE
            SET @log = CONCAT(@log, '\n[', NOW(), '] 复合主键，无需禁用自增属性');
        END IF;
        
        IF @error_occurred = 1 THEN
            SET @log = CONCAT(@log, '\n[', NOW(), '] 处理表 ', table_name, ' 失败：修改自增属性时出错');
            LEAVE table_loop;
        END IF;

        -- 2. 清空目标表（单/复合主键通用）
        SET @sql_str = CONCAT('DELETE FROM `', @target_db, '`.`', table_name, '`');
        PREPARE stmt_delete FROM @sql_str;
        EXECUTE stmt_delete;
        DEALLOCATE PREPARE stmt_delete;
        SET @log = CONCAT(@log, '\n[', NOW(), '] 目标表已清空');
        
        IF @error_occurred = 1 THEN
            SET @log = CONCAT(@log, '\n[', NOW(), '] 处理表 ', table_name, ' 失败：清空数据时出错');
            LEAVE table_loop;
        END IF;

        -- 3. 导入数据（单/复合主键通用）
        SET @sql_str = CONCAT(
            'INSERT INTO `', @target_db, '`.`', table_name, '` ',
            'SELECT * FROM `', @source_db, '`.`', table_name, '`'
        );
        PREPARE stmt_insert FROM @sql_str;
        EXECUTE stmt_insert;
        DEALLOCATE PREPARE stmt_insert;
        SET @log = CONCAT(@log, '\n[', NOW(), '] 数据导入完成');
        
        IF @error_occurred = 1 THEN
            SET @log = CONCAT(@log, '\n[', NOW(), '] 处理表 ', table_name, ' 失败：导入数据时出错');
            LEAVE table_loop;
        END IF;

        -- 检查目标表记录数（单/复合主键通用）
        SET @sql_str = CONCAT(
            'SELECT COUNT(*) INTO @target_count FROM `', @target_db, '`.`', table_name, '`'
        );
        PREPARE stmt_target_count FROM @sql_str;
        EXECUTE stmt_target_count;
        DEALLOCATE PREPARE stmt_target_count;
        SET target_count = @target_count;
        SET @log = CONCAT(@log, '\n[', NOW(), '] 目标表记录数：', target_count);
        
        IF target_count = 0 THEN
            SET @error_occurred = 1;
            SET @log = CONCAT(@log, '\n[', NOW(), '] 处理表 ', table_name, ' 失败：导入后记录数为0');
            LEAVE table_loop;
        END IF;
        
        IF target_count != source_count THEN
            SET @log = CONCAT(@log, '\n[警告] 源表与目标表记录数不一致（源：', source_count, '，目标：', target_count, '）');
        END IF;

        -- 4. 仅单主键需要：恢复自增属性并重置起始值
        IF is_composite = 0 THEN
            -- 恢复自增属性
            SET @sql_str = CONCAT(
                'ALTER TABLE `', @target_db, '`.`', table_name, '` ',
                'MODIFY COLUMN `', id_columns, '` BIGINT NOT NULL AUTO_INCREMENT'  -- 单主键字段
            );
            PREPARE stmt_alter2 FROM @sql_str;
            EXECUTE stmt_alter2;
            DEALLOCATE PREPARE stmt_alter2;
            SET @log = CONCAT(@log, '\n[', NOW(), '] 已恢复自增属性');
            
            -- 重置自增起始值
            SET @sql_str = CONCAT(
                'SELECT MAX(`', id_columns, '`) INTO @max_id FROM `', @target_db, '`.`', table_name, '`'  -- 单主键字段
            );
            PREPARE stmt_maxid FROM @sql_str;
            EXECUTE stmt_maxid;
            DEALLOCATE PREPARE stmt_maxid;

            SET @sql_str = CONCAT(
                'ALTER TABLE `', @target_db, '`.`', table_name, '` ',
                'AUTO_INCREMENT = ', IFNULL(@max_id, 0) + 1
            );
            PREPARE stmt_alter3 FROM @sql_str;
            EXECUTE stmt_alter3;
            DEALLOCATE PREPARE stmt_alter3;
            SET @log = CONCAT(@log, '\n[', NOW(), '] 自增起始值已重置');
        ELSE
            SET @log = CONCAT(@log, '\n[', NOW(), '] 复合主键，无需恢复自增属性');
        END IF;

        SET @log = CONCAT(@log, '\n[', NOW(), '] 表 ', table_name, ' 处理完成\n');
        SET current_table = current_table + 1;
    END LOOP table_loop;

    -- 恢复外键检查
    SET FOREIGN_KEY_CHECKS = 1;

    -- 事务处理 & 最终日志
    IF @error_occurred = 0 THEN
        COMMIT;
        SET @log = CONCAT(@log, '=== 所有表迁移成功 ===');
    ELSE
        ROLLBACK;
        SET @log = CONCAT(@log, '\n=== 迁移失败，所有操作已回滚 ===');
    END IF;

    -- 输出完整日志
    SELECT @log AS 迁移日志;
END //
DELIMITER ;

-- 执行迁移
CALL temp_migrate_tables();

-- 清理存储过程
DROP PROCEDURE IF EXISTS temp_migrate_tables;