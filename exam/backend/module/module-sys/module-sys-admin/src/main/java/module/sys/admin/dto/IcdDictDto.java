package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;
import module.sys.model.SexLimit;

/**
 * ICD编码Dto
 */
public class IcdDictDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 疾病名称
        @NotBlank(message = "疾病名称不能为空")
        private String name;
        // ICD编码
        private String icdNo;
        // 是否传染病 0：否 1：是
        private Boolean isContagion;
        // 是否慢性病 0：否 1：是
        private Boolean isChronic;
        // 性病
        private SexLimit sex;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
    }    
}