<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/>
    </parent>

    <groupId>top</groupId>
    <artifactId>depends</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>统一依赖管理，也方便依赖库升级检查</description>

    <properties>
        <revision>0.0.1</revision>

        <!-- 开发及编译及环境 -->
        <java.version>24</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <lombok.version>1.18.38</lombok.version>
        <slf4j.version>2.0.9</slf4j.version>

        <!-- Spring -->
        <spring-ai.version>1.0.0</spring-ai.version>

        <!-- Web -->
        <jsonwebtoken.version>0.13.0</jsonwebtoken.version>

        <!-- 数据库 -->
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mybatis-plus-join.version>1.5.3</mybatis-plus-join.version>

        <!-- 缓存/Redis/消息队列 -->
        <caffeine.version>3.2.2</caffeine.version>
        <redisson.version>3.50.0</redisson.version>

        <!-- 加密库 -->
        <bouncycastle.version>1.81</bouncycastle.version>

        <!-- 流程规则引擎 -->
        <QLExpress.version>3.3.4</QLExpress.version>
        <flowable.version>7.1.0</flowable.version>

        <!-- 工具 -->
        <tinypinyin.version>3.0.0</tinypinyin.version>
        <fastexcel.version>1.2.0</fastexcel.version>
        <springdoc-openapi.version>2.8.9</springdoc-openapi.version>
        <apache.compress.version>1.28.0</apache.compress.version>
        <zip4j.version>2.11.5</zip4j.version>
        <!-- 二维码/条码 -->
        <zxing.version>3.5.3</zxing.version>
        <!-- PDF -->
        <openhtmltopdf.version>1.1.28</openhtmltopdf.version>
        <!-- HTML-PDF模板解析依赖，注意此版本需与thymeleaf引用版本一致 -->
        <ognl.version>3.3.5</ognl.version>

        <!-- 第三方sdk -->
        <minio.version>8.5.17</minio.version>
        <aliyun.sms.version>4.1.2</aliyun.sms.version>
        <tencent.sms.version>3.1.1281</tencent.sms.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
            </dependency>

            <!-- Web -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jsonwebtoken.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jsonwebtoken.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jsonwebtoken.version}</version>
            </dependency>

            <!-- 数据库 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>

            <!-- 缓存/Redis/消息队列 -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- 加密 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <!-- 流程规则引擎 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>${QLExpress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- 工具 -->
            <dependency>
                <groupId>com.github.promeg</groupId>
                <artifactId>tinypinyin</artifactId>
                <version>${tinypinyin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.promeg</groupId>
                <artifactId>tinypinyin-lexicons-java-cncity</artifactId>
                <version>${tinypinyin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-common</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>net.lingala.zip4j</groupId>
                <artifactId>zip4j</artifactId>
                <version>${zip4j.version}</version>
            </dependency>
            <!-- 二维码/条码 -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <!-- PDF -->
            <dependency>
                <groupId>io.github.openhtmltopdf</groupId>
                <artifactId>openhtmltopdf-core</artifactId>
                <version>${openhtmltopdf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openhtmltopdf</groupId>
                <artifactId>openhtmltopdf-pdfbox</artifactId>
                <version>${openhtmltopdf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openhtmltopdf</groupId>
                <artifactId>openhtmltopdf-svg-support</artifactId>
                <version>${openhtmltopdf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openhtmltopdf</groupId>
                <artifactId>openhtmltopdf-mathml-support</artifactId>
                <version>${openhtmltopdf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.openhtmltopdf</groupId>
                <artifactId>openhtmltopdf-slf4j</artifactId>
                <version>${openhtmltopdf.version}</version>
            </dependency>
            <dependency>
                <groupId>ognl</groupId>
                <artifactId>ognl</artifactId>
                <version>${ognl.version}</version>
            </dependency>

            <!-- 第三方 SDK -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sms</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.18.0</version>
            </plugin>
        </plugins>
    </build>

    <!-- 自定义源 -->
    <repositories>
        <repository>
            <id>ali</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>spring</id>
            <url>https://repo.spring.io/milestone</url>
        </repository>
    </repositories>

</project>