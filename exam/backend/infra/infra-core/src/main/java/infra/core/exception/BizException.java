package infra.core.exception;

import lombok.Getter;

/**
 * 业务异常
 * <p>
 * 用于封装业务逻辑异常，包含错误码和错误消息。
 * 此异常为运行时异常，通常用于可预见的业务错误场景。
 */
@Getter
public class BizException extends RuntimeException {
    /**
     * 错误码
     */
    private final int code;

    /**
     * 使用错误码和消息创建业务异常
     *
     * @param code 错误码
     * @param message  错误消息
     */
    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 使用错误码、消息和原始异常创建业务异常
     *
     * @param code  错误码
     * @param message   错误消息
     * @param cause 原始异常
     */
    public BizException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    /**
     * 仅使用消息创建业务异常
     * <p>
     * 默认使用-1作为错误码
     *
     * @param message 错误消息
     */
    public BizException(String message) {
        this(-1, message);
    }

    /**
     * 使用消息和原始异常创建业务异常
     * <p>
     * 默认使用-1作为错误码
     *
     * @param message   错误消息
     * @param cause 原始异常
     */
    public BizException(String message, Throwable cause) {
        this(-1, message, cause);
    }
}
