package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.FeeProjectDto;
import module.sys.entity.FeeProject;
import module.sys.service.FeeProjectService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "收费项目")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/feeProject")
public class FeeProjectController {
    private final FeeProjectService feeProjectService;

    @Operation(summary = "收费项目列表")
    @GetMapping("/list")
    @Perm("health:feeProject")
    public Result<PageResult<FeeProject>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = feeProjectService.getPage(w -> applyQueryCondition(w, type, key), pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增收费项目")
    @PostMapping("/add")
    @AuditLog(code = "health:feeProject:add", value = "新增收费项目")
    @Perm("health:feeProject:add")
    public Result<Long> add(@RequestBody @Valid FeeProjectDto.EditDto dto) {
        FeeProject feeProject = FeeProject.builder()
                .feeNo(dto.getFeeNo())
                .countryNo(dto.getCountryNo())
                .projectName(dto.getProjectName())
                .standardAmount(dto.getStandardAmount())
                .feeAmount(dto.getFeeAmount())
                .invoiceAmount(dto.getInvoiceAmount())
                .approvalNumber(dto.getApprovalNumber())
                .unit(dto.getUnit())
                .spec(dto.getSpec())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        if (!feeProjectService.add(feeProject))
            return Result.FAIL_LONG;
        return Result.okData(feeProject.getId());
    }

    @Operation(summary = "编辑收费项目")
    @GetMapping("/edit")
    @Perm("health:feeProject:edit")
    public Result<FeeProjectDto.EditDto> getEdit(Long id) {
        var entity = feeProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return entity.map(item -> Result.okData(
                ObjectUtil.copyTo(item, new FeeProjectDto.EditDto())))
                .orElseGet(() -> Result.fail("收费项目不存在"));
    }

    @Operation(summary = "编辑收费项目")
    @PostMapping("/edit")
    @AuditLog(code = "health:feeProject:edit", value = "编辑收费项目")
    @Perm("health:feeProject:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid FeeProjectDto.EditDto dto) {
        var entity = feeProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("收费项目不存在");
        }

        FeeProject feeProject = entity.get();
        feeProject.setFeeNo(dto.getFeeNo());
        feeProject.setCountryNo(dto.getCountryNo());
        feeProject.setProjectName(dto.getProjectName());
        feeProject.setStandardAmount(dto.getStandardAmount());
        feeProject.setFeeAmount(dto.getFeeAmount());
        feeProject.setInvoiceAmount(dto.getInvoiceAmount());
        feeProject.setApprovalNumber(dto.getApprovalNumber());
        feeProject.setUnit(dto.getUnit());
        feeProject.setSpec(dto.getSpec());
        feeProject.setSort(dto.getSort());
        feeProject.setDisabled(dto.getDisabled());

        if (!feeProjectService.update(feeProject))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除收费项目")
    @PostMapping("/delete")
    @AuditLog(code = "health:feeProject:delete", value = "删除收费项目")
    @Perm("health:feeProject:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            feeProjectService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "批量启用收费项目")
    @PostMapping("/enable")
    @AuditLog(code = "health:feeProject:enable", value = "批量启用收费项目")
    @Perm("health:feeProject:enable")
    public Result<Void> enable(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            feeProjectService.updateBatch(wrapper ->
                    wrapper.in("id", ids)
                            .eq("disabled", true)
                            .set("disabled", false));
        }
        return Result.OK;
    }

    @Operation(summary = "批量禁用收费项目")
    @PostMapping("/disable")
    @AuditLog(code = "health:feeProject:disable", value = "批量禁用收费项目")
    @Perm("health:feeProject:disable")
    public Result<Void> disable(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            feeProjectService.updateBatch(wrapper ->
                    wrapper.in("id", ids)
                            .eq("disabled", false)
                            .set("disabled", true));
        }
        return Result.OK;
    }

    @Operation(summary = "导出收费项目")
    @PostMapping("/export")
    @AuditLog(code = "health:feeProject:export", value = "导出收费项目")
    @Perm("health:feeProject:export")
    public void export(
            String type,
            String key,
            HttpServletResponse response) throws IOException {

        ExcelExporter.<FeeProject>create()
                .sheet("收费项目")
                .columns(cols ->
                        cols.add("编号", FeeProject::getFeeNo)
                                .add("国家编码", FeeProject::getCountryNo)
                                .add("项目名称", FeeProject::getProjectName)
                                .add("标准金额", FeeProject::getStandardAmount)
                                .add("收费金额", FeeProject::getFeeAmount)
                                .add("发票金额", FeeProject::getInvoiceAmount)
                                .add("批准文号", FeeProject::getApprovalNumber)
                                .add("单位", FeeProject::getUnit)
                                .add("规格", FeeProject::getSpec)
                                .add("排序值", FeeProject::getSort)
                                .add("状态", project -> project.getDisabled() ? "禁用" : "启用")
                )
                .fromService(feeProjectService, w -> applyQueryCondition(w, type, key), FilterOptions.DISABLE_ALL_FILTER)
                .export(response, "收费项目");
    }

    @Operation(summary = "导入收费项目")
    @PostMapping("/import")
    @AuditLog(code = "health:feeProject:import", value = "导入收费项目")
    @Perm("health:feeProject:import")
    @Transactional
    public Result<Void> importData(@RequestParam("files") MultipartFile file) {
        try {
            ImportResult<FeeProject> parseResult = ExcelImporter.create(FeeProject::new)
                    .columns(cols -> {
                        cols.required("编号", ExcelParser.string(), FeeProject::setFeeNo);
                        cols.add("国家编码", ExcelParser.string(), FeeProject::setCountryNo);
                        cols.required("项目名称", ExcelParser.string(), FeeProject::setProjectName);
                        cols.add("标准金额", ExcelParser.decimal(), FeeProject::setStandardAmount);
                        cols.add("收费金额", ExcelParser.decimal(), FeeProject::setFeeAmount);
                        cols.add("发票金额", ExcelParser.decimal(), FeeProject::setInvoiceAmount);
                        cols.add("批准文号", ExcelParser.string(), FeeProject::setApprovalNumber);
                        cols.add("单位", ExcelParser.string(), FeeProject::setUnit);
                        cols.add("规格", ExcelParser.string(), FeeProject::setSpec);
                        cols.withDefault("排序值", ExcelParser.integer(), FeeProject::setSort, 0);
                        cols.withDefault("状态", "禁用"::equals, FeeProject::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            feeProjectService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "获取费用项目字典")
    @GetMapping("/dict")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<Long>>> getDict() {
        return Result.okData(feeProjectService.getList().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getId(), w.getProjectName()))
                .toList());
    }

    /**
     * 条件处理
     */
    private QueryWrapper<FeeProject> applyQueryCondition(QueryWrapper<FeeProject> wrapper, String type, String key) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.like("project_name", keyTrim).or()
                        .like("py", keyTrim);
            }
            wrapper.like("feeNo".equals(type), "fee_no", keyTrim);
            wrapper.like("countryNo".equals(type), "country_no", keyTrim);
            wrapper.like("approvalNumber".equals(type), "approval_number", keyTrim);
        }
        return wrapper;
    }
}