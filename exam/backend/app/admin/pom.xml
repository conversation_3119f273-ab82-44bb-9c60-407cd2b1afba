<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>app</groupId>
        <artifactId>app</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>admin</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>basic</groupId>
            <artifactId>basic-configurer</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>module</groupId>
            <artifactId>module-sys-admin</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>module</groupId>
            <artifactId>module-member-admin</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>module</groupId>
            <artifactId>module-health-admin</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-report</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-sse</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>
</project>