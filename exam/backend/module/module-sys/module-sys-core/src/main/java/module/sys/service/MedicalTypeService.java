package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Chinese;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Medical;
import module.sys.entity.MedicalType;
import module.sys.mapper.MedicalTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * 病史类别服务
 */
@Slf4j
@Service
public class MedicalTypeService extends ServiceBase<MedicalTypeMapper, MedicalType> {
    @Autowired
    private MedicalService medicalService;

    @Override
    protected Function<QueryWrapper<MedicalType>, QueryWrapper<MedicalType>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<MedicalType>, QueryWrapper<MedicalType>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(MedicalType entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);
        setPinyin(entity);
    }

    /**
     * 获取指定分类下所有病史
     */
    public List<Medical> getValues(Long typeId) {
        return medicalService.getListByType(typeId);
    }

    /**
     * 设置拼音
     */
    private void setPinyin(MedicalType value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
