package infra.sms.config;

import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.sms.core.ISmsMessage;
import infra.sms.core.ISmsSender;
import infra.sms.core.ISmsService;
import infra.sms.impl.AliyunSmsSender;
import infra.sms.impl.LogSmsSender;
import infra.sms.impl.TencentSmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;

/**
 * 短信自动配置
 */
@AutoConfiguration
@EnableConfigurationProperties(SmsConfigProperties.class)
@ConditionalOnProperty(prefix = "app.sms", name = "provider")
@Slf4j
public class SmsAutoConfiguration {
    /**
     * 根据配置创建短信服务实例
     */
    @Bean
    @ConditionalOnMissingBean(ISmsSender.class)
    public ISmsSender infraSmsProvider(SmsConfigProperties config) {
        log.info("[配置] 配置sms提供程序 - {}", config.getProvider());
        return createSmsProvider(config);
    }

    /**
     * 创建短信实例
     */
    private ISmsSender createSmsProvider(SmsConfigProperties config) {
        if (Str.isEmpty(config.getProvider())) {
            throw new ConfigException("未配置app.sms.provider参数");
        }

        return switch (config.getProvider().toLowerCase()) {
            case "aliyun" -> new AliyunSmsSender(config.getAliyun());
            case "tencent" -> new TencentSmsSender(config.getTencent());
            case "log" -> new LogSmsSender();
            default -> throw new IllegalArgumentException("暂未支持的SMS提供实现: " + config.getProvider());
        };
    }

    /**
     * 任务注册监听，在启动后自动注册所有短信
     */
    @Bean
    @ConditionalOnBean({ISmsSender.class, ISmsService.class})
    public ApplicationListener<ApplicationReadyEvent> infraSmsRegistrationListener(
            ApplicationContext applicationContext,
            ISmsService smsService) {
        return event -> {
            try {
                // 获取所有ISmsMessage类型的Bean
                var messageBeans = applicationContext.getBeansOfType(ISmsMessage.class);
                if (!messageBeans.isEmpty()) {
                    var messageList = messageBeans.values().stream().toList();
                    smsService.register(messageList);
                    log.info("[配置] 发现并注册 {} 条短信模板", messageList.size());
                }
            } catch (Exception e) {
                log.error("[配置] 自动注册短信内容失败", e);
            }
        };
    }
}
