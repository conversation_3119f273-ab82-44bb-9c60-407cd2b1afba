package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.SignDictDto;
import module.sys.entity.SignDict;
import module.sys.service.SignDictService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "体征词字典")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/signDict")
public class SignDictController {
    private final SignDictService signDictService;

    @Operation(summary = "体征词字典列表")
    @GetMapping("/list")
    @Perm("health:signDict")
    public Result<PageResult<SignDict>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = signDictService.getPage(w -> applyQueryCondition(w, type, key), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增体征词字典")
    @PostMapping("/add")
    @AuditLog(code = "health:signDict:add", value = "体征词字典-新增")
    @Perm("health:signDict:add")
    public Result<Long> add(@RequestBody @Valid SignDictDto.EditDto dto) {
        SignDict entity = SignDict.builder()
                .name(dto.getName())
                .info(dto.getInfo())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return signDictService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑体征词字典")
    @GetMapping("/edit")
    @Perm("health:signDict:edit")
    public Result<SignDictDto.EditDto> getEdit(Long id) {
        var entityOpt = signDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new SignDictDto.EditDto())
        );
    }

    @Operation(summary = "编辑体征词字典")
    @PostMapping("/edit")
    @AuditLog(code = "health:signDict:edit", value = "体征词字典-编辑")
    @Perm("health:signDict:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid SignDictDto.EditDto dto) {
        var entityOpt = signDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("体征词字典不存在");
        }

        SignDict entity = entityOpt.get();
        entity.setName(dto.getName());
        entity.setInfo(dto.getInfo());
        entity.setSort(dto.getSort());
        entity.setDisabled(dto.getDisabled());

        return signDictService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除体征词字典")
    @PostMapping("/delete")
    @AuditLog(code = "health:signDict:delete", value = "体征词字典-删除")
    @Perm("health:signDict:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            signDictService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "导出体征词字典")
    @PostMapping("/export")
    @AuditLog(code = "health:signDict:export", value = "体征词字典-导出")
    @Perm("health:signDict:export")
    public void export(String type,
                       String key,
                       HttpServletResponse response) throws IOException {

        ExcelExporter.<SignDict>create()
                .sheet("体征词字典")
                .columns(cols ->
                        cols.add("体征名称", SignDict::getName)
                                .add("体征描述", SignDict::getInfo)
                                .add("排序", SignDict::getSort)
                                .add("状态", entity -> entity.getDisabled() ? "禁用" : "启用")
                )
                .fromService(signDictService, w -> applyQueryCondition(w, type, key), FilterOptions.DISABLE_ALL_FILTER)
                .export(response, "体征词字典");
    }

    @Operation(summary = "导入体征词字典")
    @PostMapping("/import")
    @AuditLog(code = "health:signDict:import", value = "体征词字典-导入")
    @Perm("health:signDict:import")
    @Transactional
    public Result<Void> importData(@RequestParam("files") MultipartFile file) {
        try {
            ImportResult<SignDict> parseResult = ExcelImporter.create(SignDict::new)
                    .columns(cols -> {
                        cols.required("体征名称", ExcelParser.string(), SignDict::setName);
                        cols.add("体征描述", ExcelParser.string(), SignDict::setInfo);
                        cols.withDefault("排序", ExcelParser.integer(), SignDict::setSort, 0);
                        cols.withDefault("状态", "禁用"::equals, SignDict::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            signDictService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:signDict:enable", value = "体征词字典-启用")
    @Perm("health:signDict:enable")
    public Result<Void> enableSignDict(@RequestBody List<Long> ids) {
        signDictService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:signDict:disable", value = "体征词字典-禁用")
    @Perm("health:signDict:disable")
    public Result<Void> disableSignDict(@RequestBody List<Long> ids) {
        signDictService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    /**
     * 条件处理
     */
    private QueryWrapper<SignDict> applyQueryCondition(QueryWrapper<SignDict> wrapper, String type, String key) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.and(w -> w.like("name", keyTrim).or().like("py", keyTrim));
            }
            wrapper.like("info".equals(type), "info", keyTrim);
        }
        return wrapper;
    }
}
