<script setup lang="ts">
import type { TreeInstance } from 'element-plus'
import perms from '~/composables/perms'

interface Props {
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
})

// 状态
const buildData = computed(() => buildPermTree(perms))
const treeData = computed(() => buildData.value.tree)
const readonlyTreeData = computed(() => buildReadonlyTree(buildData.value.tree))
const allKeys = computed(() => buildData.value.keys)
const checkStrictly = ref(false)

const treeRef = useTemplateRef<TreeInstance>('treeRef')
const filterText = ref('')

// 构建权限树数据
function buildPermTree(data: any, parentKey = ''): { tree: any[], keys: string[] } {
  const result: any[] = []
  const keys: string[] = []

  Object.keys(data).forEach((key) => {
    if (key.startsWith('_'))
      return

    const currentKey = parentKey ? `${parentKey}:${key}` : key
    const item = data[key]
    const node = {
      id: currentKey,
      label: item._name || key,
      key: currentKey,
    } as any

    // 收集当前节点的key
    keys.push(currentKey)

    // 递归处理子权限
    const childResult = buildPermTree(item, currentKey)
    if (childResult.tree.length > 0) {
      node.children = childResult.tree
      // 合并子节点的keys
      keys.push(...childResult.keys)
    }

    result.push(node)
  })

  return { tree: result, keys }
}

// 构建只读树数据
function buildReadonlyTree(data: any) {
  const result = JSON.parse(JSON.stringify(data))
  const setDisabled = (node: any) => {
    node.disabled = true
    if (node.children && node.children.length > 0) {
      node.children.forEach((item: any) => setDisabled(item))
    }
  }
  result.forEach((item: any) => setDisabled(item))
  return result
}

// 全选所有节点
function handleSelectAll() {
  treeRef.value?.setCheckedKeys(allKeys.value)
}

// 反选所有节点
function handleUnselectAll() {
  const checked = treeRef.value!.getCheckedKeys()
  treeRef.value?.setCheckedKeys(allKeys.value.filter(key => !checked.includes(key)))
}

// 树过滤
watch(filterText, (newValue) => {
  treeRef.value?.filter(newValue)
})
function filterNode(value: string, data: any) {
  if (!value)
    return true
  return data.label.includes(value)
    || data.key.toLowerCase().includes(value.toLowerCase())
}

function setCheckedKeys(keys: string[]) {
  treeRef.value?.setCheckedKeys(keys)
}

function getCheckedKeys() {
  return treeRef.value?.getCheckedKeys()
}

defineExpose({
  setCheckedKeys,
  getCheckedKeys,
})
</script>

<template>
  <div>
    <!-- 工具栏 -->
    <div v-if="!props.readonly" class="mb-4 flex items-center justify-between">
      <div class="flex space-x-2">
        <el-button-group>
          <el-button title="全选" @click="handleSelectAll">
            全选
          </el-button>
          <el-button title="反选" @click="handleUnselectAll">
            反选
          </el-button>
        </el-button-group>
        <el-radio-group v-model="checkStrictly" size="default">
          <el-radio-button label="父子联动" :value="false" />
          <el-radio-button label="父子独立" :value="true" />
        </el-radio-group>
      </div>
      <el-input v-model="filterText" class="w-44" placeholder="输入关键词过滤" clearable>
        <template #prefix>
          <icon icon="carbon:search" />
        </template>
      </el-input>
    </div>
    <el-scrollbar class="h-80">
      <el-tree
        ref="treeRef" class="tree" :filter-node-method="filterNode" :data="props.readonly ? readonlyTreeData : treeData" node-key="key" show-checkbox :check-strictly="checkStrictly"
      >
        <template #default="{ node }">
          <div class="w-full flex justify-between">
            <div>{{ node.label }}</div>
          </div>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">
.tree {
  :deep(.el-tree-node__content) {
    padding: 2px;
    border-radius: 4px;
  }
  :deep(.el-checkbox.is-disabled) {
    .el-checkbox__input {
      &.is-checked .el-checkbox__inner {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        &::after {
          border-color: var(--el-color-white);
        }
      }

      &.is-indeterminate .el-checkbox__inner {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);

        &::before {
          background-color: var(--el-color-white);
        }
      }
    }
  }
}
</style>
