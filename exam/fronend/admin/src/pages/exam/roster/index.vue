<script setup lang="ts">
definePage({
  meta: {
    title: '医师排班',
    menu: true,
    order: 5,
    icon: 'fluent:people-queue-20-regular',
  },
})
</script>

<template>
<p>1. 排班规则配置：支持按检查科室（如内科、检验科、放射科）、检查项目类型（如空腹项目、影像学检查）设置排班周期（日 / 周 / 月），关联科室承载能力（如日均接诊量、设备数量）；</p>
<p>2. 医师信息关联：导入医师基础信息（姓名、职称、擅长项目），标记医师可排班时段（如工作日上午、周末全天），支持按擅长项目分配专属检查科室；</p>
<p>3. 排班计划生成：根据检前预约量（含个检 / 团检）、科室负荷预警，自动生成初步排班表，提示超负荷时段（如某科室某时段预约量超承载），支持手动调整医师班次（如增加支援医师）；</p>
<p>4. 排班状态跟踪：实时展示医师当前状态（待接诊 / 接诊中 / 已下班），支持按日期、科室查询排班记录；若医师临时调班，系统同步更新排班表并通知相关科室，避免影响检中流程；</p>
<p>5. 与检中流程协同：医师登录系统后，自动加载当日排班客户列表，关联客户体检项目与检查结果录入权限，未排班医师无法进入对应检查科室操作界面，确保流程规范</p>

前台导检 “预约资源动态调配（系统内置预约负荷预警机制，超承载时推送预警，支持调整排班）”；检查科室 “操作流程标准化约束（按标准化流程管控操作，与排班协同保障效率）”
</template>
