package infra.cache.core;

/**
 * 锁接口
 */
public interface ILock extends AutoCloseable {
    /**
     * 尝试获取锁
     *
     * @param timeoutSeconds 获取锁的超时时间（秒），小于等于0表示不带超时
     * @return true表示获取成功，false表示获取失败
     */
    boolean tryLock(long timeoutSeconds);

    /**
     * 释放锁
     */
    void unlock();

    /**
     * 自动释放锁（AutoCloseable实现）
     */
    @Override
    default void close() {
        unlock();
    }
}
