package infra.core.common;

import com.fasterxml.jackson.annotation.JsonValue;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 字典枚举接口
 */
public interface IDictEnum {
    /**
     * 获取枚举值
     */
    @JsonValue
    int getValue();

    /**
     * 获取枚举描述
     */
    String getName();

    /**
     * 获取枚举项列表
     */
    static List<EnumDictItem<Integer>> getItems(Class<? extends Enum<? extends IDictEnum>> enumClass) {
        return Arrays.stream(enumClass.getEnumConstants())
                .map(item -> {
                    IDictEnum dictEnum = (IDictEnum) item;
                    return new EnumDictItem<>(dictEnum.getValue(), dictEnum.getName());
                })
                .toList();
    }

    /**
     * 根据名称获取枚举实例
     *
     * @param enumClass 枚举类
     * @param name      枚举名称
     * @return 对应的枚举实例
     */
    static <T extends Enum<T> & IDictEnum> T fromName(Class<T> enumClass, String name) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(item -> item.getName().equals(name))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        "枚举" + enumClass.getSimpleName() + "中不存在名称为[" + name + "]的枚举项"));
    }

    /**
     * 根据枚举值获取枚举实例
     *
     * @param enumClass 枚举类
     * @param value     枚举值
     * @return 对应的枚举实例
     */
    static <T extends Enum<T> & IDictEnum> T fromValue(Class<T> enumClass, int value) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(item -> item.getValue() == value)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        "枚举" + enumClass.getSimpleName() + "中不存在值为[" + value + "]的枚举项"));
    }

    /**
     * 枚举项
     */
    record EnumDictItem<T>(T id, String name) implements Serializable {
    }
}
