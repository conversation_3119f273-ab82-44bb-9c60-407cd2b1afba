package infra.protect.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 保护模块配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.protect")
public class ProtectConfigProperties {
    // 是否启用保护模块
    private boolean enabled = true;
    // 限流配置
    private RateLimitConfig rateLimit = new RateLimitConfig();

    /**
     * 限流配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateLimitConfig {
        // 限流器类型：local, redis
        private String provider = "local";
        // 默认限流策略
        private RateLimitStrategyConfig defaultStrategy = new RateLimitStrategyConfig();

        /**
         * 限流策略
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RateLimitStrategyConfig {
            /**
             * 默认限流次数
             */
            private int count = 300;

            /**
             * 默认时间窗口(秒)
             */
            private int window = 60;

            /**
             * 提示消息
             */
            private String message = "请求过于频繁，请稍后再试";
        }
    }
}