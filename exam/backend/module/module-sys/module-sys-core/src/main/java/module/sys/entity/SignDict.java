package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 体征词字典
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_sign_dict")
public class SignDict extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 体征名称
    private String name;

    // 拼音
    private String py;

    // 体征描述
    private String info;

    // 排序值
    @Builder.Default
    private Integer sort = 0;

    // 禁用
    private Boolean disabled;
}