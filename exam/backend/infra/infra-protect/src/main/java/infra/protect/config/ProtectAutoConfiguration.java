package infra.protect.config;

import infra.protect.core.IRateLimiter;
import infra.protect.core.RateLimitAspect;
import infra.protect.core.impl.LocalRateLimiter;
import infra.protect.core.impl.RedisRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 保护模块自动配置
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(ProtectConfigProperties.class)
@ConditionalOnProperty(prefix = "app.protect", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableAspectJAutoProxy
public class ProtectAutoConfiguration {
    /**
     * 本地限流器
     */
    @Bean
    @ConditionalOnProperty(prefix = "app.protect.rate-limit", name = "provider", havingValue = "local", matchIfMissing = true)
    @ConditionalOnMissingBean(IRateLimiter.class)
    public IRateLimiter infraLocalRateLimiter() {
        log.info("[配置] 启用本地内存限流器");
        return new LocalRateLimiter();
    }

    /**
     * Redis限流器
     */
    @Bean
    @ConditionalOnProperty(prefix = "app.protect.rate-limit", name = "provider", havingValue = "redis")
    @ConditionalOnMissingBean(IRateLimiter.class)
    public IRateLimiter infraRedisRateLimiter(RedissonClient redissonClient) {
        log.info("[配置] 启用Redis限流器");
        return new RedisRateLimiter(redissonClient);
    }

    /**
     * 限流切面
     */
    @Bean
    @ConditionalOnBean(IRateLimiter.class)
    public RateLimitAspect infraRateLimitAspect(IRateLimiter rateLimiter, ProtectConfigProperties properties) {
        log.info("[配置] 限流器切入");
        return new RateLimitAspect(rateLimiter, properties.getRateLimit().getDefaultStrategy());
    }
}
