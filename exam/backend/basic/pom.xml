<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>top</groupId>
        <artifactId>project</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>basic</groupId>
    <artifactId>basic</artifactId>
    <description>提供一些所有项目都可能要用到的通用模板项</description>
    <packaging>pom</packaging>

    <modules>
        <module>basic-configurer</module>
    </modules>

    <dependencies>

    </dependencies>
</project>