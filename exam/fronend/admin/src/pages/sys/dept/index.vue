<script setup lang="ts">
import * as deptApi from '~/api/sys/dept'
import * as postApi from '~/api/sys/post'
import { useDict, useLoading } from '~/composables'
import { tips } from '~/utils'

definePage({
  meta: {
    title: '科室管理',
    perm: 'sys:dept',
    menu: true,
    order: 1,
    icon: 'hugeicons:structure-01',
  },
})

type OperType = 'dept' | 'post' // 操作类型
const { KEYS, removeDict } = useDict()

// 状态数据
const treeData = ref<any[]>([])
const filterText = ref('')
const deptFormRef = useTemplateRef('deptFormRef')
const postFormRef = useTemplateRef('postFormRef')

const currentDept = ref<any>(null)
const postDialogVisible = ref(false)
const postList = ref([])
const postSelected = ref([])

const { loading, withLoading } = useLoading()
const { loading: postLoading, withLoading: postWithLoading } = useLoading()

const postCanAction = computed(() => postSelected.value.length > 0 && !postLoading.value)

// 表单数据
const initDeptData = {
  name: '',
  type: 0,
  code: '',
  parentId: null,
  leaderId: null,
  leaderName: '',
  contact: '',
  addressDictId: null,
  sort: 0,
  disabled: false,
}
const initPostData = {
  name: '',
  code: '',
  deptId: null,
  sort: 0,
  disabled: false,
}
const deptRules = {
  name: [
    { required: true, message: '科室名称不能为空' },
  ],
}
const postRules = {
  name: [
    { required: true, message: '岗位名称不能为空' },
  ],
  deptId: [
    { required: true, message: '归属科室不能为空' },
  ],
}

// 加载树
async function loadTree() {
  await withLoading(async () => {
    const res = await deptApi.getTree()
    treeData.value = res.data

    removeDict(KEYS.DEPT_LIST)
  })
}

// 加载岗位
async function loadPostList() {
  await postWithLoading(async () => {
    const res = await postApi.list({ deptId: currentDept.value.id })
    postList.value = res.data
  })
}

// 过滤树
function filterTree(nodes: any[], keyword: string): any[] {
  if (!keyword)
    return nodes

  keyword = keyword.trim().toLowerCase()

  const result: any[] = []
  for (const node of nodes) {
    let children: any[] = []
    if (node.children)
      children = filterTree(node.children, keyword)

    if (node.name.toLowerCase().includes(keyword) || (node.data && (
      (node.data.py && node.data.py.toLowerCase().includes(keyword))
      || (node.data.code && node.data.code.toLowerCase().includes(keyword))
      || (node.data.py && node.data.py.toLowerCase().includes(keyword))
      || (node.data.leaderName && node.data.leaderName.toLowerCase().includes(keyword))
      || (node.data.contact && node.data.contact.toLowerCase().includes(keyword))
    )) || children.length > 0) {
      result.push({ ...node, children })
    }
  }
  return result
}

const filteredTreeData = computed(() => filterTree(treeData.value, filterText.value))

// 新增科室/岗位
function handleAdd(type: OperType, parentId?: number) {
  if (type === 'dept')
    deptFormRef.value?.add({ parentId })
  else if (type === 'post')
    postFormRef.value?.add({ deptId: parentId })
}

// 编辑科室/岗位
async function handleEdit(type: OperType, data: any) {
  if (type === 'dept')
    deptFormRef.value?.edit(data.id)
  else if (type === 'post')
    postFormRef.value?.edit(data.id)
}

// 删除科室
async function handleDelete(ids: number[]) {
  await withLoading(async () => {
    await deptApi.remove(ids)
    tips.success()
    await loadTree()
  })
}

// 显示岗位列表
function visiblePostList(data: any) {
  currentDept.value = data
  loadPostList()
  postDialogVisible.value = true
}

function handlePostSelectionChange(val: any) {
  postSelected.value = val.map((item: any) => item.id)
}

// 删除岗位
async function handleDeletePost(ids: number[]) {
  await postWithLoading(async () => {
    await postApi.remove(ids)
    tips.success()
    await loadPostList()
  })
}

// 是否为岗位数据
function isPost(data: any) {
  return data.key.startsWith('p')
}

// 初始化
onMounted(() => {
  loadTree()
})
</script>

<template>
  <div class="p-4">
    <div class="mt-2 flex flex-col">
      <!-- 查询表单 -->
      <div class="mb-1 items-center">
        <el-form :inline="true">
          <el-form-item class="w-60 mr-2!">
            <el-input v-model="filterText" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="filterText = ''">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="flex flex-col justify-between sm:flex-row space-y-3 sm:space-y-0">
        <!-- 左侧操作区 -->
        <div class="flex items-center space-x-2">
          <el-dropdown v-if="$hasPerm(['sys:dept:addDept', 'sys:dept:addPost'])" type="primary" split-button @click="handleAdd('dept')">
            新增
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="$hasPerm('sys:dept:addDept')" @click="handleAdd('dept')">
                  新增科室
                </el-dropdown-item>
                <el-dropdown-item v-if="$hasPerm('sys:dept:addPost')" @click="handleAdd('post')">
                  新增岗位
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <!-- 右侧操作区 -->
        <div class="flex items-center justify-end space-x-3">
          <el-button-group class="flex">
            <el-button round :loading="loading" @click="loadTree">
              <template #icon>
                <icon icon="mdi:refresh" />
              </template>
              刷新
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 树形表格 -->
    <div v-loading="loading" class="mt-2">
      <el-table class="table-list" size="large" :data="filteredTreeData" row-key="key" default-expand-all :tree-props="{ children: 'children' }">
        <el-table-column prop="name" label="名称" show-overflow-tooltip>
          <template #default="{ row }">
            <icon v-if="row.data.type === 1" icon="ph:buildings" title="公司" />
            <icon v-else-if="isPost(row)" icon="mdi:account-tie" title="岗位" />
            <icon v-else icon="fluent-mdl2:org" title="科室" />
            <span class="ml-1">{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" width="150">
          <template #default="{ row }">
            <el-tag v-if="isPost(row)" type="info">
              岗位
            </el-tag>
            <el-tag v-else-if="row.data.type === 1" type="danger">
              公司
            </el-tag>
            <el-tag v-else type="success">
              科室
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="data.code" label="编码" align="center" width="180" />
        <el-table-column prop="data.leaderName" label="负责人" width="180" show-overflow-tooltip />
        <el-table-column prop="sort" label="排序值" align="center" width="100" />
        <el-table-column label="状态" align="center" width="120">
          <template #default="{ row }">
            <el-tag :type="row.data.disabled ? 'danger' : 'success'">
              {{ row.data.disabled ? '禁用' : '启用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="270">
          <template #default="{ row }">
            <div class="space-x-2">
              <el-button v-if="$hasPerm(isPost(row) ? 'sys:dept:editPost' : 'sys:dept:editDept')" link @click="handleEdit(isPost(row) ? 'post' : 'dept', row)">
                编辑
              </el-button>
              <template v-if="!isPost(row)">
                <el-button v-if="$hasPerm('sys:dept:addDept')" link @click="handleAdd('dept', row.id)">
                  新增子科室
                </el-button>
                <el-button v-if="$hasPerm('sys:dept:addPost') || $hasPerm('sys:dept:editPost')" link @click="visiblePostList(row)">
                  科室岗位
                </el-button>
              </template>
              <el-popconfirm
                v-if="$hasPerm(isPost(row) ? 'sys:dept:deletePost' : 'sys:dept:deleteDept')"
                title="确认要删除该项吗？"
                placement="left"
                @confirm="handleDelete([row.id])"
              >
                <template #reference>
                  <el-button type="warning" link>
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <!-- 科室对话框 -->
  <detail-form
    ref="deptFormRef"
    class="p-6" title="科室" :init-data="initDeptData" :rules="deptRules" :get-api="deptApi.getEdit"
    width="700px" :add-api="deptApi.add" :edit-api="deptApi.postEdit" @success="loadTree"
  >
    <el-row :gutter="10">
      <el-col :span="14">
        <el-form-item label="名称" prop="name">
          <el-input v-model="deptFormRef.formData.name" placeholder="请输入名称" maxlength="63" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="编号" prop="code">
          <el-input v-model="deptFormRef.formData.code" placeholder="请输入编号" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="10">
        <el-form-item label="类型" prop="type">
          <el-select v-model="deptFormRef.formData.type" placeholder="请选择类型">
            <el-option label="科室" :value="0" />
            <el-option label="公司" :value="1" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="14">
        <el-form-item label="上级组织" prop="parentId">
          <dept-select
            v-model="deptFormRef.formData.parentId" v-model:name="deptFormRef.formData.parentName"
            :data="treeData"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="10">
        <el-form-item label="负责人" prop="leaderId">
          <user-select
            v-model="deptFormRef.formData.leaderId" v-model:name="deptFormRef.formData.leaderName"
            placeholder="请选择负责人"
          />
        </el-form-item>
      </el-col>
      <el-col :span="14">
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="deptFormRef.formData.contact" placeholder="请输入联系方式" maxlength="63" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="所在地址" prop="addressDictId">
          <dict-select v-model="deptFormRef.formData.addressDictId" :dict-key="KEYS.DICT.ADDRESS" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="10">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="deptFormRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="14">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="deptFormRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>

  <!-- 岗位列表对话框 -->
  <el-dialog v-model="postDialogVisible" :title="`${currentDept?.name}岗位列表`" width="900px" draggable>
    <div class="px-2 py-5">
      <div class="mb-4 flex items-center justify-between">
        <div>
          <el-button v-if="$hasPerm('sys:dept:addPost')" type="primary" @click="handleAdd('post', currentDept.id)">
            <template #icon>
              <icon icon="ep:plus" />
            </template>
            新增岗位
          </el-button>
          <el-popconfirm
            v-if="$hasPerm('sys:dept:deletePost')" title="确认要删除选中项吗？"
            @confirm="handleDeletePost(postSelected)"
          >
            <template #reference>
              <el-button type="danger" :disabled="!postCanAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除选中
              </el-button>
            </template>
          </el-popconfirm>
        </div>
      </div>
      <el-table v-loading="postLoading" :data="postList" @selection-change="handlePostSelectionChange">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="code" label="编码" width="180" />
        <el-table-column prop="sort" label="排序" width="100" align="center" />
        <el-table-column prop="disabled" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.disabled ? 'danger' : 'success'">
              {{ row.disabled ? '禁用' : '启用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button v-if="$hasPerm('sys:dept:editPost')" link @click="handleEdit('post', row)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('sys:dept:deletePost')" title="确认要删除该项吗？"
              @confirm="handleDeletePost([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button @click="postDialogVisible = false">
        关闭
      </el-button>
    </template>
  </el-dialog>

  <!-- 岗位编辑对话框 -->
  <detail-form
    ref="postFormRef"
    class="p-6" title="岗位" :init-data="initPostData" :rules="postRules" :get-api="postApi.getEdit"
    width="700px" :add-api="postApi.add" :edit-api="postApi.postEdit" @success="loadPostList"
  >
    <el-row :gutter="10">
      <el-col :span="14">
        <el-form-item label="岗位名称" prop="name">
          <el-input v-model="postFormRef.formData.name" placeholder="请输入岗位名称" maxlength="15" />
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="岗位编号" prop="code">
          <el-input v-model="postFormRef.formData.code" placeholder="请输入岗位编号" maxlength="15" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="所属组织" prop="deptId">
          <dept-select v-model="postFormRef.formData.deptId" :data="treeData" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="14">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="postFormRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="postFormRef.formData.sort" :min="0" />
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
