<script setup lang="ts">
import ChangePassword from '~/components/auth/ChangePassword.vue'
import Base from './components/Base.vue'
import Log from './components/Log.vue'
import Sign from './components/Sign.vue'

type Tab = 'base' | 'pwd' | 'sign' | 'log'

definePage({
  meta: {
    title: ' 个人中心',
  },
})

const route = useRoute()
const activeTab = ref<Tab>('base')

// 通过路由参数，设置默认选项卡
function handleTabFromRoute() {
  const tab = route.query.tab as string
  if (tab)
    activeTab.value = tab as Tab
}

onMounted(() => {
  handleTabFromRoute()
})
onActivated(() => {
  handleTabFromRoute()
})
</script>

<template>
  <div class="p-4">
    <el-tabs v-model="activeTab">
      <el-tab-pane name="base" label="基础信息" lazy>
        <Base />
      </el-tab-pane>
      <el-tab-pane name="pwd" label="修改密码" lazy>
        <ChangePassword />
      </el-tab-pane>
      <el-tab-pane name="sign" label="电子签名" lazy>
        <Sign />
      </el-tab-pane>
      <el-tab-pane name="log" label="登录日志" class="p-0" lazy>
        <Log />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
