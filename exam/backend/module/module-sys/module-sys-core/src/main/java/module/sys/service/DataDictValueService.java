package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.DataDictValue;
import module.sys.mapper.DataDictValueMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * 数据字典值服务
 */
@Slf4j
@Service
public class DataDictValueService extends ServiceBase<DataDictValueMapper, DataDictValue> {
    @Override
    protected Function<QueryWrapper<DataDictValue>, QueryWrapper<DataDictValue>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<DataDictValue>, QueryWrapper<DataDictValue>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(DataDictValue entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (entity.getDictId() == null) {
            throw new BizException("归属字典不能为空");
        }

        if (existsByCode(entity.getDictId(), entity.getCode(),
                isUpdate ? entity.getId() : null)) {
            throw new BizException("字典编码已存在");
        }

        setPinyin(entity);
    }

    /**
     * 根据字典ID获取所有字典值
     */
    public List<DataDictValue> getListByDict(Long dictId) {
        return getListByDict(dictId, FilterOptions.DEFAULTS);
    }

    /**
     * 根据字典ID获取所有字典值
     */
    public List<DataDictValue> getListByDict(Long dictId, FilterOptions filterOptions) {
        return getList(w -> w.eq("dict_id", dictId), filterOptions);
    }

    /**
     * 检查字典值编码是否已存在
     */
    public boolean existsByCode(Long dictId, String code, Long excludeId) {
        if (Str.isEmpty(code) || dictId == null) {
            return false;
        }

        return exists(w -> {
            w.eq("dict_id", dictId)
                    .eq("code", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 设置拼音
     */
    private void setPinyin(DataDictValue value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
