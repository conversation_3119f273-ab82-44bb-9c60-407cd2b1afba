package infra.core.image;

import org.springframework.lang.NonNull;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.image.BufferedImage;
import java.awt.geom.Rectangle2D;
import java.awt.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * 处理工具类，链式API调用方式
 * <p>
 * 实现了 {@link AutoCloseable} 接口，推荐在 try-with-resources 结构中使用以确保资源被正确释放
 * <p>
 * 示例:
 * <pre>{@code
 * try (ImageProcessor processor = ImageProcessor.from(file)) {
 *     byte[] thumbnailBytes = processor.thumbnailByWidth(800)
 *                                      .watermark("CONFIDENTIAL", WatermarkPosition.CENTER, 0.5f)
 *                                      .toBytes();
 * }
 * }</pre>
 */
public final class ImageProcessor implements AutoCloseable {

    private final BufferedImage image;
    private final String format;

    private static final Font WATERMARK_FONT;

    static {
        WATERMARK_FONT = findAvailableFont();
    }

    /**
     * 缩略图缩放模式
     */
    public enum ScaleMode {
        /**
         * 裁剪模式保持图片比例，从中心裁剪以适应目标尺寸，无黑边
         */
        CROP,
        /**
         * 填充模式将图片拉伸至目标尺寸，可能导致变形
         */
        FILL,
        /**
         * 保持比例模式等比缩放图片，空白部分用白色填充
         */
        KEEP_RATIO
    }

    /**
     * 水印位置
     */
    public enum WatermarkPosition {
        TOP_LEFT, TOP_RIGHT, BOTTOM_LEFT, BOTTOM_RIGHT, CENTER
    }

    private ImageProcessor(BufferedImage image, String format) {
        this.image = Objects.requireNonNull(image, "图片不能为空");
        this.format = Objects.requireNonNull(format, "格式不能为空");
    }

    /**
     * 从 MultipartFile 创建处理器
     *
     * @param file 上传的文件
     * @return ImageProcessor 实例
     * @throws IOException 如果文件为空、过大或无法读取
     */
    public static ImageProcessor from(@NonNull MultipartFile file) throws IOException {
        Objects.requireNonNull(file, "输入文件不能为空");
        if (file.isEmpty()) {
            throw new IOException("输入文件为空");
        }

        // 限制文件大小为50MB
        long maxSize = 50 * 1024 * 1024;
        if (file.getSize() > maxSize) {
            throw new IOException("文件大小超出50MB限制");
        }

        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                throw new IOException("无法读取图片文件，文件可能已损坏或格式不支持");
            }
            String format = getImageFormat(file.getOriginalFilename());
            return new ImageProcessor(image, format);
        }
    }

    /**
     * 从 BufferedImage 创建处理器
     *
     * @param image  BufferedImage 对象
     * @param format 图片格式 (e.g., "JPEG", "PNG")
     * @return ImageProcessor 实例
     */
    public static ImageProcessor from(@NonNull BufferedImage image, @NonNull String format) {
        return new ImageProcessor(image, format);
    }

    /**
     * 按指定宽度创建缩略图，高度按比例自动计算
     *
     * @param width 目标宽度（像素）
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnailByWidth(int width) {
        return thumbnail(width, 0, ScaleMode.CROP);
    }

    /**
     * 按指定高度创建缩略图，宽度按比例自动计算
     *
     * @param height 目标高度（像素）
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnailByHeight(int height) {
        return thumbnail(0, height, ScaleMode.CROP);
    }

    /**
     * 创建缩略图默认使用裁剪模式 {@link ScaleMode#CROP}
     *
     * @param width  目标宽度（像素）
     * @param height 目标高度（像素）
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnail(int width, int height) {
        return thumbnail(width, height, ScaleMode.CROP);
    }

    /**
     * 创建缩略图默认使用裁剪模式 {@link ScaleMode#CROP}, 允许使用 null 参数.
     *
     * @param width  目标宽度（像素）, null 或 0 表示不指定
     * @param height 目标高度（像素）, null 或 0 表示不指定
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnail(Integer width, Integer height) {
        return thumbnail(width, height, ScaleMode.CROP);
    }

    /**
     * 根据指定模式创建缩略图, 允许使用 null 参数.
     *
     * @param width  目标宽度（像素）, null 或 0 表示不指定
     * @param height 目标高度（像素）, null 或 0 表示不指定
     * @param mode   缩放模式
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnail(Integer width, Integer height, @NonNull ScaleMode mode) {
        int w = (width != null) ? width : 0;
        int h = (height != null) ? height : 0;

        if (w <= 0 && h <= 0) {
            return this;
        }

        return thumbnail(w, h, mode);
    }

    /**
     * 根据指定模式创建缩略图
     *
     * @param width  目标宽度如果小于等于0，将根据高度和原图比例自动计算
     * @param height 目标高度如果小于等于0，将根据宽度和原图比例自动计算
     * @param mode   缩放模式
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor thumbnail(int width, int height, @NonNull ScaleMode mode) {
        if (width <= 0 && height <= 0) {
            throw new IllegalArgumentException("宽度和高度不能同时为零或负数");
        }
        if (width > 10000 || height > 10000) {
            throw new IllegalArgumentException("宽度和高度不能超过10000像素");
        }

        final int targetWidth = (width <= 0) ? (int) Math.round((double) height * image.getWidth() / image.getHeight()) : width;
        final int targetHeight = (height <= 0) ? (int) Math.round((double) width * image.getHeight() / image.getWidth()) : height;

        if (targetWidth == image.getWidth() && targetHeight == image.getHeight()) {
            return this;
        }

        BufferedImage resultImage = switch (mode) {
            case CROP -> createCropThumbnail(targetWidth, targetHeight);
            case FILL -> createFillThumbnail(targetWidth, targetHeight);
            case KEEP_RATIO -> createKeepRatioThumbnail(targetWidth, targetHeight);
        };

        return new ImageProcessor(resultImage, this.format);
    }

    /**
     * 添加文字水印
     *
     * @param text     水印文字
     * @param position 水印位置
     * @param opacity  不透明度 (0.0f - 1.0f)
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor watermark(@NonNull String text, @NonNull WatermarkPosition position, float opacity) {
        if (text.isBlank()) {
            return this;
        }

        BufferedImage result = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createHighQualityGraphics(result);
        try {
            g2d.drawImage(image, 0, 0, null);

            int fontSize = Math.max(image.getWidth() / 25, 14);
            Font font = WATERMARK_FONT.deriveFont(Font.BOLD, (float) fontSize);
            g2d.setFont(font);
            g2d.setColor(new Color(1f, 1f, 1f, clamp(opacity)));

            FontMetrics fm = g2d.getFontMetrics();
            Rectangle2D textBounds = fm.getStringBounds(text, g2d);

            Point pos = calculateWatermarkPosition(image.getWidth(), image.getHeight(),
                    (int) textBounds.getWidth(), (int) textBounds.getHeight(), position);

            g2d.drawString(text, pos.x, pos.y + fm.getAscent());
        } finally {
            g2d.dispose();
        }

        return new ImageProcessor(result, this.format);
    }

    /**
     * 添加图片水印
     *
     * @param watermarkImage 水印图片
     * @param position       水印位置
     * @param opacity        不透明度 (0.0f - 1.0f)
     * @return 新的 ImageProcessor 实例
     */
    public ImageProcessor watermark(@NonNull BufferedImage watermarkImage, @NonNull WatermarkPosition position, float opacity) {
        Objects.requireNonNull(watermarkImage, "水印图片不能为空");

        BufferedImage result = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createHighQualityGraphics(result);
        try {
            g2d.drawImage(image, 0, 0, null);
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, clamp(opacity)));

            Point pos = calculateWatermarkPosition(image.getWidth(), image.getHeight(),
                    watermarkImage.getWidth(), watermarkImage.getHeight(), position);
            g2d.drawImage(watermarkImage, pos.x, pos.y, null);
        } finally {
            g2d.dispose();
        }

        return new ImageProcessor(result, this.format);
    }

    /**
     * 将处理后的图片转换为字节数组
     *
     * @return 图片字节数组
     * @throws IOException 转换失败
     */
    public byte[] toBytes() throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            if ("JPEG".equalsIgnoreCase(format)) {
                // 确保图片具有正确的颜色空间用于JPEG编码
                BufferedImage rgbImage = ensureRGBImage(image);

                ImageWriter writer = ImageIO.getImageWritersByFormatName("jpeg").next();
                try {
                    ImageWriteParam param = writer.getDefaultWriteParam();
                    if (param.canWriteCompressed()) {
                        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                        // 调整压缩质量以平衡质量和文件大小
                        param.setCompressionQuality(0.85f);
                    }
                    try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
                        writer.setOutput(ios);
                        writer.write(null, new IIOImage(rgbImage, null, null), param);
                    }
                } finally {
                    writer.dispose();
                }
            } else {
                ImageIO.write(image, format, baos);
            }
            return baos.toByteArray();
        }
    }

    /**
     * 将处理后的图片转换为 MultipartFile
     *
     * @param filename 文件名
     * @return MultipartFile 实例
     * @throws IOException 转换失败
     */
    public MultipartFile toMultipartFile(String filename) throws IOException {
        return new ProcessedImageFile(toBytes(), filename, "image/" + format.toLowerCase());
    }

    /**
     * 获取当前图片信息
     *
     * @return ImageInfo 记录
     * @throws IOException 获取字节失败
     */
    public ImageInfo getInfo() throws IOException {
        return new ImageInfo(image.getWidth(), image.getHeight(), format, toBytes().length);
    }

    @Override
    public void close() {
        image.flush();
    }

    // --- 私有实现 ---

    /**
     * 确保图片具有正确的RGB颜色空间用于JPEG编码
     *
     * @param sourceImage 源图片
     * @return RGB格式的图片
     */
    private BufferedImage ensureRGBImage(BufferedImage sourceImage) {
        // 如果已经是TYPE_INT_RGB格式，直接返回
        if (sourceImage.getType() == BufferedImage.TYPE_INT_RGB) {
            return sourceImage;
        }

        // 创建新的RGB图片
        BufferedImage rgbImage = new BufferedImage(
                sourceImage.getWidth(),
                sourceImage.getHeight(),
                BufferedImage.TYPE_INT_RGB
        );

        Graphics2D g2d = rgbImage.createGraphics();
        try {
            // 设置白色背景（对于透明图片很重要）
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, sourceImage.getWidth(), sourceImage.getHeight());
            // 绘制原始图片
            g2d.drawImage(sourceImage, 0, 0, null);
        } finally {
            g2d.dispose();
        }

        return rgbImage;
    }

    private BufferedImage createFillThumbnail(int width, int height) {
        BufferedImage thumbnail = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createHighQualityGraphics(thumbnail);
        try {
            g2d.drawImage(image, 0, 0, width, height, null);
        } finally {
            g2d.dispose();
        }
        return thumbnail;
    }

    private BufferedImage createKeepRatioThumbnail(int width, int height) {
        double sourceRatio = (double) image.getWidth() / image.getHeight();
        double targetRatio = (double) width / height;

        int newWidth = width;
        int newHeight = height;

        if (sourceRatio > targetRatio) {
            newHeight = (int) (width / sourceRatio);
        } else {
            newWidth = (int) (height * sourceRatio);
        }

        BufferedImage thumbnail = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createHighQualityGraphics(thumbnail);
        try {
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);
            int x = (width - newWidth) / 2;
            int y = (height - newHeight) / 2;
            g2d.drawImage(image, x, y, newWidth, newHeight, null);
        } finally {
            g2d.dispose();
        }
        return thumbnail;
    }

    private BufferedImage createCropThumbnail(int width, int height) {
        BufferedImage thumbnail = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = createHighQualityGraphics(thumbnail);
        try {
            double sourceRatio = (double) image.getWidth() / image.getHeight();
            double targetRatio = (double) width / height;

            int sx1 = 0, sy1 = 0, sx2 = image.getWidth(), sy2 = image.getHeight();

            if (sourceRatio > targetRatio) { // 源图更宽，需裁剪左右
                int newSourceWidth = (int) (image.getHeight() * targetRatio);
                sx1 = (image.getWidth() - newSourceWidth) / 2;
                sx2 = sx1 + newSourceWidth;
            } else if (sourceRatio < targetRatio) { // 源图更高，需裁剪上下
                int newSourceHeight = (int) (image.getWidth() / targetRatio);
                sy1 = (image.getHeight() - newSourceHeight) / 2;
                sy2 = sy1 + newSourceHeight;
            }
            // 如果比例相同，则无需裁剪

            g2d.drawImage(image, 0, 0, width, height, sx1, sy1, sx2, sy2, null);
        } finally {
            g2d.dispose();
        }
        return thumbnail;
    }

    private Graphics2D createHighQualityGraphics(BufferedImage image) {
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        return g2d;
    }

    private Point calculateWatermarkPosition(int imageWidth, int imageHeight, int markWidth, int markHeight, WatermarkPosition position) {
        int margin = Math.max(imageWidth / 50, 10);
        return switch (position) {
            case TOP_LEFT -> new Point(margin, margin);
            case TOP_RIGHT -> new Point(imageWidth - markWidth - margin, margin);
            case BOTTOM_LEFT -> new Point(margin, imageHeight - markHeight - margin);
            case BOTTOM_RIGHT -> new Point(imageWidth - markWidth - margin, imageHeight - markHeight - margin);
            case CENTER -> new Point((imageWidth - markWidth) / 2, (imageHeight - markHeight) / 2);
        };
    }

    private static Font findAvailableFont() {
        String[] preferredFonts = {"PingFang SC", "Microsoft YaHei", "Noto Sans CJK SC", "SimHei", "Arial Unicode MS", Font.SANS_SERIF};
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] availableFonts = ge.getAvailableFontFamilyNames();

        for (String fontName : preferredFonts) {
            for (String availableFont : availableFonts) {
                if (availableFont.equalsIgnoreCase(fontName)) {
                    return new Font(fontName, Font.PLAIN, 12);
                }
            }
        }
        return new Font(Font.SANS_SERIF, Font.PLAIN, 12); // Fallback
    }

    private static String getImageFormat(String filename) {
        if (filename != null && filename.contains(".")) {
            String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
            return switch (extension) {
                case "png" -> "PNG";
                case "gif" -> "GIF";
                case "bmp" -> "BMP";
                default -> "JPEG";
            };
        }
        return "JPEG";
    }

    private static float clamp(float value) {
        return Math.max(0.0f, Math.min(1.0f, value));
    }

    /**
     * 图片信息记录
     */
    public record ImageInfo(int width, int height, String format, long sizeInBytes) {
    }

    /**
     * 一个内部实现的 MultipartFile，用于封装处理后的图片字节
     */
    private static class ProcessedImageFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String contentType;

        ProcessedImageFile(byte[] content, String originalName, String contentType) {
            this.content = content;
            this.name = originalName;
            this.contentType = contentType;
        }

        @Override
        @NonNull
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        @NonNull
        public byte[] getBytes() {
            return content;
        }

        @Override
        @NonNull
        public InputStream getInputStream() {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(@NonNull java.io.File dest) throws IOException {
            try (var fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}