import { get, post } from '~/utils/request'

const baseUrl = '/sys/examProject'

/**
 * 体检项目列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 新增体检项目
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑体检项目(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑体检项目(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除体检项目
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 启用体检项目
 */
export function enable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/enable`, ids)
}

/**
 * 禁用体检项目
 */
export function disable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/disable`, ids)
}

/**
 * 关联项目字典列表
 */
export function listDict(params: any): Promise<any> {
  return get<any>(`${baseUrl}/listDict`, params)
}

/**
 * 新增关联项目字典
 */
export function addDict(data: any): Promise<any> {
  return post<any>(`${baseUrl}/addDict`, data)
}

/**
 * 编辑关联项目字典(获取)
 */
export function getEditDict(id: number): Promise<any> {
  return get<any>(`${baseUrl}/editDict`, {
    id,
  })
}

/**
 * 编辑关联项目字典(提交)
 */
export function postEditDict(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/editDict`, data, {
    id,
  })
}

/**
 * 删除关联项目字典
 */
export function removeDict(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/deleteDict`, ids)
}

/**
 * 关联项目费用列表
 */
export function listFee(params: any): Promise<any> {
  return get<any>(`${baseUrl}/listFee`, params)
}

/**
 * 新增关联项目费用
 */
export function addFee(data: any): Promise<any> {
  return post<any>(`${baseUrl}/addFee`, data)
}

/**
 * 编辑关联项目费用(获取)
 */
export function getEditFee(id: number): Promise<any> {
  return get<any>(`${baseUrl}/editFee`, {
    id,
  })
}

/**
 * 编辑关联项目费用(提交)
 */
export function postEditFee(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/editFee`, data, {
    id,
  })
}

/**
 * 删除关联项目费用
 */
export function removeFee(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/deleteFee`, ids)
}
