package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.entity.IdEntity;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.ExamComboDto;
import module.sys.entity.*;
import module.sys.model.AgeLimit;
import module.sys.model.CustomerLimit;
import module.sys.model.SexLimit;
import module.sys.service.DataDictService;
import module.sys.service.ExamComboProjectService;
import module.sys.service.ExamComboService;
import module.sys.service.ExamProjectService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Tag(name = "体检套餐")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/examCombo")
public class ExamComboController {
    private static final String EXAM_TYPE_DICT_ID = "exam_type";

    private final DataDictService dataDictService;
    private final ExamComboService examComboService;
    private final ExamComboProjectService examComboProjectService;
    private final ExamProjectService examProjectService;

    @Operation(summary = "体检套餐列表")
    @GetMapping("/list")
    @Perm("health:examCombo")
    public Result<PageResult<ExamCombo>> list(
            String type,
            String key,
            Long examType,
            Boolean disabled,
            PageParam pageParam) {

        var result = examComboService.getPage(w -> applyQueryCondition(w, type, key, examType, disabled), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增体检套餐")
    @PostMapping("/add")
    @AuditLog(code = "health:examCombo:add", value = "体检套餐-新增")
    @Perm("health:examCombo:add")
    public Result<Long> add(@RequestBody @Valid ExamComboDto.EditDto dto) {
        ExamCombo examCombo = ExamCombo.builder()
                .code(dto.getCode())
                .name(dto.getName())
                .price(dto.getPrice())
                .examType(dto.getExamType())
                .sex(dto.getSex())
                .age(dto.getAge())
                .customer(dto.getCustomer())
                .sort(dto.getSort())
                .remark(dto.getRemark())
                .disabled(dto.getDisabled())
                .build();

        if (!examComboService.add(examCombo))
            return Result.FAIL_LONG;
        return Result.okData(examCombo.getId());
    }

    @Operation(summary = "编辑体检套餐")
    @GetMapping("/edit")
    @Perm("health:examCombo:edit")
    public Result<ExamComboDto.EditDto> getEdit(Long id) {
        var entity = examComboService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return entity.map(item -> Result.okData(
                        ObjectUtil.copyTo(item, new ExamComboDto.EditDto())))
                .orElseGet(() -> Result.fail("体检套餐不存在"));
    }

    @Operation(summary = "编辑体检套餐")
    @PostMapping("/edit")
    @AuditLog(code = "health:examCombo:edit", value = "体检套餐-编辑")
    @Perm("health:examCombo:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid ExamComboDto.EditDto dto) {
        var entity = examComboService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("体检套餐不存在");
        }

        ExamCombo examCombo = entity.get();
        examCombo.setCode(dto.getCode());
        examCombo.setName(dto.getName());
        examCombo.setPrice(dto.getPrice());
        examCombo.setExamType(dto.getExamType());
        examCombo.setSex(dto.getSex());
        examCombo.setAge(dto.getAge());
        examCombo.setCustomer(dto.getCustomer());
        examCombo.setSort(dto.getSort());
        examCombo.setRemark(dto.getRemark());
        examCombo.setDisabled(dto.getDisabled());

        if (!examComboService.update(examCombo))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除体检套餐")
    @PostMapping("/delete")
    @AuditLog(code = "health:examCombo:delete", value = "体检套餐-删除")
    @Perm("health:examCombo:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            examComboService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:examCombo:enable", value = "体检套餐-启用")
    @Perm("health:examCombo:enable")
    public Result<Void> enable(@RequestBody List<Long> ids) {
        examComboService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:examCombo:disable", value = "体检套餐-禁用")
    @Perm("health:examCombo:disable")
    public Result<Void> disable(@RequestBody List<Long> ids) {
        examComboService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "导出体检套餐")
    @PostMapping("/export")
    @AuditLog(code = "health:examCombo:export", value = "体检套餐-导出")
    @Perm("health:examCombo:export")
    public void export(
            String type,
            String key,
            Long examType,
            Boolean disabled,
            HttpServletResponse response) throws IOException {

        var examTypes = dataDictService.getValuesByCode(EXAM_TYPE_DICT_ID);

        ExcelExporter.<ExamCombo>create()
                .sheet("体检套餐")
                .columns(cols ->
                        cols.add("套餐名称", ExamCombo::getName)
                                .add("套餐代码", ExamCombo::getCode)
                                .add("套餐价格", ExamCombo::getPrice)
                                .add("体检类型", value -> examTypes.stream()
                                        .filter(item -> item.getId().equals(value.getExamType()))
                                        .findFirst()
                                        .map(DataDictValue::getName)
                                        .orElse(""))
                                .add("性别", value -> value.getSex().getName())
                                .add("年龄阶段", value -> value.getAge().getName())
                                .add("适用客体", value -> value.getCustomer().getName())
                                .add("排序值", ExamCombo::getSort)
                                .add("备注", ExamCombo::getRemark)
                                .add("状态", item -> item.getDisabled() ? "禁用" : "启用")
                )
                .fromService(examComboService, w -> applyQueryCondition(w, type, key, examType, disabled), FilterOptions.DISABLE_ALL_FILTER)
                .export(response, "体检套餐");
    }

    @Operation(summary = "导入体检套餐")
    @PostMapping("/import")
    @AuditLog(code = "health:examCombo:import", value = "体检套餐-导入")
    @Perm("health:examCombo:import")
    @Transactional
    public Result<Void> importData(@RequestParam("files") MultipartFile file) {
        var examTypes = dataDictService.getValuesByCode(EXAM_TYPE_DICT_ID);

        try {
            ImportResult<ExamCombo> parseResult = ExcelImporter.create(ExamCombo::new)
                    .columns(cols -> {
                        cols.required("套餐名称", ExcelParser.string(), ExamCombo::setName);
                        cols.add("套餐代码", ExcelParser.string(), ExamCombo::setCode);
                        cols.required("套餐价格", ExcelParser.decimal(), ExamCombo::setPrice);
                        cols.add("体检类型", value ->
                                        examTypes.stream().filter(item -> item.getName().equals(String.valueOf(value)))
                                                .findFirst().map(IdEntity::getId)
                                                .orElse(null)
                                , ExamCombo::setExamType);
                        cols.withDefault("性别", value -> IDictEnum.fromName(SexLimit.class, value.toString()), ExamCombo::setSex, SexLimit.NORMAL);
                        cols.withDefault("年龄阶段", value -> IDictEnum.fromName(AgeLimit.class, value.toString()), ExamCombo::setAge, AgeLimit.NORMAL);
                        cols.withDefault("适用客体", value -> IDictEnum.fromName(CustomerLimit.class, value.toString()), ExamCombo::setCustomer, CustomerLimit.NORMAL);
                        cols.withDefault("排序值", ExcelParser.integer(), ExamCombo::setSort, 0);
                        cols.add("备注", ExcelParser.string(), ExamCombo::setRemark);
                        cols.withDefault("状态", "禁用"::equals, ExamCombo::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            examComboService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "体检套餐关联项目列表")
    @GetMapping("/listProject")
    @Perm("health:examCombo:project")
    public Result<List<ExamComboDto.ComboProject>> listProject(Long id) {
        // 获取套餐包含的项目列表
        var comboProjects = examComboProjectService.getListByCombo(id);

        // 构建项目ID的映射
        Map<Long, ExamProject> projectMap = comboProjects.isEmpty() ?
                Map.of() :
                examProjectService.getList().stream().collect(Collectors.toUnmodifiableMap(ExamProject::getId, Function.identity()));
        ExamProject emptyExamProject = new ExamProject();

        return Result.okData(comboProjects.stream()
                .map(item -> ExamComboDto.ComboProject.builder()
                        .examComboId(item.getExamComboId())
                        .examProjectId(item.getExamProjectId())
                        .examProjectName(projectMap.getOrDefault(item.getExamProjectId(), emptyExamProject).getName())
                        .comboPrice(projectMap.getOrDefault(item.getExamProjectId(), emptyExamProject).getComboPrice())
                        .build()
                )
                .toList());
    }

    @Operation(summary = "新增体检套餐关联项目")
    @PostMapping("/addProject")
    @AuditLog(code = "health:examCombo:project:add", value = "体检套餐关联项目-新增")
    @Perm("health:examCombo:project:add")
    public Result<Long> addProject(@RequestBody @Valid ExamComboDto.ComboProject dto) {
        var entity = ExamComboProject.builder()
                .examProjectId(dto.getExamProjectId())
                .examComboId(dto.getExamComboId())
                .build();

        return examComboProjectService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑体检套餐关联项目")
    @GetMapping("/editProject")
    @Perm("health:examCombo:project:edit")
    public Result<ExamComboDto.ComboProject> getEditProject(Long id) {
        var entityOpt = examComboProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new ExamComboDto.ComboProject())
        );
    }

    @Operation(summary = "编辑体检套餐关联项目")
    @PostMapping("/editProject")
    @AuditLog(code = "health:examCombo:project:edit", value = "体检套餐关联项目-编辑")
    @Perm("health:examCombo:project:edit")
    public Result<Void> editProject(@RequestParam Long id, @RequestBody @Valid ExamComboDto.ComboProject dto) {
        var entityOpt = examComboProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("关联数据不存在");
        }

        var examComboProject = entityOpt.get();
        examComboProject.setExamProjectId(dto.getExamProjectId());
        examComboProject.setExamComboId(dto.getExamComboId());

        if (!examComboProjectService.update(examComboProject))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除体检套餐关联项目")
    @PostMapping("/deleteProject")
    @AuditLog(code = "health:examCombo:project:delete", value = "体检套餐关联项目-删除")
    @Perm("health:examCombo:project:delete")
    public Result<Void> deleteProject(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            examComboProjectService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "导出体检套餐关联项目")
    @PostMapping("/exportProject")
    @AuditLog(code = "health:examCombo:project:export", value = "体检套餐关联项目-导出")
    @Perm("health:examCombo:project:export")
    public void exportProject(@RequestParam Long id, HttpServletResponse response) throws IOException {
        // 获取套餐包含的项目列表
        var comboProjects = examComboProjectService.getListByCombo(id);

        // 构建项目ID到名称的映射
        Map<Long, String> projectNameMap = comboProjects.isEmpty() ? Map.of() :
                examProjectService.getList().stream()
                        .collect(Collectors.toUnmodifiableMap(ExamProject::getId, ExamProject::getName));

        // 转换为导出DTO列表
        var exportData = comboProjects.stream()
                .map(item -> ExamComboDto.ComboProject.builder()
                        .examProjectName(projectNameMap.getOrDefault(item.getExamProjectId(), ""))
                        .build()
                )
                .toList();

        ExcelExporter.<ExamComboDto.ComboProject>create()
                .sheet("套餐包含项目")
                .columns(cols ->
                        cols.add("项目名称", ExamComboDto.ComboProject::getExamProjectName)
                )
                .data(exportData)
                .export(response, "套餐包含项目");
    }

    /**
     * 条件处理
     */
    private QueryWrapper<ExamCombo> applyQueryCondition(QueryWrapper<ExamCombo> wrapper, String type,
                                                        String key,
                                                        Long examType,
                                                        Boolean disabled) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.like("name", keyTrim)
                        .or().like("py", keyTrim);
            }
            wrapper.like("code".equals(type), "code", keyTrim);
        }
        wrapper.eq(examType != null, "exam_type", examType);
        wrapper.eq(disabled != null, "disabled", disabled);

        return wrapper;
    }

}
