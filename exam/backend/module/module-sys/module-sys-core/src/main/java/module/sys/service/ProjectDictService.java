package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Chinese;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ProjectDict;
import module.sys.mapper.ProjectDictMapper;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 项目字典服务
 */
@Slf4j
@Service
public class ProjectDictService extends ServiceBase<ProjectDictMapper, ProjectDict> {
    @Override
    protected void beforeSave(ProjectDict entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        setPinyin(entity);
    }

    @Override
    protected Function<QueryWrapper<ProjectDict>, QueryWrapper<ProjectDict>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected Function<QueryWrapper<ProjectDict>, QueryWrapper<ProjectDict>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    /**
     * 设置拼音
     */
    private void setPinyin(ProjectDict value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
