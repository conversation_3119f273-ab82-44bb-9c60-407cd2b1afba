<script setup lang="ts">
import { useMenu } from '~/composables'

const route = useRoute()
const router = useRouter()
const { activeModule } = useMenu()

const menu = computed(() => activeModule.value?.children || [])

function handleMenuSelect(path: string) {
  router.push({
    path,
  })
}
</script>

<template>
  <div class="h-full select-none">
    <div class="h-full flex">
      <el-menu collapse class="left-menu h-full" :style="{ width: menu.length ? '74px' : '0' }" :default-active="route.name" @select="handleMenuSelect">
        <el-scrollbar>
          <template v-for="level1 in menu" :key="level1.name">
            <template v-if="level1.children && level1.children.length">
              <el-sub-menu :index="level1.name" class="flex items-center justify-center">
                <template #title>
                  <div class="h-full w-full flex flex-col items-center justify-center">
                    <icon :icon="level1.icon" :width="level1.size || 24" :height="level1.size || 24" class="mb-2" />
                    <div class="text-center text-sm line-height-tight">
                      {{ level1.title }}
                    </div>
                  </div>
                </template>
                <template v-for="level2 in level1.children || []" :key="level2.name">
                  <template v-if="level2.children && level2.children.length">
                    <el-sub-menu :index="level2.name">
                      <template #title>
                        <el-icon v-if="level2.icon">
                          <icon :icon="level2.icon" />
                        </el-icon>
                        <span>{{ level2.title }}</span>
                      </template>
                      <el-menu-item v-for="level3 in level2.children || []" :key="level3.name" :index="level3.name">
                        <el-icon v-if="level3.icon">
                          <icon :icon="level3.icon" />
                        </el-icon>
                        <span>{{ level3.title }}</span>
                      </el-menu-item>
                    </el-sub-menu>
                  </template>
                  <template v-else>
                    <el-menu-item :index="level2.name">
                      <el-icon v-if="level2.icon">
                        <icon :icon="level2.icon" />
                      </el-icon>
                      <span>{{ level2.title }}</span>
                    </el-menu-item>
                  </template>
                </template>
              </el-sub-menu>
            </template>
            <template v-else>
              <el-menu-item :index="level1.name" class="flex items-center justify-center">
                <div class="h-full w-full flex flex-col items-center justify-center">
                  <icon :icon="level1.icon" :width="level1.size || 24" :height="level1.size || 24" class="mb-2" />
                  <div class="text-center text-sm line-height-tight">
                    {{ level1.title }}
                  </div>
                </div>
              </el-menu-item>
            </template>
          </template>
        </el-scrollbar>
      </el-menu>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.left-menu .el-menu-item),
:deep(.left-menu .el-sub-menu) {
  width: 68px;
  height: 76px;
  border-radius: 4px;
  margin: 4px 2px;
  &:hover {
    background: var(--el-menu-hover-bg-color);
  }
  .el-sub-menu__title {
    width: 68px;
    height: 76px;
    border-radius: 4px;
  }

  /**
  &.is-active {
    background: var(--el-color-primary);
    color: #fff;
    .el-sub-menu__title {
      background: var(--el-color-primary);
      color: #fff;
    }
  }
  */
}

:deep(.left-menu .el-sub-menu > .el-sub-menu__title) {
  // 隐藏一级菜单的展开箭头
  .el-sub-menu__icon-arrow {
    display: none !important;
  }
}
</style>
