package infra.sms.core;

import infra.core.common.Result;

import java.util.List;

/**
 * 短信服务接口
 */
public interface ISmsService {
    /**
     * 注册系统使用到的短信
     *
     * @param messages 短信
     */
    void register(List<ISmsMessage> messages);

    /**
     * 发送短信
     * @param number 接收号码
     * @param messageId 消息id
     * @param params 短信参数
     */
    Result<String> send(String number, String messageId, String... params);
}
