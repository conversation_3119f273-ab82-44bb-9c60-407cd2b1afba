package infra.core.config;

import infra.core.common.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;

/**
 * 配置
 */
@Slf4j
@AutoConfiguration
public class CoreAutoConfiguration implements ApplicationContextAware {
    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        log.info("[配置] 已初始化设置Spring上下文");
        SpringUtil.setApplicationContext(applicationContext);
    }
}
