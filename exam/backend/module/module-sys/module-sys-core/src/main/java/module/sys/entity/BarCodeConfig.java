package module.sys.entity;

import com.baomidou.mybatisplus.annotation.Version;
import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 检验条码设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_bar_code_config")
public class BarCodeConfig extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 条形码前缀
    private String prefix;

    // 条形码名称
    private String name;

    // 拼音
    private String py;

    // 条形码长度(不含前缀)
    private Integer len;

    // 起始编号
    private Integer startNo;

    // 当前编号
    private Long currentNo;

    // 备注说明
    private String remark;

    // 样本体积
    private String sampleVolume;

    // 样本颜色
    private String sampleColor;

    // 样本说明
    private String sampleRemark;

    // 禁用
    private Boolean disabled;

    @Version
    @Builder.Default
    private Long version = 0L;
}