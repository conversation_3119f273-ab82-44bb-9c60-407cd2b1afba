import { get } from '~/utils/request'

// 存储
const store = new Map<string, any>()

const sysCommonUrl = '/sys/common'
// 字典Key与地址的映射
const KEYS = {
  // 后台任务状态
  JOB_STATUS: `${sysCommonUrl}/status?key=job_status`,
  // 数据权限
  DATA_PERM: `${sysCommonUrl}/status?key=data_perm`,
  // 用户状态
  USER_STATUS: `${sysCommonUrl}/status?key=user_status`,
  // 性别
  SEX: `${sysCommonUrl}/status?key=sex`,
  // 性别限制
  SEX_LIMIT: `${sysCommonUrl}/status?key=sex_limit`,
  // 年龄阶段限制
  AGE_LIMIT: `${sysCommonUrl}/status?key=age_limit`,
  // 科室列表
  DEPT_LIST: `/sys/dept/commonDept`,
  // 文书模板编码
  DOC_CODES: `/sys/docTemplate/codes`,
  // 涨尿/排尿
  URINE: `${sysCommonUrl}/status?key=urine`,
  // 餐前餐后
  DINING: `${sysCommonUrl}/status?key=dining`,
  // 样本类型
  SAMPLE_TYPE: `/sys/sampleType/dict`,
  // 条码前缀
  BAR_CODE: `/sys/barCodeConfig/dict`,
  // 文书模板
  DOC_TEMPLATE: `/sys/docTemplate/dict`,
  // 项目字典
  PROJECT_DICT: `/sys/projectDict/dict`,
  // 收费项目
  PROJECT_FEE: `/sys/feeProject/dict`,
  // 公共字典
  DICT: {
    // 民族
    NATION: `${sysCommonUrl}/dict?code=nation`,
    // 教育程度
    EDUCATION: `${sysCommonUrl}/dict?code=education`,
    // 支付方式
    PAY_TYPE: `${sysCommonUrl}/dict?code=pay_type`,
    // 项目类型
    PROJECT_TYPE: `${sysCommonUrl}/dict?code=project_type`,
    // 体检类型
    EXAM_TYPE: `${sysCommonUrl}/dict?code=exam_type`,
    // 婚姻状态
    MARITAL_STATUS: `${sysCommonUrl}/dict?code=marital_status`,
    // 健康状态
    HEALTH_TYPE: `${sysCommonUrl}/dict?code=health_type`,
    // 体检人类型
    CUSTOMER_TYPE: `${sysCommonUrl}/dict?code=customer_type`,
    // 医务人员职称
    MEDICAL_TITLE: `${sysCommonUrl}/dict?code=medical_title`,
    // 医务人员类型
    MEDICAL_TYPE: `${sysCommonUrl}/dict?code=medical_type`,
    // 身体部位
    BODY_PART: `${sysCommonUrl}/dict?code=body_part`,
    // 开单分类
    BILLING_TYPE: `${sysCommonUrl}/dict?code=billing_type`,
    // 地址
    ADDRESS: `${sysCommonUrl}/dict?code=address`,
  },
}

let clearTimer: null | number = null

export function useDict() {
  // 启动定时清理任务
  function startAutoClear() {
    // 如果已存在定时器，先清除
    if (clearTimer) {
      clearInterval(clearTimer)
    }

    // 每30分钟清理一次缓存
    clearTimer = setInterval(() => {
      store.clear()
    }, 30 * 60 * 1000)
  }

  // 停止定时清理任务
  function stopAutoClear() {
    if (clearTimer) {
      clearInterval(clearTimer)
      clearTimer = null
    }
  }

  // 获取字典数据
  async function getDict(key: string): Promise<any> {
    if (store.has(key)) {
      return store.get(key)
    }

    const res = await get(key)
    if (res) {
      store.set(key, res.data)
      return res.data
    }

    return Promise.reject(new Error(`获取数据失败: ${key}`))
  }

  // 移除字典数据
  function removeDict(key: string) {
    store.delete(key)
  }

  // 移除系统公共字典数据
  function removeSysDict(code: string) {
    const key = (KEYS.DICT as any)[code.toUpperCase()]
    if (key) {
      store.delete(key)
    }
  }

  // 清除所有数据
  function clear() {
    store.clear()
  }

  // 获取状态描述(用于获取后端枚举值的描述)
  function getName(key: string) {
    if (!store.has(key)) {
      getDict(key).catch(() => {})
    }

    return (id: number, defaultValue: string = '') => {
      return computed(() => {
        if (store.has(key) && store.get(key)) {
          return store.get(key).find((item: any) => item.id === id)?.name || defaultValue
        }
        return defaultValue
      })
    }
  }

  startAutoClear()

  return {
    KEYS,
    getDict,
    removeDict,
    removeSysDict,
    clear,
    getName,
    startAutoClear,
    stopAutoClear,
  }
}
