<script setup lang="ts">
definePage({
  meta: {
    title: '体检开单',
    menu: true,
    order: 3,
    icon: 'ix:project-duplicate',
  },
})
</script>

<template>
  <p>1. 团检客户项目开单：按团体定制需求，批量选定体检项目，支持项目增删并校验排异，关联团体优惠规则；</p>
  <p>2. 项目准备工作：生成团检项目清单，同步至检查科室，提前准备所需设备与耗材；</p>
  <p>3. 开单数据统计：实时统计团检开单人数、项目类型占比，便于前台掌握团检业务量；</p>
  <p>4. 开单记录保存，为后续预约排期、费用结算提供项目数据支撑</p>

  检前阶段 “开单登记与导入（批量导入团检信息）”“体检套餐确认（校验项目调整合理性）”；检前阶段 “项目物价核对（内置物价标准，确保收费依据）”
</template>
