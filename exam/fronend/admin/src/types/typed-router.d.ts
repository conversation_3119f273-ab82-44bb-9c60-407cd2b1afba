/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/common/403': RouteRecordInfo<'/common/403', '/common/403', Record<never, never>, Record<never, never>>,
    '/common/notice/': RouteRecordInfo<'/common/notice/', '/common/notice', Record<never, never>, Record<never, never>>,
    '/common/profile/': RouteRecordInfo<'/common/profile/', '/common/profile', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Base': RouteRecordInfo<'/common/profile/components/Base', '/common/profile/components/Base', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Log': RouteRecordInfo<'/common/profile/components/Log', '/common/profile/components/Log', Record<never, never>, Record<never, never>>,
    '/common/profile/components/Sign': RouteRecordInfo<'/common/profile/components/Sign', '/common/profile/components/Sign', Record<never, never>, Record<never, never>>,
    '/customer/': RouteRecordInfo<'/customer/', '/customer', Record<never, never>, Record<never, never>>,
    '/customer/contact/': RouteRecordInfo<'/customer/contact/', '/customer/contact', Record<never, never>, Record<never, never>>,
    '/customer/intent/': RouteRecordInfo<'/customer/intent/', '/customer/intent', Record<never, never>, Record<never, never>>,
    '/customer/package/': RouteRecordInfo<'/customer/package/', '/customer/package', Record<never, never>, Record<never, never>>,
    '/customer/reserve/': RouteRecordInfo<'/customer/reserve/', '/customer/reserve', Record<never, never>, Record<never, never>>,
    '/dept/': RouteRecordInfo<'/dept/', '/dept', Record<never, never>, Record<never, never>>,
    '/dept/decision/': RouteRecordInfo<'/dept/decision/', '/dept/decision', Record<never, never>, Record<never, never>>,
    '/dept/decision/add/': RouteRecordInfo<'/dept/decision/add/', '/dept/decision/add', Record<never, never>, Record<never, never>>,
    '/dept/decision/cacel/': RouteRecordInfo<'/dept/decision/cacel/', '/dept/decision/cacel', Record<never, never>, Record<never, never>>,
    '/dept/finance/': RouteRecordInfo<'/dept/finance/', '/dept/finance', Record<never, never>, Record<never, never>>,
    '/dept/finance/bill/': RouteRecordInfo<'/dept/finance/bill/', '/dept/finance/bill', Record<never, never>, Record<never, never>>,
    '/dept/finance/invoice/': RouteRecordInfo<'/dept/finance/invoice/', '/dept/finance/invoice', Record<never, never>, Record<never, never>>,
    '/dept/inspection/': RouteRecordInfo<'/dept/inspection/', '/dept/inspection', Record<never, never>, Record<never, never>>,
    '/dept/inspection/icdDict/': RouteRecordInfo<'/dept/inspection/icdDict/', '/dept/inspection/icdDict', Record<never, never>, Record<never, never>>,
    '/dept/inspection/medical/': RouteRecordInfo<'/dept/inspection/medical/', '/dept/inspection/medical', Record<never, never>, Record<never, never>>,
    '/dept/inspection/sampleType/': RouteRecordInfo<'/dept/inspection/sampleType/', '/dept/inspection/sampleType', Record<never, never>, Record<never, never>>,
    '/dept/knowledge/': RouteRecordInfo<'/dept/knowledge/', '/dept/knowledge', Record<never, never>, Record<never, never>>,
    '/dept/knowledge/signDict/': RouteRecordInfo<'/dept/knowledge/signDict/', '/dept/knowledge/signDict', Record<never, never>, Record<never, never>>,
    '/dept/project/': RouteRecordInfo<'/dept/project/', '/dept/project', Record<never, never>, Record<never, never>>,
    '/dept/project/examProject/': RouteRecordInfo<'/dept/project/examProject/', '/dept/project/examProject', Record<never, never>, Record<never, never>>,
    '/dept/project/examProject/components/ProjectDict': RouteRecordInfo<'/dept/project/examProject/components/ProjectDict', '/dept/project/examProject/components/ProjectDict', Record<never, never>, Record<never, never>>,
    '/dept/project/examProject/components/ProjectFee': RouteRecordInfo<'/dept/project/examProject/components/ProjectFee', '/dept/project/examProject/components/ProjectFee', Record<never, never>, Record<never, never>>,
    '/dept/project/feeProject/': RouteRecordInfo<'/dept/project/feeProject/', '/dept/project/feeProject', Record<never, never>, Record<never, never>>,
    '/dept/project/pack/': RouteRecordInfo<'/dept/project/pack/', '/dept/project/pack', Record<never, never>, Record<never, never>>,
    '/dept/project/projectDict/': RouteRecordInfo<'/dept/project/projectDict/', '/dept/project/projectDict', Record<never, never>, Record<never, never>>,
    '/dept/staff/': RouteRecordInfo<'/dept/staff/', '/dept/staff', Record<never, never>, Record<never, never>>,
    '/dept/template/': RouteRecordInfo<'/dept/template/', '/dept/template', Record<never, never>, Record<never, never>>,
    '/exam/': RouteRecordInfo<'/exam/', '/exam', Record<never, never>, Record<never, never>>,
    '/exam/critical/': RouteRecordInfo<'/exam/critical/', '/exam/critical', Record<never, never>, Record<never, never>>,
    '/exam/critical/collect/': RouteRecordInfo<'/exam/critical/collect/', '/exam/critical/collect', Record<never, never>, Record<never, never>>,
    '/exam/critical/result/': RouteRecordInfo<'/exam/critical/result/', '/exam/critical/result', Record<never, never>, Record<never, never>>,
    '/exam/critical/submit/': RouteRecordInfo<'/exam/critical/submit/', '/exam/critical/submit', Record<never, never>, Record<never, never>>,
    '/exam/entry/': RouteRecordInfo<'/exam/entry/', '/exam/entry', Record<never, never>, Record<never, never>>,
    '/exam/entry/check/': RouteRecordInfo<'/exam/entry/check/', '/exam/entry/check', Record<never, never>, Record<never, never>>,
    '/exam/entry/image/': RouteRecordInfo<'/exam/entry/image/', '/exam/entry/image', Record<never, never>, Record<never, never>>,
    '/exam/entry/sign/': RouteRecordInfo<'/exam/entry/sign/', '/exam/entry/sign', Record<never, never>, Record<never, never>>,
    '/exam/evaluation/': RouteRecordInfo<'/exam/evaluation/', '/exam/evaluation', Record<never, never>, Record<never, never>>,
    '/exam/roster/': RouteRecordInfo<'/exam/roster/', '/exam/roster', Record<never, never>, Record<never, never>>,
    '/exam/specimen/': RouteRecordInfo<'/exam/specimen/', '/exam/specimen', Record<never, never>, Record<never, never>>,
    '/front/': RouteRecordInfo<'/front/', '/front', Record<never, never>, Record<never, never>>,
    '/front/archive/': RouteRecordInfo<'/front/archive/', '/front/archive', Record<never, never>, Record<never, never>>,
    '/front/billing/': RouteRecordInfo<'/front/billing/', '/front/billing', Record<never, never>, Record<never, never>>,
    '/front/guidance/': RouteRecordInfo<'/front/guidance/', '/front/guidance', Record<never, never>, Record<never, never>>,
    '/front/reception/': RouteRecordInfo<'/front/reception/', '/front/reception', Record<never, never>, Record<never, never>>,
    '/front/schedule/': RouteRecordInfo<'/front/schedule/', '/front/schedule', Record<never, never>, Record<never, never>>,
    '/front/settlement/': RouteRecordInfo<'/front/settlement/', '/front/settlement', Record<never, never>, Record<never, never>>,
    '/health/': RouteRecordInfo<'/health/', '/health', Record<never, never>, Record<never, never>>,
    '/health/case/': RouteRecordInfo<'/health/case/', '/health/case', Record<never, never>, Record<never, never>>,
    '/health/follow/': RouteRecordInfo<'/health/follow/', '/health/follow', Record<never, never>, Record<never, never>>,
    '/health/mining/': RouteRecordInfo<'/health/mining/', '/health/mining', Record<never, never>, Record<never, never>>,
    '/health/record/': RouteRecordInfo<'/health/record/', '/health/record', Record<never, never>, Record<never, never>>,
    '/login': RouteRecordInfo<'/login', '/login', Record<never, never>, Record<never, never>>,
    '/report/': RouteRecordInfo<'/report/', '/report', Record<never, never>, Record<never, never>>,
    '/report/delivery/': RouteRecordInfo<'/report/delivery/', '/report/delivery', Record<never, never>, Record<never, never>>,
    '/report/inspection/': RouteRecordInfo<'/report/inspection/', '/report/inspection', Record<never, never>, Record<never, never>>,
    '/report/issue/': RouteRecordInfo<'/report/issue/', '/report/issue', Record<never, never>, Record<never, never>>,
    '/report/pileup/': RouteRecordInfo<'/report/pileup/', '/report/pileup', Record<never, never>, Record<never, never>>,
    '/report/review/': RouteRecordInfo<'/report/review/', '/report/review', Record<never, never>, Record<never, never>>,
    '/sys/': RouteRecordInfo<'/sys/', '/sys', Record<never, never>, Record<never, never>>,
    '/sys/components/DeptPost': RouteRecordInfo<'/sys/components/DeptPost', '/sys/components/DeptPost', Record<never, never>, Record<never, never>>,
    '/sys/components/Perm': RouteRecordInfo<'/sys/components/Perm', '/sys/components/Perm', Record<never, never>, Record<never, never>>,
    '/sys/dept/': RouteRecordInfo<'/sys/dept/', '/sys/dept', Record<never, never>, Record<never, never>>,
    '/sys/interface/': RouteRecordInfo<'/sys/interface/', '/sys/interface', Record<never, never>, Record<never, never>>,
    '/sys/log/': RouteRecordInfo<'/sys/log/', '/sys/log', Record<never, never>, Record<never, never>>,
    '/sys/log/audit/': RouteRecordInfo<'/sys/log/audit/', '/sys/log/audit', Record<never, never>, Record<never, never>>,
    '/sys/log/job/': RouteRecordInfo<'/sys/log/job/', '/sys/log/job', Record<never, never>, Record<never, never>>,
    '/sys/log/job/components/Job': RouteRecordInfo<'/sys/log/job/components/Job', '/sys/log/job/components/Job', Record<never, never>, Record<never, never>>,
    '/sys/log/job/components/Log': RouteRecordInfo<'/sys/log/job/components/Log', '/sys/log/job/components/Log', Record<never, never>, Record<never, never>>,
    '/sys/log/loginlog/': RouteRecordInfo<'/sys/log/loginlog/', '/sys/log/loginlog', Record<never, never>, Record<never, never>>,
    '/sys/parameter/': RouteRecordInfo<'/sys/parameter/', '/sys/parameter', Record<never, never>, Record<never, never>>,
    '/sys/parameter/barCodeConfig/': RouteRecordInfo<'/sys/parameter/barCodeConfig/', '/sys/parameter/barCodeConfig', Record<never, never>, Record<never, never>>,
    '/sys/parameter/dict/': RouteRecordInfo<'/sys/parameter/dict/', '/sys/parameter/dict', Record<never, never>, Record<never, never>>,
    '/sys/parameter/file/': RouteRecordInfo<'/sys/parameter/file/', '/sys/parameter/file', Record<never, never>, Record<never, never>>,
    '/sys/parameter/idBuilder/': RouteRecordInfo<'/sys/parameter/idBuilder/', '/sys/parameter/idBuilder', Record<never, never>, Record<never, never>>,
    '/sys/parameter/setting/': RouteRecordInfo<'/sys/parameter/setting/', '/sys/parameter/setting', Record<never, never>, Record<never, never>>,
    '/sys/parameter/setting/components/App': RouteRecordInfo<'/sys/parameter/setting/components/App', '/sys/parameter/setting/components/App', Record<never, never>, Record<never, never>>,
    '/sys/parameter/setting/components/HospitalInfo': RouteRecordInfo<'/sys/parameter/setting/components/HospitalInfo', '/sys/parameter/setting/components/HospitalInfo', Record<never, never>, Record<never, never>>,
    '/sys/parameter/setting/components/Safe': RouteRecordInfo<'/sys/parameter/setting/components/Safe', '/sys/parameter/setting/components/Safe', Record<never, never>, Record<never, never>>,
    '/sys/parameter/smsConfig/': RouteRecordInfo<'/sys/parameter/smsConfig/', '/sys/parameter/smsConfig', Record<never, never>, Record<never, never>>,
    '/sys/perm/': RouteRecordInfo<'/sys/perm/', '/sys/perm', Record<never, never>, Record<never, never>>,
    '/sys/perm/components/Post': RouteRecordInfo<'/sys/perm/components/Post', '/sys/perm/components/Post', Record<never, never>, Record<never, never>>,
    '/sys/perm/components/User': RouteRecordInfo<'/sys/perm/components/User', '/sys/perm/components/User', Record<never, never>, Record<never, never>>,
    '/sys/user/': RouteRecordInfo<'/sys/user/', '/sys/user', Record<never, never>, Record<never, never>>,
  }
}
