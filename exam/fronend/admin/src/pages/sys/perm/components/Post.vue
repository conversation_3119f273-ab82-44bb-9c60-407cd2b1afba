<script setup lang="ts">
import * as api from '~/api/sys/perm'
import { useDict, useLoading } from '~/composables'
import { tips } from '~/utils'
import Perm from '../../components/Perm.vue'

// 状态
const editPost = ref<any>({})
const visible = ref(false)
const activeTab = ref('perm')
const dataPerm = ref(0)
const permRef = useTemplateRef('permRef')
const { loading, withLoading } = useLoading()
const { loading: submiting, withLoading: withSubmitting } = useLoading()
const { KEYS } = useDict()

const dialogTitle = computed(() => {
  return `岗位权限设置 - ${editPost.value.name}`
})

// 提交
async function submit() {
  await withSubmitting(async () => {
    const perms = permRef.value?.getCheckedKeys()
    await api.postPostPerm(editPost.value.id, {
      perms,
      data: dataPerm.value,
    })
    tips.success()
    visible.value = false
  })
}

// 打开
async function open(post: any) {
  editPost.value = post
  visible.value = true
  activeTab.value = 'perm'
  permRef.value?.setCheckedKeys([])
  await withLoading(async () => {
    const res = await api.getPostPerm(post.id)
    permRef.value?.setCheckedKeys(res.data.perms || [])
    dataPerm.value = res.data.data
  })
}

defineExpose({
  open,
})
</script>

<template>
  <el-dialog
    v-model="visible" :title="dialogTitle" width="550" draggable
    :close-on-click-modal="false" :close-on-press-escape="false"
  >
    <el-tabs v-model="activeTab" v-loading="loading">
      <el-tab-pane label="功能权限" name="perm">
        <Perm ref="permRef" />
      </el-tab-pane>
      <el-tab-pane label="数据权限" name="data">
        <div class="p-4">
          <dict-select v-model="dataPerm" :dict-key="KEYS.DATA_PERM" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button :disabled="submiting" @click="visible = false">
          取消
        </el-button>
        <el-button type="primary" :loading="submiting" :disabled="loading" @click="submit">
          <template #icon>
            <icon icon="ep:check" />
          </template>
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
