package module.sys.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 授权用户信息
 */
@Data
public class AuthUser implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 用户ID
    private Long id;
    // 登录名
    private String loginName;
    // 用户名
    private String userName;
    // 是否为管理员
    private Boolean admin;
    // 部门ID
    private Long deptId;
    // // 部门名称
    private String deptName;
    // 扩展字符(修改密码等安全信息变更，这个值会重新生成)
    private String extra;
}
