package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.SexLimit;

/**
 * 病史类别Dto
 */
public class MedicalTypeDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 病史类别名称
        @NotBlank(message = "病史类别名称不能为空")
        private String name;
        // 性别 0：不限 1：男 2：女
        private SexLimit sex;
        // 用于成人或儿童 0：不限 1：成人 2：儿童
        private AgeLimit age;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
    }    
}