<script setup lang="ts">
definePage({
  meta: {
    title: '总检评价',
    menu: true,
    order: 4,
    icon: 'tabler:file-pencil',
  },
})
</script>

<template>
<p>1. 结果调阅：总检医生可查看客户所有检查结果（含录入结果、外送回填结果），调用系统辅助知识库（体征词、结论词）；</p>
<p>2. 结论生成：基于检查结果与知识库，生成综合总检结论，系统自动检查结论规范性（如术语准确性、逻辑完整性）；</p>
<p>3. 缺陷处理：发现项目遗漏、结果矛盾等缺陷时，将记录驳回至对应科室补检 / 修正，跟踪处理进度直至闭环；</p>
<p>4. 评估归档：总检结论生成后，自动流转至总审环节，同时为报告处理模块生成体检报告提供核心依据</p>

检后阶段 “总检评价（总检医生查看结果、生成结论，系统检查规范性）”“缺陷项目驳回（跟踪补检 / 修正进度）”；检中阶段 “标本外送回填（外送结果为总检提供完整数据）”
</template>
