package infra.domain.encrypt;

import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.entity.IEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 数据库字段加密拦截器
 * 只对标注了 @EncryptField 的字段进行加密/解密处理
 */
@Slf4j
@RequiredArgsConstructor
@Intercepts({
        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class),
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = Statement.class)
})
public class FieldEncryptInterceptor implements Interceptor {

    private final IFieldEncryptor encryptor;

    // 缓存类的加密字段信息，避免重复反射
    private final Map<Class<?>, List<Field>> FIELD_CACHE = new ConcurrentReferenceHashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object target = invocation.getTarget();

        if (target instanceof ParameterHandler) {
            // 处理参数加密（INSERT/UPDATE）
            handleParameterEncrypt(invocation);
            return invocation.proceed();
        } else if (target instanceof ResultSetHandler) {
            // 处理结果解密（SELECT）
            Object result = invocation.proceed();
            return handleResultDecrypt(result);
        }

        return invocation.proceed();
    }

    /**
     * 处理参数加密
     */
    private void handleParameterEncrypt(Invocation invocation) {
        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(parameterHandler);

        Object parameterObject = metaObject.getValue("parameterObject");
        if (parameterObject != null) {
            encryptFields(parameterObject);
        }
    }

    /**
     * 处理结果解密
     */
    private Object handleResultDecrypt(Object result) {
        if (result instanceof List<?> list) {
            list.forEach(this::decryptFields);
        } else if (result != null) {
            decryptFields(result);
        }
        return result;
    }

    /**
     * 加密对象中标注了 @EncryptField 的字段
     */
    private void encryptFields(Object obj) {
        if (obj == null) return;

        List<Field> encryptFields = getEncryptFields(obj.getClass());
        if (encryptFields == null || encryptFields.isEmpty()) {
            return;
        }

        for (Field field : encryptFields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);

                if (value instanceof String && !Str.isEmpty((String) value)) {
                    String encrypted = encryptor.encrypt((String) value);
                    field.set(obj, encrypted);
                }
            } catch (Exception e) {
                log.error("加密字段 {}.{} 失败", obj.getClass().getSimpleName(), field.getName(), e);
                throw new BizException("字段加密失败", e);
            }
        }
    }

    /**
     * 解密对象中标注了 @EncryptField 的字段
     */
    private void decryptFields(Object obj) {
        if (obj == null) return;

        List<Field> encryptFields = getEncryptFields(obj.getClass());
        if (encryptFields == null || encryptFields.isEmpty()) {
            return;
        }

        for (Field field : encryptFields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);

                if (value instanceof String && !Str.isEmpty((String) value)) {
                    String decrypted = encryptor.decrypt((String) value);
                    field.set(obj, decrypted);
                }
            } catch (Exception e) {
                log.error("解密字段 {}.{} 失败", obj.getClass().getSimpleName(), field.getName(), e);
            }
        }
    }

    /**
     * 获取类中标注了 @EncryptField 的字段（带缓存）
     */
    private List<Field> getEncryptFields(Class<?> clazz) {
        if (!IEntity.class.isAssignableFrom(clazz)) {
            return null;
        }
        return FIELD_CACHE.computeIfAbsent(clazz, this::findEncryptFields);
    }

    /**
     * 查找类中标注了 @EncryptField 的字段
     */
    private List<Field> findEncryptFields(Class<?> clazz) {
        List<Field> encryptFields = new ArrayList<>();

        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields) {
                if (shouldProcessField(field)) {
                    encryptFields.add(field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        if (clazz != null) {
            log.debug("类 {} 发现 {} 个加密字段", clazz.getSimpleName(), encryptFields.size());
        }
        return Collections.unmodifiableList(encryptFields);
    }

    private boolean shouldProcessField(Field field) {
        return field.isAnnotationPresent(EncryptField.class) &&
                field.getType() == String.class &&
                !Modifier.isStatic(field.getModifiers()) &&
                !Modifier.isFinal(field.getModifiers()) &&
                !Modifier.isTransient(field.getModifiers()) &&
                !field.isSynthetic();
    }
}
