package module.sys.service;

import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.SmsConfig;
import module.sys.mapper.SmsConfigMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class SmsConfigService extends ServiceBase<SmsConfigMapper, SmsConfig> {
    /**
     * 根据消息id获取
     */
    public Optional<SmsConfig> getByMessageId(String messageId, FilterOptions filterOptions) {
        return getFirst(w -> w.eq("message_id", messageId), filterOptions);
    }

    /**
     * 根据消息id获取
     */
    public Optional<SmsConfig> getByMessageId(String messageId) {
        return getByMessageId(messageId, FilterOptions.DEFAULTS);
    }
}
