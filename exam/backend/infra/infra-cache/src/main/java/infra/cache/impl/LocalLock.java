package infra.cache.impl;

import infra.cache.core.ILock;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/***
 * 本地锁实现，仅适用于单机场景
 */
@Slf4j
public class LocalLock implements ILock {

    private final ReentrantLock lock = new ReentrantLock();

    /**
     * 尝试获取锁
     *
     * @param timeoutSeconds 获取锁的超时时间（秒），小于等于0表示不带超时
     * @return true表示获取成功，false表示获取失败
     */
    @Override
    public boolean tryLock(long timeoutSeconds) {
        try {
            if (timeoutSeconds <= 0) {
                return lock.tryLock();
            } else {
                return lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
            }
        } catch (InterruptedException e) {
            // 虚拟线程被中断时，恢复中断状态并返回false
            log.warn("获取锁时线程被中断", e);
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 释放锁
     */
    @Override
    public void unlock() {
        lock.unlock();
    }
}
