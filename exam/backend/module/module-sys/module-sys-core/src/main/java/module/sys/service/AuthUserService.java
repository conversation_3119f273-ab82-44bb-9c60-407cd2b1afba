package module.sys.service;

import infra.auth.config.AuthConfigProperties;
import infra.auth.core.AuthManager;
import infra.auth.core.IUser;
import infra.auth.core.IUserService;
import infra.auth.core.Token;
import infra.cache.core.ICache;
import infra.cache.impl.NoneCache;
import infra.core.common.Result;
import infra.core.common.SpringUtil;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.service.FilterOptions;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.LoginLog;
import module.sys.entity.User;
import module.sys.model.AuthUser;
import module.sys.model.SafeSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * 认证服务
 */
@Slf4j
@Service
public class AuthUserService implements IUserService {
    @Autowired
    private SettingService settingService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserDeptService userDeptService;
    @Autowired
    private DeptService deptService;
    @Autowired
    private PostService postService;
    @Autowired
    private LoginLogService loginLogService;
    @Autowired
    private ICache cache;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private AuthConfigProperties config;

    /**
     * 获取授权用户
     */
    @Override
    public IUser getUser(Long userId, Long deptId, String extra) {
        if (userId == null) {
            return null;
        }

        String cacheKey = getAuthCachePrefix(userId) + "base" + (deptId != null ? deptId : "");
        var userModel = getCache().getOrSet(cacheKey, AuthUser.class, () -> {
            // 用户
            var userOpt = userService.getById(userId);
            if (userOpt.isEmpty()) {
                return null;
            }
            var user = userOpt.get();
            // 部门
            String deptName = "";
            if (deptId != null) {
                var deptOpt = deptService.getById(deptId);
                if (deptOpt.isEmpty()) {
                    return null;
                }
                deptName = deptOpt.get().getName();
                var depts = userDeptService.getDepts(userId);
                if (depts == null || depts.stream().noneMatch(dept -> dept.getDeptId().equals(deptId))) {
                    return null;
                }
            }

            var authUser = new AuthUser();
            authUser.setId(user.getId());
            authUser.setLoginName(user.getLoginName());
            authUser.setUserName(user.getUserName());
            authUser.setAdmin(user.getAdmin());
            authUser.setDeptId(deptId);
            authUser.setDeptName(deptName);
            authUser.setExtra(UserService.getSafeExtra(user));

            return authUser;
        }, config.getUserCacheExpireSeconds());

        if (userModel == null || !Objects.equals(extra, userModel.getExtra())) {
            return null;
        }

        return new AuthUserImpl(userModel, deptService, userDeptService, postService, cache, config);
    }

    /**
     * 用户登录
     */
    @Transactional
    public Result<Token> login(String loginName, String password) {
        if (Str.isEmpty(loginName) || Str.isEmpty(password)) {
            return Result.fail("用户名或密码不能为空");
        }

        // 获取用户
        Optional<User> userOpt = userService.getByLoginName(loginName);
        if (userOpt.isEmpty()) {
            return Result.fail("用户名或密码错误");
        }

        User user = userOpt.get();
        try {
            SafeSetting safeSetting = settingService.getSafeSetting();
            int failedCount = loginLogService.getRecentFailedCount(user.getId(), safeSetting.getPasswordTryLock());
            if (failedCount >= safeSetting.getPasswordTryMax()) {
                loginLogService.logLogin(user.getId(), user.getLoginName(), user.getUserName(), false, LoginLog.LoginType.USER_PASSWORD, "密码错误次数过多");
                return Result.fail("密码错误次数过多，请" + safeSetting.getPasswordTryLock() + "分钟后重试");
            }
        } catch (Exception e) {
            log.warn("检查密码错误次数失败", e);
        }

        // 验证密码
        if (!verifyPassword(user, password)) {
            try {
                loginLogService.logLogin(user.getId(), user.getLoginName(), user.getUserName(), false, LoginLog.LoginType.USER_PASSWORD, "密码错误");
            } catch (Exception e) {
                log.warn("记录登录日志失败", e);
            }
            return Result.fail("用户名或密码错误");
        }

        // 更新登录信息
        user.setLastLogin(LocalDateTime.now());
        userService.update(user);

        // 记录登录日志
        try {
            loginLogService.logLogin(user.getId(), user.getLoginName(), user.getUserName(), true, LoginLog.LoginType.USER_PASSWORD, null);
        } catch (Exception e) {
            log.warn("记录登录日志失败", e);
        }

        // 创建Token
        Token token = SpringUtil.getBean(AuthManager.class).createToken(user.getId(), UserService.getSafeExtra(user));

        // 清除与该用户相关的缓存
        clearAuthCache(user.getId());

        return Result.okData(token);
    }

    /**
     * 修改密码
     */
    public Result<Void> changePassword(Long userId, String oldPassword, String newPassword) {
        if (Str.isEmpty(oldPassword)) {
            return Result.fail("请输入原密码");
        }

        if (Str.isEmpty(newPassword)) {
            return Result.fail("新密码不能为空");
        }

        if (Objects.equals(oldPassword, newPassword)) {
            return Result.fail("新密码不能和原密码一致");
        }

        Optional<User> userOpt = userService.getById(userId);
        if (userOpt.isEmpty()) {
            return Result.fail("用户不存在");
        }

        User user = userOpt.get();

        if (!verifyPassword(user, oldPassword)) {
            return Result.fail("原密码错误");
        }

        user.setPassword(hashPassword(newPassword));
        user.setNeedChangePassword(false);
        userService.update(user);
        clearAuthCache(userId);

        return Result.OK;
    }

    /**
     * 用户登出
     */
    @Override
    public void logout(Long userId) {
        clearAuthCache(userId);
    }

    /**
     * 重置密码
     */
    public void resetPassword(Long userId, String newPassword, FilterOptions filterOptions) {
        if (Str.isEmpty(newPassword)) {
            throw new BizException("新密码不能为空");
        }

        var userOpt = userService.getById(userId, filterOptions);
        if (userOpt.isEmpty()) {
            throw new BizException("用户不存在");
        }

        User user = userOpt.get();
        user.setPassword(hashPassword(newPassword));
        user.setNeedChangePassword(true);
        userService.update(user);
        clearAuthCache(userId);
    }

    /**
     * 密码加密
     */
    public String hashPassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * 获取授权缓存前缀
     */
    public String getAuthCachePrefix(Long userId) {
        return config.getUserCachePrefix() + userId + ":";
    }

    /**
     * 清除授权用户缓存
     */
    public void clearAuthCache(Long userId) {
        log.debug("清除用户{}的授权缓存", userId);
        cache.deletePattern(getAuthCachePrefix(userId));
    }

    /**
     * 验证密码
     */
    public boolean verifyPassword(User user, String password) {
        return passwordEncoder.matches(password, user.getPassword());
    }

    /**
     * 生成随机密码
     */
    public static String randomPassword(int length) {
        if (length <= 0) length = 8;

        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        return random.ints(length, 0, chars.length()).mapToObj(chars::charAt).collect(StringBuilder::new, StringBuilder::append, StringBuilder::append).toString();
    }

    /**
     * 生成随机密码 - 默认8位
     */
    public static String randomPassword() {
        return randomPassword(8);
    }

    /**
     * 获取缓存
     */
    private ICache getCache() {
        return cache == null ? NoneCache.INSTANCE : cache;
    }
}
