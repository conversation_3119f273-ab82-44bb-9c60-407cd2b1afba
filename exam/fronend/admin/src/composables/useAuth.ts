import type { RefreshTokenReq, TokenRes, UserInfo } from '~/api/common/auth'
import { getPerms as getPerms<PERSON>pi, getUserInfo as getUserInfo<PERSON>pi, logout as logoutApi, refreshToken as refreshTokenApi } from '~/api/common/auth'

// 单例缓存token
let token: TokenRes | null = null
// 单例缓存用户信息
const user = ref<UserInfo | null>(null)
// 当前登录科室
const dept = useStorage<number | null>('dept', null)
// 权限数据
const perms = ref<string[] | null>(null)
// 本地token存储key
const storageKey: string = 'token'
// 简单的混淆密钥
const encodeKey = 'LQKDOexoKCamgpc5XqHd'

// 简单的数据混淆
function encodeData(data: string): string {
  let result = ''
  for (let i = 0; i < data.length; i++) {
    const keyChar = encodeKey.charCodeAt(i % encodeKey.length)
    const dataChar = data.charCodeAt(i)
    result += String.fromCharCode(dataChar ^ keyChar)
  }
  return btoa(result) // 转换为base64
}

// 简单的数据解混淆
function decodeData(encode: string): string | null {
  try {
    const data = atob(encode)
    let result = ''
    for (let i = 0; i < data.length; i++) {
      const keyChar = encodeKey.charCodeAt(i % encodeKey.length)
      const dataChar = data.charCodeAt(i)
      result += String.fromCharCode(dataChar ^ keyChar)
    }
    return result
  }
  catch {
    return null
  }
}

// 获取token
function getToken(): TokenRes | null {
  if (token)
    return token

  const saved = localStorage.getItem(storageKey)
  if (!saved)
    return null

  try {
    const data = decodeData(saved)
    if (!data) {
      localStorage.removeItem(storageKey)
      return null
    }

    token = JSON.parse(data)
    return token || null
  }
  catch {
    localStorage.removeItem(storageKey)
    return null
  }
}

// 设置token
function setToken(newToken: TokenRes): void {
  token = newToken
  const data = encodeData(JSON.stringify(newToken))
  localStorage.setItem(storageKey, data)
  user.value = null
}

// 获取访问token
function getAccessToken(): string | null {
  return getToken()?.accessToken || null
}

// 刷新token
async function refreshToken(): Promise<boolean> {
  const refreshToken = getToken()?.refreshToken
  if (!refreshToken)
    return false

  try {
    const result = await refreshTokenApi({ refreshToken } as RefreshTokenReq)
    if (result) {
      setToken(result.data)
      return true
    }

    return false
  }
  catch {
    return false
  }
}

// 清除token
function clearToken(): void {
  token = null
  localStorage.removeItem(storageKey)
  user.value = null
  dept.value = null
}

// 获取当前用户信息(远程或本地缓存)
async function getUserInfo(): Promise<Ref<UserInfo | null>> {
  if (!user.value) {
    try {
      const userInfo = await getUserInfoApi()
      const permData = await getPermsApi()
      user.value = userInfo.data
      perms.value = permData.data
      return user
    }
    catch { }
  }

  return user
}

// 获取权限数据
function getPerms(): Ref<string[] | null> {
  return perms
}

// 判断是否有指定权限
function hasPerm(code: string | string[]) {
  if (!code)
    return true

  const values = Array.isArray(code) ? code : [code]
  if (perms.value) {
    if (perms.value.includes('*')) {
      return true
    }

    for (const item of values) {
      const itemPrefix = `${item}:`
      if (perms.value.some(perm => perm === item || perm.startsWith(itemPrefix))) {
        return true
      }
    }
  }
  console.warn('未初始化权限数据')

  return false
}

// 登出(清理token等)
async function logout(): Promise<void> {
  try {
    await logoutApi()
  }
  catch {
  }

  clearToken()
}

// 获取当前登录用户
function getUser(): UserInfo | null {
  return user.value
}

// 获取当前登录科室
function getDept(): number | null {
  return dept.value ? Number(dept.value) : null
}

// 设置当前登录科室
function setDept(newDept: number | null) {
  dept.value = newDept
}

export function useAuth() {
  return {
    getUser,
    getUserInfo,
    getPerms,
    hasPerm,
    getAccessToken,
    setToken,
    refreshToken,
    clearToken,
    getDept,
    setDept,
    logout,
  }
}
