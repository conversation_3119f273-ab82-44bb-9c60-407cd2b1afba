<script setup lang="ts">
definePage({
  meta: {
    title: '质量抽检',
    menu: true,
    order: 2,
    icon: 'ic:outline-high-quality',
  },
})
</script>

<template>
<p>1. 抽检规则配置：支持按时间（如每日 / 每周）、报告类型（个检 / 团检）、科室设置抽检比例（如 10%）及重点抽检项（如报告规范性、结论准确性）；</p>
<p>2. 待抽检列表生成：系统按规则随机抽取体检报告，生成待抽检列表，标注报告关键信息（客户姓名、体检号、总检医生）；</p>
<p>3. 抽检结果判定：质控人员对照标准检查报告内容，记录抽检结果（合格 / 不合格），不合格项需填写整改意见；</p>
<p>4. 质控报告生成：定期汇总抽检数据（合格率、常见问题类型），生成质量控制报告，为体检质量改进提供依据</p>

检后阶段 “质量控制抽检（系统按规则抽取报告检查，生成质控报告，助力质量改进）”；检查科室 “全流程质量监控（与抽检协同，保障服务质量稳定）”
</template>
