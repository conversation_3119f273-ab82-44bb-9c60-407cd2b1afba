<script setup lang="ts">
definePage({
  meta: {
    title: '参数管理',
    menu: true,
    order: 4,
    icon: 'codicon:symbol-parameter',
  },
})
</script>

<template>
<p>1. 系统基础参数配置：配置分布式数据库同步频率、数据备份周期、规则引擎触发阈值（如异常结果触发复查提醒的时间阈值）；</p>
<p>2. 参数校验：修改参数时自动校验合理性（如同步频率不得小于 1 分钟、备份周期不得短于 1 天），不满足则禁止保存；</p>
<p>3. 参数日志：记录参数修改日志（修改人、时间、原参数值、新参数值），支持按参数类型查询；</p>
<p>4. 参数生效监控：监控参数生效状态，未正常生效时触发提醒</p>

系统技术底座 “通过分布式数据库实现多科室数据实时同步，以规则引擎驱动流程自动化”🔶21-2
</template>
