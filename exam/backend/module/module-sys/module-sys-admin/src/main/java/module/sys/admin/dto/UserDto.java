package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import module.sys.entity.User;
import module.sys.model.Sex;

import java.time.LocalDate;
import java.util.List;

public class UserDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        @NotBlank(message = "登录账号不能为空")
        private String loginName;
        @NotBlank(message = "用户姓名不能为空")
        private String userName;
        // HIS编号
        private String hisCode;
        // 密码不能加（注意）
        // private String password;
        // 性别
        private Sex sex;
        // 手机号码
        private String phone;
        // 邮箱
        private String email;
        // 身份证号
        private String idNo;
        // 职称字典ID
        private Long titleDictId;
        // 职称字典名称
        private String titleDictName;
        // 证书编号
        private String certificateNo;
        // 入职日期
        private LocalDate entryDate;
        // 人员类别字典ID
        private Long categoryDictId;
        // 人员类别字典名称
        private String categoryDictName;
        // 人员图像
        private String image;
        // 人员简介
        private String intro;
        @NotNull(message = "用户状态不能为空")
        private User.UserStatus status;
        private List<String> deptPosts;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class InfoDto {
        private Long id;
        private String loginName;
        private String userName;
        private Sex sex;
        private String titleDictName;
        private String phone;
        private String email;
        private User.UserStatus status;
        private List<String> deptPosts;
    }
}
