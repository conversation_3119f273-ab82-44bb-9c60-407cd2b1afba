package infra.backup.core.database.impl;

import infra.backup.config.BackupConfigProperties;
import infra.backup.core.database.AbstractDatabaseBackup;
import infra.backup.exception.BackupException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MySQL数据库备份服务
 */
@Slf4j
public class MySqlBackupService extends AbstractDatabaseBackup {

    private static final String MYSQL_URL_PATTERN = "^jdbc:mysql://.*";
    private static final int COMMAND_TIMEOUT_MINUTES = 60; // 命令超时时间

    @Override
    public boolean supports(String url) {
        return StringUtils.hasText(url) && url.matches(MYSQL_URL_PATTERN);
    }

    @Override
    public File backup(BackupConfigProperties.ConnectionConfig config) {
        log.info("开始备份MySQL数据库，URL: {}", config.getUrl());

        File tempFile = null;
        Process process = null;

        try {
            // 创建临时文件
            tempFile = File.createTempFile("mysql_backup_", ".sql");
            log.debug("创建临时备份文件: {}", tempFile.getAbsolutePath());

            // 解析连接信息
            DatabaseConnectionInfo connInfo = parseConnectionUrl(config.getUrl());

            // 构建mysqldump命令
            List<String> command = buildMysqldumpCommand(connInfo, config, tempFile);

            log.debug("执行mysqldump命令: {}", String.join(" ", command));

            // 执行mysqldump命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            process = processBuilder.start();

            // 读取命令输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.debug("mysqldump输出: {}", line);
                }
            }

            // 等待命令完成
            boolean finished = process.waitFor(COMMAND_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            if (!finished) {
                process.destroyForcibly();
                throw new BackupException("mysqldump命令执行超时");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("mysqldump命令执行失败，退出码: {}, 输出: {}", exitCode, output);
                throw new BackupException("mysqldump命令执行失败，退出码: " + exitCode);
            }

            log.info("MySQL数据库备份完成，文件大小: {} bytes", tempFile.length());
            return tempFile;

        } catch (Exception e) {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                log.debug("清理临时文件: {}, 删除结果: {}", tempFile.getAbsolutePath(), deleted);
            }
            log.error("备份MySQL数据库失败", e);
            throw new BackupException("备份MySQL数据库失败: " + e.getMessage(), e);
        } finally {
            if (process != null && process.isAlive()) {
                process.destroyForcibly();
            }
        }
    }

    /**
     * 数据库连接信息
     */
    private static class DatabaseConnectionInfo {
        String host;
        int port;
        String database;

        DatabaseConnectionInfo(String host, int port, String database) {
            this.host = host;
            this.port = port;
            this.database = database;
        }
    }

    /**
     * 解析数据库连接URL
     */
    private DatabaseConnectionInfo parseConnectionUrl(String url) {
        try {
            // 格式：**************************************
            String cleanUrl = url.replace("jdbc:mysql://", "");

            // 分离主机端口和数据库部分
            String[] parts = cleanUrl.split("/", 2);
            if (parts.length < 2) {
                throw new BackupException("无效的数据库URL格式: " + url);
            }

            String hostPort = parts[0];
            String dbPart = parts[1];

            // 解析主机和端口
            String host;
            int port = 3306; // 默认端口

            if (hostPort.contains(":")) {
                String[] hostPortParts = hostPort.split(":");
                host = hostPortParts[0];
                port = Integer.parseInt(hostPortParts[1]);
            } else {
                host = hostPort;
            }

            // 解析数据库名称（去掉参数）
            String database = dbPart;
            int paramIndex = database.indexOf('?');
            if (paramIndex > 0) {
                database = database.substring(0, paramIndex);
            }

            return new DatabaseConnectionInfo(host, port, database);

        } catch (Exception e) {
            throw new BackupException("解析数据库URL失败: " + url, e);
        }
    }

    /**
     * 构建mysqldump命令
     */
    private List<String> buildMysqldumpCommand(DatabaseConnectionInfo connInfo,
                                               BackupConfigProperties.ConnectionConfig config,
                                               File outputFile) {
        List<String> command = new ArrayList<>();

        // 基本命令
        command.add("mysqldump");

        // 连接参数
        command.add("--host=" + connInfo.host);
        command.add("--port=" + connInfo.port);
        command.add("--user=" + config.getUsername());
        command.add("--password=" + config.getPassword());

        // 备份选项
        command.add("--single-transaction");  // 保证数据一致性
        command.add("--routines");           // 包含存储过程和函数
        command.add("--triggers");           // 包含触发器
        command.add("--events");             // 包含事件
        command.add("--add-drop-table");     // 添加DROP TABLE语句
        command.add("--add-locks");          // 添加锁定语句
        command.add("--disable-keys");       // 禁用键检查以提高导入速度
        command.add("--extended-insert");    // 使用扩展INSERT语法
        command.add("--quick");              // 快速检索行
        command.add("--lock-tables=false");  // 不锁定表
        command.add("--set-charset");        // 设置字符集
        command.add("--default-character-set=utf8mb4"); // 使用utf8mb4字符集

        // 添加注释信息
        command.add("--comments");
        command.add("--dump-date");

        // 如果配置了排除表，添加忽略表选项
        if (config.getExcludeTables() != null && !config.getExcludeTables().isEmpty()) {
            for (String excludeTable : config.getExcludeTables()) {
                // 处理通配符
                if (excludeTable.contains("*") || excludeTable.contains("?")) {
                    log.warn("mysqldump不支持通配符排除表，跳过: {}", excludeTable);
                } else {
                    command.add("--ignore-table=" + connInfo.database + "." + excludeTable);
                }
            }
        }

        // 输出文件
        command.add("--result-file=" + outputFile.getAbsolutePath());
        // 数据库名称
        command.add(connInfo.database);
        return command;
    }


}