package infra.sse.core;

import infra.core.text.Str;
import infra.sse.config.SseConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.SmartLifecycle;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.*;

/**
 * SSE连接管理
 */
@Slf4j
public class SseConnectionManager implements DisposableBean, SmartLifecycle {

    private final SseConfigProperties config;
    private final ConcurrentHashMap<Long, Set<ConnectionInfo>> userConnections = new ConcurrentHashMap<>();
    private final ScheduledExecutorService heartbeatExecutor;
    private volatile boolean isDestroying = false;
    private volatile boolean isRunning;

    public SseConnectionManager(SseConfigProperties config) {
        if (config == null) {
            throw new IllegalArgumentException("SSE配置不能为空");
        }
        if (config.getTimeoutMin() <= 0) {
            throw new IllegalArgumentException("SSE超时时间必须大于0");
        }
        if (config.getHeartbeatInterval() <= 0) {
            throw new IllegalArgumentException("心跳间隔必须大于0");
        }

        this.config = config;
        this.heartbeatExecutor = Executors.newScheduledThreadPool(1,
                Thread.ofVirtual().name("sse-heartbeat-", 0).factory());

        startHeartbeat();
        this.isRunning = true;
    }

    /**
     * 创建SSE连接
     */
    public SseEmitter createConnection(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        // 正在关闭，拒绝新连接
        if (isDestroying) {
            return null;
        }

        SseEmitter emitter = new SseEmitter(config.getTimeoutMin() * 60 * 1000);
        ConnectionInfo connectionInfo = new ConnectionInfo(userId, emitter, UUID.randomUUID().toString());

        setupEmitterCallbacks(userId, connectionInfo);
        addConnection(userId, connectionInfo);

        if (!sendEvent(connectionInfo, "connected", Map.of(
                "userId", userId,
                "sessionId", connectionInfo.sessionId()))) {
            return null;
        }

        log.debug("用户 {} 建立SSE连接，会话ID: {}", userId, connectionInfo.sessionId());
        return emitter;
    }

    /**
     * 发送事件消息给指定用户
     */
    public boolean sendEvent(Long userId, String eventName, Object message) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (Str.isEmpty(eventName)) {
            throw new IllegalArgumentException("事件名不能为空");
        }

        Set<ConnectionInfo> connections = userConnections.get(userId);
        if (connections == null || connections.isEmpty()) {
            return false;
        }

        boolean success = false;

        Iterator<ConnectionInfo> iterator = connections.iterator();
        while (iterator.hasNext()) {
            ConnectionInfo connection = iterator.next();
            if (sendEvent(connection, eventName, message)) {
                success = true;
            } else {
                iterator.remove();
            }
        }

        return success;
    }

    /**
     * 获取在线用户数
     */
    public int getOnlineUserCount() {
        return userConnections.size();
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        Set<ConnectionInfo> connections = userConnections.get(userId);
        return connections != null && !connections.isEmpty();
    }

    /**
     * 获取所有在线用户ID
     */
    public Set<Long> getOnlineUserIds() {
        return new HashSet<>(userConnections.keySet());
    }

    /**
     * 添加连接
     */
    private void addConnection(Long userId, ConnectionInfo connectionInfo) {
        Set<ConnectionInfo> connections = userConnections.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet());
        connections.add(connectionInfo);
    }

    /**
     * 设置Emitter回调
     */
    private void setupEmitterCallbacks(Long userId, ConnectionInfo connectionInfo) {
        SseEmitter emitter = connectionInfo.emitter();
        emitter.onError(ex -> removeConnection(userId, connectionInfo));
    }

    /**
     * 移除连接
     */
    private void removeConnection(Long userId, ConnectionInfo connectionInfo) {
        Set<ConnectionInfo> connections = userConnections.get(userId);
        if (connections != null) {
            boolean removed = connections.remove(connectionInfo);
            if (!removed)
                return;

            log.debug("移除用户 {} 会话（ID:{}）", userId, connectionInfo.sessionId());
            if (connections.isEmpty()) {
                if (userConnections.remove(userId) != null) {
                    log.info("已清理用户 {} 的会话，当前在线 {} 用户", userId, userConnections.size());
                }
            }
        }
    }

    /**
     * 发送消息
     */
    private boolean sendEvent(ConnectionInfo connection, String eventName, Object data) {
        try {
            connection.emitter().send(SseEmitter.event()
                    .name(eventName)
                    .data(data));
            return true;
        } catch (Exception _) {
            log.debug("客户端 {} 发送失败", connection.sessionId());
            removeConnection(connection.userId(), connection);
            return false;
        }
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (isDestroying || userConnections.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        Map<String, Object> heartbeat = Map.of(
                "type", "heartbeat",
                "time", currentTime
        );

        try {
            userConnections.forEach((userId, value) ->
                    Thread.ofVirtual().start(() ->
                            value.forEach(conn -> sendEvent(conn, "heartbeat", heartbeat))));
        } catch (Exception _) {
        }
    }

    /**
     * 启动心跳
     */
    private void startHeartbeat() {
        heartbeatExecutor.scheduleWithFixedDelay(
                this::sendHeartbeat,
                config.getHeartbeatInterval(),
                config.getHeartbeatInterval(),
                TimeUnit.SECONDS
        );
    }

    /**
     * 关闭所有连接
     */
    private void closeAllConnections() {
        if (userConnections.isEmpty()) {
            return;
        }

        log.info("开始关闭 {} 个用户的SSE连接", userConnections.size());

        List<CompletableFuture<Void>> closeTasks = new ArrayList<>();
        userConnections.forEach((userId, connections) -> {
            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> connections.forEach(conn -> {
                try {
                    conn.emitter().complete();
                } catch (Exception e) {
                    log.debug("关闭用户 {} 连接时出错: {}", userId, e.getMessage());
                }
            }));
            closeTasks.add(task);
        });

        try {
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    closeTasks.toArray(new CompletableFuture[0])
            );
            allTasks.get(5, TimeUnit.SECONDS);
            log.info("所有SSE连接已正常关闭");
        } catch (TimeoutException e) {
            log.warn("SSE连接关闭超时，强制清理");
        } catch (Exception e) {
            log.warn("关闭SSE连接时出错: {}", e.getMessage());
        }

        userConnections.clear();
    }

    @Override
    public void start() {
    }

    @Override
    public void stop() {
        performShutdown();
    }

    @Override
    public boolean isRunning() {
        return isRunning && !isDestroying;
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE - 1000;
    }

    /**
     * 执行关闭操作
     */
    private void performShutdown() {
        if (isDestroying) {
            return;
        }

        log.debug("开始关闭SSE连接管理器");
        isDestroying = true;
        isRunning = false;

        try {
            heartbeatExecutor.shutdown();
            if (!heartbeatExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                heartbeatExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            heartbeatExecutor.shutdownNow();
        }

        closeAllConnections();
        log.info("SSE连接管理器已清理完成");
    }

    /**
     * 销毁资源
     */
    @Override
    public void destroy() {
        if (!isDestroying) {
            performShutdown();
        }
    }

    /**
     * 连接信息内部类
     */
    private record ConnectionInfo(Long userId, SseEmitter emitter, String sessionId) {
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ConnectionInfo that = (ConnectionInfo) o;
            return Objects.equals(sessionId, that.sessionId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(sessionId);
        }
    }
}
