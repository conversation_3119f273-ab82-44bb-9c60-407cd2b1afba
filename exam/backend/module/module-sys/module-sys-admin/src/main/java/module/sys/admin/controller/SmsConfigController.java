package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.auth.core.IUser;
import infra.auth.web.UserContext;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.SmsConfigDto;
import module.sys.entity.SmsConfig;
import module.sys.service.SmsConfigService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "短信配置")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/smsConfig")
public class SmsConfigController {
    private final SmsConfigService smsConfigService;

    @Operation(summary = "短信配置列表")
    @GetMapping("/list")
    @Perm("sys:smsConfig")
    public Result<PageResult<SmsConfig>> list(String type, String key, Boolean status, PageParam pageParam) {
        var result = smsConfigService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                w.like("messageId".equals(type), "message_id", keyTrim);
                w.like("contentTemplate".equals(type), "content_template", keyTrim);
                w.like("supplierTemplateCode".equals(type), "supplier_template_code", keyTrim);
            }
            w.eq(status != null, "disabled", status);
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "编辑短信配置")
    @GetMapping("/edit")
    @Perm("sys:smsConfig:edit")
    public Result<SmsConfigDto.EditDto> getEdit(Long id) {
        var smsConfigOpt = smsConfigService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(smsConfigOpt.orElse(null), new SmsConfigDto.EditDto())
        );
    }

    @Operation(summary = "编辑短信配置")
    @PostMapping("/edit")
    @AuditLog(code = "sys:smsConfig:edit", value = "短信配置-编辑")
    @Perm("sys:smsConfig:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid SmsConfigDto.EditDto dto) {
        var smsConfigOpt = smsConfigService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (smsConfigOpt.isEmpty()) {
            return Result.fail("短信配置不存在");
        }

        IUser currentUser = UserContext.getCurrentUser();
        SmsConfig smsConfig = smsConfigOpt.get();
        smsConfig.setSupplierTemplateCode(dto.getSupplierTemplateCode());
        smsConfig.setSort(dto.getSort());

        if (currentUser != null) {
            smsConfig.setUpdateBy(currentUser.getId());
            smsConfig.setUpdateByName(currentUser.getUserName());
            smsConfig.setUpdateTime(LocalDateTime.now());
        }

        return smsConfigService.update(smsConfig) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "sys:smsConfig:enable", value = "短信配置-启用")
    @Perm("sys:smsConfig:enable")
    public Result<Void> enableSmsConfig(@RequestBody List<Long> ids) {
        smsConfigService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "sys:smsConfig:disable", value = "短信配置-禁用")
    @Perm("sys:smsConfig:disable")
    public Result<Void> disableSmsConfig(@RequestBody List<Long> ids) {
        smsConfigService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }
}
