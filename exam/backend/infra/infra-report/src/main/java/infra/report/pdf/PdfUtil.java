package infra.report.pdf;

import infra.core.text.Str;
import lombok.extern.slf4j.Slf4j;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.StringTemplateResolver;

import java.io.OutputStream;
import java.util.Locale;

/**
 * PDF工具类 - 提供简便的PDF生成方法
 */
@Slf4j
public class PdfUtil {
    private static final TemplateEngine htmlTemplateEngine;

    static {
        // 初始化 htmlTemplateEngine
        try {
            StringTemplateResolver templateResolver = new StringTemplateResolver();
            templateResolver.setTemplateMode(TemplateMode.HTML);
            templateResolver.setCacheable(false);

            htmlTemplateEngine = new TemplateEngine();
            htmlTemplateEngine.setTemplateResolver(templateResolver);

            log.info("htmlTemplateEngine 初始化完成");
        } catch (Exception e) {
            log.error("htmlTemplateEngine 初始化失败", e);
            throw new ExceptionInInitializerError("htmlTemplateEngine 初始化失败: " + e.getMessage());
        }
    }


    /**
     * 从HTML生成PDF
     *
     * @param html HTML内容
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html) {
        return htmlToPdf(html, new PdfRenderOptions());
    }

    /**
     * 从HTML生成PDF
     *
     * @param html    HTML内容
     * @param options PDF渲染选项
     * @return PDF字节数组
     */
    public static byte[] htmlToPdf(String html, PdfRenderOptions options) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (options == null) {
            options = new PdfRenderOptions();
        }

        return HtmlToPdfGenerator.generatePdf(html, options);
    }

    /**
     * 从HTML生成PDF到输出流
     *
     * @param html         HTML内容
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, OutputStream outputStream) {
        htmlToPdf(html, new PdfRenderOptions(), outputStream);
    }

    /**
     * 从HTML生成PDF到输出流
     *
     * @param html         HTML内容
     * @param options      PDF渲染选项
     * @param outputStream 输出流
     */
    public static void htmlToPdf(String html, PdfRenderOptions options, OutputStream outputStream) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }
        if (options == null) {
            options = new PdfRenderOptions();
        }

        try {
            byte[] pdfBytes = HtmlToPdfGenerator.generatePdf(html, options);
            outputStream.write(pdfBytes);
            outputStream.flush();
        } catch (Exception e) {
            log.error("写入PDF到输出流失败", e);
            throw new RuntimeException("写入PDF到输出流失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用模板和数据生成PDF
     *
     * @param html    HTML模板内容
     * @param data    模板数据
     * @param options PDF渲染选项
     * @param <T>     数据类型
     * @return PDF字节数组
     */
    public static <T> byte[] htmlToPdf(String html, T data, PdfRenderOptions options) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("HTML模板内容不能为空");
        }
        if (options == null) {
            options = new PdfRenderOptions();
        }

        try {
            Context context = new Context(Locale.getDefault());
            if (data != null) {
                context.setVariable("data", data);
            }

            String processedHtml = htmlTemplateEngine.process(html, context);
            return HtmlToPdfGenerator.generatePdf(processedHtml, options);

        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new RuntimeException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用模板和数据生成PDF
     *
     * @param html HTML模板内容
     * @param data 模板数据
     * @param <T>  数据类型
     * @return PDF字节数组
     */
    public static <T> byte[] htmlToPdf(String html, T data) {
        return htmlToPdf(html, data, new PdfRenderOptions());
    }

    /**
     * 使用模板和数据生成PDF到输出流
     *
     * @param html         HTML模板内容
     * @param data         模板数据
     * @param outputStream 输出流
     * @param <T>          数据类型
     */
    public static <T> void htmlToPdf(String html, T data, OutputStream outputStream) {
        htmlToPdf(html, data, new PdfRenderOptions(), outputStream);
    }

    /**
     * 使用模板和数据生成PDF到输出流
     *
     * @param html         HTML模板内容
     * @param data         模板数据
     * @param options      PDF渲染选项
     * @param outputStream 输出流
     * @param <T>          数据类型
     */
    public static <T> void htmlToPdf(String html, T data, PdfRenderOptions options, OutputStream outputStream) {
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        try {
            byte[] pdfBytes = htmlToPdf(html, data, options);
            outputStream.write(pdfBytes);
            outputStream.flush();
        } catch (Exception e) {
            log.error("写入模板PDF到输出流失败", e);
            throw new RuntimeException("写入模板PDF到输出流失败: " + e.getMessage(), e);
        }
    }

}
