package infra.core.security;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;

/**
 * SM4 对称加密工具类 (CBC模式)
 * 国产密码算法，适用于商用密码场景
 * 线程安全：每次加密使用独立 Cipher 实例
 */
public final class Sm4 {
    private static final String ALGORITHM = "SM4/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "SM4";
    private static final String PROVIDER = "BC";
    private static final int IV_LENGTH = 16; // 固定16字节
    private static final int SM4_KEY_LENGTH = 16; // SM4密钥长度128位=16字节
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    static {
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private Sm4() {
    }

    /**
     * 生成 SM4-128 密钥 (Base64编码)
     */
    public static String generateKey() {
        byte[] key = new byte[SM4_KEY_LENGTH];
        SECURE_RANDOM.nextBytes(key);
        return Base64.getEncoder().encodeToString(key);
    }

    /**
     * 加密 (返回Base64字符串，格式: IV + 密文)
     *
     * @param plaintext 明文
     * @param base64Key Base64编码的SM4密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plaintext, String base64Key) {
        if (plaintext == null || base64Key == null) {
            throw new Sm4CryptoException("明文和密钥均不可为空");
        }

        try {
            byte[] iv = generateIv();
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM, PROVIDER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return encodeResult(iv, ciphertext);
        } catch (Exception e) {
            throw new Sm4CryptoException("SM4加密失败", e);
        }
    }

    /**
     * 确定性加密
     * 相同明文总是产生相同密文，但安全性较低
     *
     * @param plaintext 明文
     * @param base64Key Base64编码的AES密钥
     * @return 加密后的Base64字符串
     */
    public static String encryptDeterministic(String plaintext, String base64Key) {
        if (plaintext == null || base64Key == null) {
            throw new Sm4CryptoException("明文和密钥均不可为空");
        }

        try {
            byte[] deterministicIv = generateDeterministicIv(plaintext, base64Key);
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM, PROVIDER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(deterministicIv));

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return encodeResult(deterministicIv, ciphertext);
        } catch (Exception e) {
            throw new Sm4CryptoException("SM4确定性加密失败", e);
        }
    }

    /**
     * 解密
     *
     * @param base64Ciphertext Base64格式的加密数据 (IV + 密文)
     * @param base64Key        Base64编码的SM4密钥
     * @return 解密后的明文
     */
    public static String decrypt(String base64Ciphertext, String base64Key) {
        if (base64Ciphertext == null || base64Key == null) {
            throw new Sm4CryptoException("密文和密钥均不可为空");
        }

        try {
            byte[] combined = Base64.getDecoder().decode(base64Ciphertext);
            validateCombinedData(combined);

            byte[] iv = extractIv(combined);
            byte[] ciphertext = extractCiphertext(combined, iv.length);
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM, PROVIDER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] plaintext = cipher.doFinal(ciphertext);
            return new String(plaintext, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new Sm4CryptoException("SM4解密失败", e);
        }
    }

    /**
     * ECB模式加密 (不推荐用于生产环境，仅用于兼容性需求)
     *
     * @param plaintext 明文
     * @param base64Key Base64编码的SM4密钥
     * @return 加密后的Base64字符串
     */
    public static String encryptEcb(String plaintext, String base64Key) {
        if (plaintext == null || base64Key == null) {
            throw new Sm4CryptoException("明文和密钥均不可为空");
        }

        try {
            SecretKey secretKey = parseKey(base64Key);
            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", PROVIDER);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(ciphertext);
        } catch (Exception e) {
            throw new Sm4CryptoException("SM4 ECB加密失败", e);
        }
    }

    /**
     * ECB模式解密 (不推荐用于生产环境，仅用于兼容性需求)
     *
     * @param base64Ciphertext Base64格式的加密数据
     * @param base64Key        Base64编码的SM4密钥
     * @return 解密后的明文
     */
    public static String decryptEcb(String base64Ciphertext, String base64Key) {
        if (base64Ciphertext == null || base64Key == null) {
            throw new Sm4CryptoException("密文和密钥均不可为空");
        }

        try {
            byte[] ciphertext = Base64.getDecoder().decode(base64Ciphertext);
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance("SM4/ECB/PKCS5Padding", PROVIDER);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] plaintext = cipher.doFinal(ciphertext);
            return new String(plaintext, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new Sm4CryptoException("SM4 ECB解密失败", e);
        }
    }

    /**
     * 验证密钥格式
     *
     * @param base64Key Base64编码的密钥
     * @return 是否为有效的SM4密钥
     */
    public static boolean isValidKey(String base64Key) {
        try {
            parseKey(base64Key);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 生成随机IV
    private static byte[] generateIv() {
        byte[] iv = new byte[IV_LENGTH];
        SECURE_RANDOM.nextBytes(iv);
        return iv;
    }

    /**
     * 生成确定性 IV
     */
    private static byte[] generateDeterministicIv(String plaintext, String key) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(plaintext.getBytes(StandardCharsets.UTF_8));
            digest.update(key.getBytes(StandardCharsets.UTF_8));
            byte[] hash = digest.digest();

            // 取前16字节作为IV
            byte[] iv = new byte[IV_LENGTH];
            System.arraycopy(hash, 0, iv, 0, IV_LENGTH);
            return iv;
        } catch (Exception e) {
            throw new Sm4CryptoException("生成确定性IV失败", e);
        }
    }

    // 解析Base64密钥
    private static SecretKey parseKey(String base64Key) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(base64Key);
            if (keyBytes.length != SM4_KEY_LENGTH) {
                throw new Sm4CryptoException("无效密钥长度，SM4密钥必须为128位(16字节)");
            }
            return new SecretKeySpec(keyBytes, KEY_ALGORITHM);
        } catch (IllegalArgumentException e) {
            throw new Sm4CryptoException("无效的Base64密钥格式", e);
        }
    }

    // 编码结果 (IV + 密文)
    private static String encodeResult(byte[] iv, byte[] ciphertext) {
        byte[] combined = new byte[iv.length + ciphertext.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(ciphertext, 0, combined, iv.length, ciphertext.length);
        return Base64.getEncoder().encodeToString(combined);
    }

    // 验证合并数据的有效性
    private static void validateCombinedData(byte[] combined) {
        if (combined.length < IV_LENGTH) {
            throw new Sm4CryptoException("密文数据长度不足，无法提取IV");
        }
    }

    // 提取IV
    private static byte[] extractIv(byte[] combined) {
        byte[] iv = new byte[IV_LENGTH];
        System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
        return iv;
    }

    // 提取密文
    private static byte[] extractCiphertext(byte[] combined, int ivLength) {
        int ciphertextLength = combined.length - ivLength;
        if (ciphertextLength <= 0) {
            throw new Sm4CryptoException("无效的密文数据");
        }

        byte[] ciphertext = new byte[ciphertextLength];
        System.arraycopy(combined, ivLength, ciphertext, 0, ciphertextLength);
        return ciphertext;
    }

    // 自定义SM4加密异常
    public static class Sm4CryptoException extends RuntimeException {
        public Sm4CryptoException(String message) {
            super(message);
        }

        public Sm4CryptoException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
