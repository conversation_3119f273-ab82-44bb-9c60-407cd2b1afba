package infra.domain.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.NonNull;

/**
 * MyBatisPlus拦截器增强器
 */
@Slf4j
@RequiredArgsConstructor
public class DomainMyBatisPlusInterceptorEnhancer implements BeanPostProcessor {
    // 数据库类型
    private final String dbType;

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        if (bean instanceof MybatisPlusInterceptor interceptor) {
            enhanceInterceptor(interceptor, beanName);
        }
        return bean;
    }

    /*
     * 增强加入数据权限拦截器
     */
    private void enhanceInterceptor(MybatisPlusInterceptor interceptor, String beanName) {
        // 分页插件
        if (interceptor.getInterceptors().stream().noneMatch(inner -> inner instanceof PaginationInnerInterceptor)) {
            log.info("[配置] 为MyBatis-Plus拦截器添加分页插件，数据库类型:{}", dbType);
            interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.getDbType(dbType)));
        }

        // 乐观锁插件
        if (interceptor.getInterceptors().stream().noneMatch(inner -> inner instanceof OptimisticLockerInnerInterceptor)) {
            log.info("[配置] 为MyBatis-Plus拦截器添加乐观锁插件，数据库类型:{}", dbType);
            interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        }
    }
}

