package infra.protect.core;

import infra.auth.web.UserContext;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.core.web.IpUtil;
import infra.core.web.ServletUtil;
import infra.protect.annotation.RateLimit;
import infra.protect.config.ProtectConfigProperties;
import infra.protect.exception.RateLimitException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;

import java.lang.reflect.Method;

/**
 * 限流切面
 */
@Aspect
@Slf4j
@RequiredArgsConstructor
public class RateLimitAspect {
    private final IRateLimiter rateLimiter;
    private final ProtectConfigProperties.RateLimitConfig.RateLimitStrategyConfig defaultConfig;

    @Around("@annotation(infra.protect.annotation.RateLimit) || @within(infra.protect.annotation.RateLimit)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();

        // 获取注解，优先方法级别
        RateLimit rateLimit = AnnotationUtils.findAnnotation(method, RateLimit.class);
        if (rateLimit == null) {
            rateLimit = AnnotationUtils.findAnnotation(method.getDeclaringClass(), RateLimit.class);
        }
        if (rateLimit == null) {
            return point.proceed();
        }

        // 构建限流key
        String key = buildRateLimitKey(rateLimit, point);
        // 执行限流检查
        boolean allowed = checkRateLimit(key, rateLimit);

        if (!allowed) {
            // 检查返回类型，如果是Result类型则返回失败结果，否则抛出异常
            String msg = rateLimit.message();
            if (Str.isEmpty(msg)) {
                msg = defaultConfig.getMessage();
            }

            Class<?> returnType = signature.getReturnType();
            if (Result.class.isAssignableFrom(returnType)) {
                return Result.fail(429, msg);
            } else {
                throw new RateLimitException(msg);
            }
        }

        return point.proceed();
    }

    /**
     * 构建限流key
     */
    private String buildRateLimitKey(RateLimit rateLimit, ProceedingJoinPoint point) {
        StringBuilder keyBuilder = new StringBuilder("rate_limit:");

        switch (rateLimit.type()) {
            case IP -> {
                String ip = getClientIp();
                keyBuilder.append("ip:").append(ip);
            }
            case USER -> {
                String userId = getCurrentUserId();
                keyBuilder.append("user:").append(userId != null ? userId : "anonymous");
            }
        }

        // 添加方法标识以避免不同方法间的key冲突
        MethodSignature signature = (MethodSignature) point.getSignature();
        keyBuilder.append(":").append(signature.getDeclaringTypeName())
                .append(".").append(signature.getName());

        return keyBuilder.toString();
    }

    /**
     * 执行限流检查
     */
    private boolean checkRateLimit(String key, RateLimit rateLimit) {
        int count = rateLimit.count();
        if (count <= 0) {
            count = defaultConfig.getCount();
        }
        int window = rateLimit.window();
        if (window <= 0) {
            window = defaultConfig.getWindow();
        }
        return rateLimiter.tryAcquire(key, count, window);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            var request = ServletUtil.getRequest();
            return IpUtil.getClientIp(request);
        } catch (Exception e) {
            log.debug("获取客户端IP失败", e);
        }
        return "unknown";
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            var user = UserContext.getCurrentUser();
            if (user != null) {
                return user.getId().toString();
            }
            return null;
        } catch (Exception e) {
            log.debug("获取当前用户ID失败", e);
            return null;
        }
    }
}