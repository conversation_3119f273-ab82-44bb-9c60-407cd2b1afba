package infra.sse.notice.config;

import infra.sse.core.SseConnectionManager;
import infra.sse.notice.core.INoticeService;
import infra.sse.notice.core.INoticeStorage;
import infra.sse.notice.core.NoticeManager;
import infra.sse.notice.core.EmptyNoticeStorage;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * SSE通知自动配置
 */
@Slf4j
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(prefix = "app.sse.notice", name = "enabled", havingValue = "true")
@ConditionalOnBean({SseConnectionManager.class})
@EnableConfigurationProperties(NoticeConfigProperties.class)
public class NoticeAutoConfiguration {
    @Bean
    @ConditionalOnMissingBean
    public INoticeStorage infraNoticeStorage() {
        log.info("[配置] 未配置INoticeStorage，将不存储任何消息");
        return new EmptyNoticeStorage();
    }

    @Bean
    @ConditionalOnMissingBean({INoticeService.class})
    public INoticeService infraNoticeService(SseConnectionManager connectionManager,
                                             NoticeConfigProperties config,
                                             INoticeStorage storage,
                                             @Autowired(required = false) RedissonClient redissonClient) {
        log.info("[配置] 初始化SSE通知服务");
        return new NoticeManager(connectionManager, storage, config, redissonClient);
    }
}
