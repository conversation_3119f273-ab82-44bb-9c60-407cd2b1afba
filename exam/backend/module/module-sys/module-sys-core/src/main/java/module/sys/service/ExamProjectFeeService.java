package module.sys.service;

import infra.core.exception.BizException;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ExamProjectFee;
import module.sys.mapper.ExamProjectFeeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 体检项目与收费项目关联服务
 */
@Slf4j
@Service
public class ExamProjectFeeService extends ServiceBase<ExamProjectFeeMapper, ExamProjectFee> {
    @Override
    protected void beforeSave(ExamProjectFee entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (exists(w ->
                w.eq("exam_project_id", entity.getExamProjectId())
                        .eq("fee_project_id", entity.getFeeProjectId())
                        .ne(isUpdate, "id", entity.getId()))) {
            throw new BizException("该关联数据已存在");
        }
    }

    /**
     * 获取体检项目的关联收费项目
     */
    public List<ExamProjectFee> getListByProject(Long id) {
        return getListByProject(id, FilterOptions.DEFAULTS);
    }

    /**
     * 获取体检项目的关联收费项目
     */
    public List<ExamProjectFee> getListByProject(Long id, FilterOptions filterOptions) {
        return getList(w -> w.eq("exam_project_id", id), filterOptions);
    }
}