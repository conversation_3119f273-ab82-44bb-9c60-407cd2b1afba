<script setup lang="ts">
import * as api from '~/api/common/auth'
import { useLoading } from '~/composables'
import { getConfig, tips } from '~/utils'

const props = withDefaults(defineProps<{
  submitCenter?: boolean
}>(), {
  submitCenter: false,
})

const router = useRouter()
const formRef = useTemplateRef('formRef')
const { loading, withLoading } = useLoading()

// 密码表单
const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 密码确认验证
function validateConfirmPassword(_rule: any, value: string, callback: any) {
  if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  }
  else {
    callback()
  }
}

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入原密码' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    { validator: validateConfirmPassword },
  ],
}

// 提交修改密码
async function handleSubmit() {
  await formRef.value.validate()
  await withLoading(async () => {
    await api.changePassword(form)
    tips.success('密码修改成功，请重新登录')
    router.replace(getConfig().loginPath)
  })
}

// 重置表单
function resetForm() {
  formRef.value.resetFields()
}
</script>

<template>
  <el-form ref="formRef" :model="form" label-width="90px" :rules="rules" class="pt-6" @submit.prevent="handleSubmit">
    <el-form-item prop="oldPassword" label="原密码" class="w-100">
      <el-input v-model="form.oldPassword" type="password" show-password />
    </el-form-item>
    <el-form-item prop="newPassword" label="新密码" class="w-100">
      <el-input v-model="form.newPassword" type="password" show-password />
    </el-form-item>
    <el-form-item prop="confirmPassword" label="确认密码" class="w-100">
      <el-input v-model="form.confirmPassword" type="password" show-password />
    </el-form-item>
    <div v-if="props.submitCenter" class="flex justify-center p-2">
      <el-button type="primary" :loading="loading" native-type="submit" size="default">
        <template #icon>
          <icon icon="mdi:check" />
        </template>
        确定
      </el-button>
      <el-button size="default" @click="resetForm">
        <template #icon>
          <icon icon="radix-icons:reset" />
        </template>
        重置
      </el-button>
    </div>
    <el-form-item v-else label=" " class="pt-4">
      <el-button type="primary" :loading="loading" native-type="submit" size="default">
        <template #icon>
          <icon icon="mdi:check" />
        </template>
        确定
      </el-button>
      <el-button size="default" @click="resetForm">
        <template #icon>
          <icon icon="radix-icons:reset" />
        </template>
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>
