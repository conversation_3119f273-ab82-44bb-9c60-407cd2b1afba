package infra.protect.core.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import infra.protect.core.IRateLimiter;
import lombok.extern.slf4j.Slf4j;

import java.util.Deque;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.TimeUnit;

/**
 * 基于本地缓存滑动窗口限流器
 * 适用于单机部署场景
 */
@Slf4j
public class LocalRateLimiter implements IRateLimiter {
    private final Cache<String, Deque<Long>> CACHE = Caffeine.newBuilder()
            .expireAfterAccess(2, TimeUnit.HOURS) // 自动清理闲置的key
            .build();

    @Override
    public boolean tryAcquire(String key, int count, int window) {
        Deque<Long> timestamps = CACHE.get(key, k -> new ConcurrentLinkedDeque<>());
        if (timestamps == null) {
            return false;
        }

        long now = System.currentTimeMillis() / 1000;
        long windowStart = now - window;

        // 移除窗口外的时间戳
        while (!timestamps.isEmpty() && timestamps.peekFirst() < windowStart) {
            timestamps.pollFirst();
        }

        // 判断是否超出限制
        if (timestamps.size() < count) {
            timestamps.offerLast(now);
            return true;
        }

        return false;
    }

    @Override
    public long remaining(String key, int count, int window) {
        Deque<Long> timestamps = CACHE.getIfPresent(key);
        if (timestamps == null) {
            return count;
        }

        long now = System.currentTimeMillis() / 1000;
        long windowStart = now - window;

        // 移除窗口外的时间戳
        while (!timestamps.isEmpty() && timestamps.peekFirst() < windowStart) {
            timestamps.pollFirst();
        }

        return Math.max(0, count - timestamps.size());
    }

    @Override
    public void reset(String key) {
        CACHE.invalidate(key);
    }
}
