<script setup lang="ts">
import * as api from '~/api/sys/dict'
import { useDict, useLoading } from '~/composables'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '数据字典',
    perm: 'sys:dict',
    keepAlive: true,
    menu: true,
    order: 2,
  },
})

const { removeSysDict } = useDict()

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const dictFormRef = useTemplateRef('dictFormRef')
const dictValueFormRef = useTemplateRef('dictValueFormRef')
const dictShow = ref()
const dictValueList = ref([])
const dictValueDialogVisible = ref(false)
const dictValueSelected = ref([])
const { loading: valueLoading, withLoading: valueWithLoading } = useLoading()
const { loading: exportLoading, withLoading: exportWithLoading } = useLoading()

const dictCanAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const dictValueCanAction = computed(() => dictValueSelected.value.length > 0 && !valueLoading.value)

// 表单
const query = reactive({
  type: 'name',
  key: '',
})
const initDictData = {
  name: '',
  code: '',
  info: '',
  sort: 0,
}
const initDictValueData = {
  dictId: 0,
  name: '',
  code: '',
  sort: 0,
  disabled: false,
}

// 验证规则
const dictRules = {
  name: [{ required: true, message: '请输入字典名称' }],
  code: [{ required: true, message: '请输入字典编码' }],
}
const dictValueRules = {
  name: [{ required: true, message: '请输入值名称' }],
}

// 加载字典值
async function loadDictValue() {
  if (dictShow.value && dictShow.value.id > 0) {
    await valueWithLoading(async () => {
      const res = await api.listValue(dictShow.value.id)
      dictValueList.value = res.data
      // 每次增删改都会重新请求，这里设置清除字典缓存
      removeSysDict(dictShow.value.code)
    })
  }
  else {
    dictValueList.value = []
  }
}

// 删除
async function handleDictRemove(ids: number[]) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await api.remove(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 显示字典值
async function handleShowDictValues(dict: any) {
  dictShow.value = dict
  dictValueDialogVisible.value = true
  await loadDictValue()
}

// 字典值选中
function handleDictValueSelectionChange(val: any) {
  dictValueSelected.value = val.map((item: any) => item.id)
}

// 操作字典值
async function handleValueBy(ids: number[], fn: any) {
  await valueWithLoading(async () => {
    await fn(ids)
    tips.success()
    await loadDictValue()
  })
}

// 启用字典值
async function handleEnableValue(ids: number[]) {
  await handleValueBy(ids, api.enableValue)
}

// 禁用字典值
async function handleDisableValue(ids: number[]) {
  await handleValueBy(ids, api.disableValue)
}

// 删除字典值
async function handleDictValueRemove(ids: number[]) {
  await handleValueBy(ids, api.removeValue)
}

// 导出字典值
function handleExportDictValue() {
  if (!dictShow.value?.id)
    return

  exportWithLoading(async () => {
    await api.exportValue(dictShow.value.id)
  })
}

// 导入字典值
async function handleImportDictValue(file: File) {
  await api.importValue(dictShow.value.id, file)
}

async function handleImportSuccess() {
  tips.success('导入成功')
  await loadDictValue()
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="名称" value="name" />
              <el-option label="编号" value="code" />
              <el-option label="说明" value="info" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('sys:dict:add')" type="primary" @click="dictFormRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group v-if="$hasPerm('sys:dict:delete')">
          <el-popconfirm title="确认要删除选中的项吗？" placement="bottom" @confirm="handleDictRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!dictCanAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="code" label="编号" width="180" />
      <el-table-column prop="name" label="字典名称" width="350" show-overflow-tooltip>
        <template #default="{ row }">
          <el-button link @click="handleShowDictValues(row)">
            {{ row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="sort" align="center" label="排序值" width="120" />
      <el-table-column prop="info" label="描述说明" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button link @click="handleShowDictValues(row)">
              字典项
            </el-button>
            <el-button v-if="$hasPerm('sys:dict:edit')" link @click="dictFormRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('sys:dict:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleDictRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 字典编辑对话框 -->
  <detail-form
    ref="dictFormRef" class="p-6" title="字典" :init-data="initDictData" :rules="dictRules"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-form-item label="字典名称" prop="name">
      <el-input v-model="dictFormRef.formData.name" placeholder="请输入字典名称" maxlength="31" />
    </el-form-item>
    <el-form-item label="字典编码" prop="code">
      <el-input v-model="dictFormRef.formData.code" placeholder="请输入字典编码" maxlength="31" />
    </el-form-item>
    <el-form-item label="描述说明" prop="info">
      <el-input v-model="dictFormRef.formData.info" type="textarea" placeholder="请输入描述说明" maxlength="63" />
    </el-form-item>
    <el-form-item label="排序" prop="sort">
      <el-input-number v-model="dictFormRef.formData.sort" :min="0" />
    </el-form-item>
  </detail-form>

  <!-- 字典值对话框 -->
  <el-dialog v-model="dictValueDialogVisible" :title="dictShow?.name" width="900px" draggable>
    <div class="px-2 py-5">
      <div class="mb-4 flex items-center justify-between">
        <div>
          <el-button
            v-if="$hasPerm('sys:dict:add')" type="primary"
            @click="dictValueFormRef.add({ dictId: dictShow?.id })"
          >
            <template #icon>
              <icon icon="ep:plus" />
            </template>
            新增字典项
          </el-button>
          <el-button-group class="ml-2">
            <el-button v-if="$hasPerm('sys:dict:valueEnable')" title="启用" :disabled="!dictValueCanAction" @click="handleEnableValue(dictValueSelected)">
              <template #icon>
                <icon icon="codicon:debug-start" />
              </template>
              启用
            </el-button>
            <el-popconfirm v-if="$hasPerm('sys:dict:valueDisable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisableValue(dictValueSelected)">
              <template #reference>
                <el-button title="禁用选中" :disabled="!dictValueCanAction">
                  <template #icon>
                    <icon icon="ant-design:stop-twotone" />
                  </template>
                  禁用
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              v-if="$hasPerm('sys:dict:delete')" title="确认要删除选中项吗？"
              @confirm="handleDictValueRemove(dictValueSelected)"
            >
              <template #reference>
                <el-button :disabled="!dictValueCanAction">
                  <template #icon>
                    <icon icon="ep:delete" />
                  </template>
                  删除选中
                </el-button>
              </template>
            </el-popconfirm>
          </el-button-group>
        </div>
        <div>
          <el-button-group>
            <file-upload
              v-if="$hasPerm('sys:dict:import')" accept=".xlsx,.xls" :max-size="5" :upload="handleImportDictValue" @success="handleImportSuccess"
            >
              <template #default="{ loading, triggerUpload }">
                <el-button round :loading="loading" @click="triggerUpload">
                  <template #icon>
                    <icon icon="lets-icons:import" />
                  </template>
                  导入
                </el-button>
              </template>
            </file-upload>
            <el-button v-if="$hasPerm('sys:dict:export')" round :loading="exportLoading" @click="handleExportDictValue">
              <template #icon>
                <icon icon="material-symbols:upload-rounded" />
              </template>
              导出
            </el-button>
          </el-button-group>
        </div>
      </div>
      <el-table v-loading="valueLoading" :data="dictValueList" @selection-change="handleDictValueSelectionChange">
        <el-table-column type="selection" width="50" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="code" label="编码" width="180" />
        <el-table-column prop="sort" label="排序" width="100" align="center" />
        <el-table-column prop="disabled" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.disabled ? 'danger' : 'success'">
              {{ row.disabled ? '禁用' : '启用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button v-if="$hasPerm('sys:dict:edit')" link @click="dictValueFormRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('sys:dict:delete')" title="确认要删除该项吗？"
              @confirm="handleDictValueRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button @click="dictValueDialogVisible = false">
        关闭
      </el-button>
    </template>
  </el-dialog>

  <!-- 字典值编辑对话框 -->
  <detail-form
    ref="dictValueFormRef" class="p-6" title="字典项" :init-data="initDictValueData" :rules="dictValueRules"
    :get-api="api.getValueEdit" :add-api="api.addValue" :edit-api="api.postValueEdit" @success="loadDictValue"
  >
    <el-form-item label="名称" prop="name">
      <el-input v-model="dictValueFormRef.formData.name" placeholder="请输入值名称" maxlength="63" />
    </el-form-item>
    <el-form-item label="编码" prop="code">
      <el-input v-model="dictValueFormRef.formData.code" placeholder="请输入值编码" maxlength="31" />
    </el-form-item>
    <el-form-item label="排序" prop="sort">
      <el-input-number v-model="dictValueFormRef.formData.sort" :min="0" />
    </el-form-item>
    <el-form-item label="状态" prop="disabled">
      <el-radio-group v-model="dictValueFormRef.formData.disabled">
        <el-radio-button label="启用" :value="false" />
        <el-radio-button label="禁用" :value="true" />
      </el-radio-group>
    </el-form-item>
  </detail-form>
</template>
