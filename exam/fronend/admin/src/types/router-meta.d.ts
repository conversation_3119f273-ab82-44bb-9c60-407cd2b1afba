import 'unplugin-vue-router/types'

declare module 'unplugin-vue-router/types' {
  interface RouteMeta {
    /**
     * 页面标题
     */
    title?: string
    /**
     * 是否为公开页面，无需登录即可访问，默认否
     */
    public?: boolean
    /**
     * 是否保持页面缓存，默认缓存
     */
    keepAlive?: boolean
    /**
     * 进入页面需要的权限
     */
    perm?: string
    /**
     * 排序值，越小越靠前
     */
    order?: number
    /**
     * 图标
     */
    icon?: string
    /**
     * 是否展现在菜单中，默认不显示
     */
    menu?: boolean
    /**
     * 是否显示水印，默认显示
     */
    watermark?: boolean
  }
}
