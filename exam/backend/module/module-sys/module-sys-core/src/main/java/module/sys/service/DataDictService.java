package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.DataDict;
import module.sys.entity.DataDictValue;
import module.sys.mapper.DataDictMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

/**
 * 数据字典服务
 */
@Slf4j
@Service
public class DataDictService extends ServiceBase<DataDictMapper, DataDict> {
    @Autowired
    private DataDictValueService dataDictValueService;

    @Override
    protected void beforeSave(DataDict entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByCode(entity.getCode(), isUpdate ? entity.getId() : null)) {
            throw new BizException("字典编码已存在");
        }
    }

    @Override
    protected Function<QueryWrapper<DataDict>, QueryWrapper<DataDict>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    /**
     * 检查字典编码是否已存在
     */
    public boolean existsByCode(String code, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(w -> {
            w.eq("code", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 获取指定字典下的所有字典值
     */
    public List<DataDictValue> getValues(Long dictId) {
        return dataDictValueService.getListByDict(dictId);
    }

    /**
     * 获取指定code字典下的所有字典值
     */
    public List<DataDictValue> getValuesByCode(String code) {
        Optional<DataDict> dictOpt = getByCode(code);
        if (dictOpt.isEmpty()) {
            throw new BizException("字典不存在");
        }

        return getValues(dictOpt.get().getId());
    }

    /**
     * 根据编码获取字典
     */
    public Optional<DataDict> getByCode(String code) {
        return getFirst(w -> w.eq("code", code));
    }
}
