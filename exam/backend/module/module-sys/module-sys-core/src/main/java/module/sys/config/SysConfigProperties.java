package module.sys.config;

import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * 系统模管理配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@Configuration
@ConfigurationProperties(prefix = "app.module.sys")
public class SysConfigProperties {
    // 新建用户默认密码
    private String defaultPassword;

    @Min(value = 1)
    // 审计日志默认保留数据天数(默认360天)
    private int auditLogDays = 360;
}
