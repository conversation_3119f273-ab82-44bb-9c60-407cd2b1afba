package infra.audit.core;

import infra.audit.config.AuditConfigProperties;
import infra.audit.logger.NoneAuditLogger;
import infra.auth.core.IUser;
import infra.auth.web.UserContext;
import infra.core.web.IpUtil;
import infra.core.text.Str;
import infra.core.web.ServletUtil;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审计日志操作切面(aop)
 */
@Aspect
@Slf4j
@RequiredArgsConstructor
public class AuditLogAspect {
    // 日志记录器
    private final IAuditLogger auditLogger;
    // 配置
    private final AuditConfigProperties config;
    // 缓存模块名称
    private final ConcurrentHashMap<Class<?>, String> moduleCache = new ConcurrentHashMap<>();

    /**
     * 审计切入
     */
    @Around("@annotation(auditLog)")
    public Object logAudit(ProceedingJoinPoint joinPoint, AuditLog auditLog) throws Throwable {
        if (!config.isEnabled() || auditLogger == null || auditLogger instanceof NoneAuditLogger)
            return joinPoint.proceed();

        return AuditLogContext.callWithData(new AuditLogContext.WithData(), () -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            LocalDateTime startTime = LocalDateTime.now();

            try {
                Object result = joinPoint.proceed();
                stopWatch.stop();
                log(auditLog, "", startTime, stopWatch.getTotalTimeMillis(), joinPoint);

                return result;
            } catch (Throwable e) {
                if (stopWatch.isRunning()) {
                    stopWatch.stop();
                }

                log(auditLog, e.getMessage(), startTime, stopWatch.getTotalTimeMillis(), joinPoint);
                throw e;
            }
        });
    }

    /**
     * 记录日志
     */
    private void log(AuditLog auditLog, String errorMsg, LocalDateTime startTime, Long useTime, JoinPoint joinPoint) {
        try {
            // 所需数据
            IUser user = UserContext.getCurrentUser();
            AuditLogContext.WithData data = AuditLogContext.getScopedData();
            HttpServletRequest request = ServletUtil.getRequest();

            // 构建并记录日志
            var operateInfo = OpInfo.builder()
                    // 操作信息
                    .module(getModuleName(auditLog, joinPoint))
                    .code(auditLog.code())
                    .detail(data != null && !Str.isEmpty(data.getDetail()) ? data.getDetail() : auditLog.value())
                    //用户信息
                    .userId(user == null ? 0L : user.getId())
                    .loginName(user == null ? "" : user.getLoginName())
                    .userName(user == null ? "" : user.getUserName())
                    // 请求信息
                    .startTime(startTime)
                    .useTime(useTime)
                    .url(request == null ? "" : request.getRequestURL().toString())
                    .ip(request == null ? "" : IpUtil.getClientIp(request))
                    .success(Str.isEmpty(errorMsg))
                    .error(errorMsg)
                    .data(data == null ? null : data.getData()).build();

            // 存储记录
            auditLogger.log(operateInfo);
        } catch (Exception e) {
            log.error("审计日志记录异常", e);
        }
    }

    /**
     * 获取模块名称
     */
    private String getModuleName(AuditLog auditLog, JoinPoint joinPoint) {
        if (!Str.isEmpty(auditLog.module())) {
            return auditLog.module();
        }

        Class<?> targetClass = joinPoint.getTarget().getClass();
        return moduleCache.computeIfAbsent(targetClass, this::extractModuleFromClass);
    }

    /**
     * 从类中提取模块名称
     * 取第二段包名
     */
    private String extractModuleFromClass(Class<?> clazz) {
        try {
            Package pkg = clazz.getPackage();
            if (pkg == null) {
                return "";
            }

            String packageName = pkg.getName();

            // 只有一段包名
            int firstDot = packageName.indexOf('.');
            if (firstDot == -1) {
                return packageName;
            }

            // 获取第二段包名
            int secondDot = packageName.indexOf('.', firstDot + 1);
            if (secondDot == -1) {
                return packageName.substring(firstDot + 1);
            } else {
                return packageName.substring(firstDot + 1, secondDot);
            }
        } catch (Exception e) {
            return "";
        }
    }
}
