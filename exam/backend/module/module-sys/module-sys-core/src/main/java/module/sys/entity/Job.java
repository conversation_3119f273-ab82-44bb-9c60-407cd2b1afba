package module.sys.entity;

import infra.core.common.IDictEnum;
import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 定时任务
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_job")
public class Job extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 工作/任务id
    private String jobId;
    // 工作/任务描述
    private String jobDesc;
    // cron表达式
    private String cron;
    // cron表达式描述
    private String cronDesc;
    // 排序值
    @Builder.Default
    private Integer sort = 0;
    // 当前状态
    @Builder.Default
    private JobStatus status = JobStatus.WAITING;
    // 上次执行时间
    private LocalDateTime runLastTime;
    // 下次执行时间
    private LocalDateTime nextRunTime;
    // 总执行次数
    @Builder.Default
    private Long runCount = 0L;
    // 成功执行次数
    @Builder.Default
    private Long runSuccess = 0L;
    // 失败执行次数
    @Builder.Default
    private Long runFail = 0L;
    // 版本号
    @Version
    @Builder.Default
    private Long version = 0L;

    /**
     * 工作/任务状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum JobStatus implements IDictEnum {
        WAITING(0, "空闲"),
        RUNNING(1, "运行中"),
        STOP(2, "禁用");

        @EnumValue
        private final int value;
        private final String name;
    }
}
