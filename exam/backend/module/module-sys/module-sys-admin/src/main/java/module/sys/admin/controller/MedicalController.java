package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.MedicalDto;
import module.sys.admin.dto.MedicalTypeDto;
import module.sys.entity.MedicalType;
import module.sys.entity.Medical;
import module.sys.model.AgeLimit;
import module.sys.model.SexLimit;
import module.sys.service.MedicalService;
import module.sys.service.MedicalTypeService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "病史管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/medical")
public class MedicalController {

    private final MedicalTypeService medicalTypeService;
    private final MedicalService medicalService;

    @Operation(summary = "病史类别列表")
    @GetMapping("/list")
    @Perm("health:medical")
    public Result<PageResult<MedicalType>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = medicalTypeService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("name".equals(type)) {
                    w.and(w1 -> w1.like("name", keyTrim).or().like("py", keyTrim));
                }
            }
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增病史类别")
    @PostMapping("/add")
    @AuditLog(code = "health:medical:add", value = "病史类别-新增")
    @Perm("health:medical:add")
    public Result<Long> add(@RequestBody @Valid MedicalTypeDto.EditDto dto) {
        MedicalType entity = MedicalType.builder()
                .name(dto.getName())
                .sex(dto.getSex())
                .age(dto.getAge())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return medicalTypeService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑病史类别")
    @GetMapping("/edit")
    @Perm("health:medical:edit")
    public Result<MedicalTypeDto.EditDto> getEdit(Long id) {
        var medicalOpt = medicalTypeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(medicalOpt.orElse(null), new MedicalTypeDto.EditDto())
        );
    }

    @Operation(summary = "编辑病史类别")
    @PostMapping("/edit")
    @AuditLog(code = "health:medical:edit", value = "病史类别-编辑")
    @Perm("health:medical:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid MedicalTypeDto.EditDto dto) {
        var medicalOpt = medicalTypeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (medicalOpt.isEmpty()) {
            return Result.fail("病史类别不存在");
        }

        MedicalType medical = medicalOpt.get();
        medical.setName(dto.getName());
        medical.setSex(dto.getSex());
        medical.setAge(dto.getAge());
        medical.setSort(dto.getSort());
        medical.setDisabled(dto.getDisabled());

        return medicalTypeService.update(medical) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除病史类别")
    @PostMapping("/delete")
    @AuditLog(code = "health:medical:delete", value = "病史类别-删除")
    @Perm("health:medical:delete")
    @Transactional
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            medicalTypeService.deleteByIds(ids);
            medicalService.deleteBatch(w -> w.in("type_id", ids));
        }
        return Result.OK;
    }

    @Operation(summary = "病史列表")
    @GetMapping("/valueList")
    @Perm("health:medical")
    public Result<List<Medical>> valueList(Long id) {
        List<Medical> entities = medicalService.getListByType(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(entities);
    }

    @Operation(summary = "新增病史")
    @PostMapping("/valueAdd")
    @AuditLog(code = "health:medical:add", value = "病史-新增")
    @Perm("health:medical:add")
    public Result<Long> valueAdd(@RequestBody @Valid MedicalDto.EditDto dto) {
        Medical entity = Medical.builder()
                .typeId(dto.getTypeId())
                .name(dto.getName())
                .icdCode(dto.getIcdCode())
                .sex(dto.getSex())
                .age(dto.getAge())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return medicalService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑病史")
    @GetMapping("/valueEdit")
    @Perm("health:medical:edit")
    public Result<MedicalDto.EditDto> valueGetEdit(Long id) {
        var valueOpt = medicalService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(valueOpt.orElse(null), new MedicalDto.EditDto())
        );
    }

    @Operation(summary = "编辑病史")
    @PostMapping("/valueEdit")
    @AuditLog(code = "health:medical:edit", value = "病史-编辑")
    @Perm("health:medical:edit")
    public Result<Void> valueEdit(@RequestParam Long id, @RequestBody @Valid MedicalDto.EditDto dto) {
        var entity = medicalService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("病史不存在");
        }

        Medical value = entity.get();
        value.setName(dto.getName());
        value.setIcdCode(dto.getIcdCode());
        value.setSex(dto.getSex());
        value.setAge(dto.getAge());
        value.setSort(dto.getSort());
        value.setDisabled(dto.getDisabled());

        return medicalService.update(value) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除病史")
    @PostMapping("/valueDelete")
    @AuditLog(code = "health:medical:delete", value = "病史-删除")
    @Perm("health:medical:delete")
    public Result<Void> valueDelete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            medicalService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用病史")
    @PostMapping("/valueEnable")
    @AuditLog(code = "health:medical:valueEnable", value = "病史-启用")
    @Perm("health:medical:valueEnable")
    public Result<Void> valueEnable(@RequestBody List<Long> ids) {
        medicalService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用病史")
    @PostMapping("/valueDisable")
    @AuditLog(code = "health:medical:valueDisable", value = "病史-禁用")
    @Perm("health:medical:valueDisable")
    public Result<Void> valueDisable(@RequestBody List<Long> ids) {
        medicalService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "导出病史")
    @PostMapping("/valueExport")
    @AuditLog(code = "health:medical:export", value = "病史-导出")
    @Perm("health:medical:export")
    public void valueExport(Long typeId, HttpServletResponse response) throws IOException {
        var medicalOpt = medicalTypeService.getById(typeId);
        if (medicalOpt.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }

        MedicalType medical = medicalOpt.get();
        List<Medical> values = medicalService.getListByType(typeId, FilterOptions.DISABLE_ALL_FILTER);

        ExcelExporter.<Medical>create()
                .sheet(medical.getName() + "病史")
                .columns(cols ->
                        cols.add("名称", Medical::getName)
                                .add("ICD编码", Medical::getIcdCode)
                                .add("性别", value -> value.getSex().getName())
                                .add("年龄阶段", value -> value.getAge().getName())
                                .add("排序", Medical::getSort)
                                .add("状态", value -> value.getDisabled() ? "禁用" : "启用")
                )
                .data(values)
                .export(response, medical.getName());
    }

    @Operation(summary = "导入病史")
    @PostMapping("/valueImport")
    @AuditLog(code = "health:medical:import", value = "病史-导入")
    @Perm("health:medical:import")
    @Transactional
    public Result<Void> valueImport(
            @RequestParam Long typeId,
            @RequestParam("files") MultipartFile file) {

        var medicalOpt = medicalTypeService.getById(typeId);
        if (medicalOpt.isEmpty()) {
            return Result.fail("病史类别不存在");
        }

        try {
            ImportResult<Medical> parseResult = ExcelImporter.create(() -> Medical.builder().typeId(typeId).build())
                    .columns(cols -> {
                        cols.required("名称", ExcelParser.string(), Medical::setName);
                        cols.add("ICD编码", ExcelParser.string(), Medical::setIcdCode);
                        cols.withDefault("性别", value -> IDictEnum.fromName(SexLimit.class, value.toString()), Medical::setSex, SexLimit.NORMAL);
                        cols.withDefault("年龄阶段", value -> IDictEnum.fromName(AgeLimit.class, value.toString()), Medical::setAge, AgeLimit.NORMAL);
                        cols.withDefault("排序", ExcelParser.integer(), Medical::setSort, 0);
                        cols.withDefault("状态", "禁用"::equals, Medical::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            medicalService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }
}
