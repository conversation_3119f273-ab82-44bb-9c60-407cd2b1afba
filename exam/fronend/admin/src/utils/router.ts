import type { Router, RouteRecordRaw } from 'vue-router'
import type { AppConfig } from '~/types/config'
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import { routes } from 'vue-router/auto-routes'
import { useAuth } from '~/composables'

let router: ReturnType<typeof createRouter>
const title = useTitle()
const { getUserInfo, hasPerm } = useAuth()

export function setupRouter(config: AppConfig) {
  router = createRouter({
    history: config.routerMode === 'history'
      ? createWebHistory()
      : createWebHashHistory(),
    routes,
  })

  // 前置守卫
  router.beforeEach(async (to, from, next) => {
    const meta = to.meta
    title.value = meta?.title ? `${meta.title} - ${config.appName}` : config.appName
    // 公共页面直接放行
    if (meta?.public === true) {
      return next()
    }

    const user = await getUserInfo()
    if (!user.value) {
      return next({
        path: config.loginPath,
        query: { redirect: to.fullPath },
      })
    }

    // 检查路由权限
    if (meta?.perm) {
      if (!hasPerm(meta?.perm as string))
        return next('/common/403')
    }

    next()
  })

  return router
}

/**
 * 获取路由实例
 */
export function getRouter(): Router {
  return router
}

/**
 * 菜单项
 */
export interface MenuItem {
  title?: string
  icon?: string
  size?: number
  perm?: string
  name: string
  path: string
  order?: number
  children?: MenuItem[]
}

/**
 * 获取路由树
 */
export function getRoutesTree(): MenuItem[] {
  return routesToMenuTree(routes)
}

/**
 * 转换路由为树级菜单结构
 */
export function routesToMenuTree(routes: RouteRecordRaw[]): MenuItem[] {
  if (!Array.isArray(routes))
    return []

  const processNode = (node: RouteRecordRaw, level: number = 0): MenuItem | undefined => {
    let menu = node
    let meta = node.meta
    if (!meta && node.children?.length) {
      // 第一个子节点就是index的meta，用于meta资料获取
      menu = node.children[0]
      meta = menu.meta

      // 只有第一级的index会保留
      if (level > 0) {
        node.children = node.children.filter(i => i !== menu)
      }
    }

    if (!meta || meta.menu !== true)
      return undefined

    // 菜单项
    const item = {
      ...meta,
      path: menu.path,
      name: menu.name?.toString() ?? '',
    } as MenuItem

    // 子节点
    if ((node.children?.length ?? 0) > 0) {
      item.children = node.children!.map(i => processNode(i, level + 1)).filter(Boolean).sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)) as MenuItem[]
    }

    return item
  }

  const tree = routes
    .map(i => processNode(i, 0))
    .filter(Boolean)
    .sort((a, b) => (a?.order ?? 0) - (b?.order ?? 0)) as MenuItem[]

  return tree
}
