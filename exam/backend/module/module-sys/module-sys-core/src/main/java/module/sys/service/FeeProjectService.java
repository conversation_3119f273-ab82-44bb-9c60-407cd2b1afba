package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.FeeProject;
import module.sys.mapper.FeeProjectMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.function.Function;

/**
 * 收费项目服务
 */
@Slf4j
@Service
public class FeeProjectService extends ServiceBase<FeeProjectMapper, FeeProject> {
    @Override
    protected void beforeSave(FeeProject entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByFeeNo(entity.getFeeNo(), isUpdate ? entity.getId() : null)) {
            throw new BizException("收费项目编号已存在");
        }

        if (existsByCountryNo(entity.getCountryNo(), isUpdate ? entity.getId() : null)) {
            throw new BizException("国家编码已存在");
        }

        setPinyin(entity);
    }

    @Override
    protected Function<QueryWrapper<FeeProject>, QueryWrapper<FeeProject>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected Function<QueryWrapper<FeeProject>, QueryWrapper<FeeProject>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    /**
     * 检查收费项目编号是否已存在
     */
    public boolean existsByFeeNo(String feeNo, Long excludeId) {
        if (Str.isEmpty(feeNo)) {
            return false;
        }

        return exists(w -> {
            w.eq("fee_no", feeNo);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 检查国家编码是否已存在
     */
    public boolean existsByCountryNo(String countryNo, Long excludeId) {
        if (Str.isEmpty(countryNo)) {
            return false;
        }

        return exists(w -> {
            w.eq("country_no", countryNo);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 根据收费项目编号获取
     */
    public Optional<FeeProject> getByFeeNo(String feeNo) {
        return getFirst(w -> w.eq("fee_no", feeNo));
    }

    /**
     * 根据国家编码获取
     */
    public Optional<FeeProject> getByCountryNo(String countryNo) {
        return getFirst(w -> w.eq("country_no", countryNo));
    }

    /**
     * 设置拼音
     */
    private void setPinyin(FeeProject value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getProjectName());
        value.setPy(pinyin.py());
    }
}