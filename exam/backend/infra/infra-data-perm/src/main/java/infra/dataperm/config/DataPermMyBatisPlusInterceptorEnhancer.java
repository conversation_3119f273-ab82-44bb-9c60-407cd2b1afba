package infra.dataperm.config;

import infra.dataperm.core.DataPermHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.NonNull;

/**
 * MyBatisPlus数据权限拦截器增强器
 */
@Slf4j
@RequiredArgsConstructor
public class DataPermMyBatisPlusInterceptorEnhancer implements BeanPostProcessor {

    private final DataPermHandler dataPermHandler;

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        if (bean instanceof MybatisPlusInterceptor interceptor) {
            enhanceInterceptor(interceptor, beanName);
        }
        return bean;
    }

    /*
     * 增强加入数据权限拦截器
     */
    private void enhanceInterceptor(MybatisPlusInterceptor interceptor, String beanName) {
        // 检查是否已经包含数据权限拦截器
        boolean hasDataPermInterceptor = interceptor.getInterceptors().stream()
                .anyMatch(inner -> inner instanceof DataPermissionInterceptor);

        if (!hasDataPermInterceptor) {
            log.info("[配置] 为MyBatis-Plus拦截器注入数据权限处理器");
            interceptor.addInnerInterceptor(new DataPermissionInterceptor(dataPermHandler));
        }
    }
}
