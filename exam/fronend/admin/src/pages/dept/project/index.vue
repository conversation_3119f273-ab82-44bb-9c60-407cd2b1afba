<script setup lang="ts">
definePage({
  meta: {
    title: '项目套餐',
    menu: true,
    order: 3,
    icon: 'la:project-diagram',
  },
})
</script>

<template>
<p>1. 检查项目管理：新增 / 编辑体检项目（如 “血常规”“CT 影像”），录入项目属性（参考范围、物价标准），关联检中结果录入模块的校验规则；</p>
<p>2. 套餐配置：创建 / 修改体检套餐（如 “基础套餐”“高血压专项套餐”），选择套餐包含项目，设置定价与优惠规则，关联物价标准确保收费合规；</p>
<p>3. 套餐校验：设置套餐项目调整规则（禁止重复添加同类项目），同步至前台套餐确认模块，确保检前调整合理性</p>

检前阶段 “体检套餐确认（校验调整合理性，更新费用）”🔶21-18；检前阶段 “项目物价核对（内置项目物价标准）”🔶21-20；客户挖掘阶段 “体检套餐推荐（关联套餐数据）”🔶21-9
</template>
