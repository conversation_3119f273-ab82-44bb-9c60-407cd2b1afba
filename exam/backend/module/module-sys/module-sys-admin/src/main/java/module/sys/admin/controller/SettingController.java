package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.model.AppSetting;
import module.sys.model.HospitalInfo;
import module.sys.model.SafeSetting;
import module.sys.service.SettingService;
import module.sys.service.UpfileService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "设置")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/setting")
public class SettingController {
    private final SettingService settingService;
    private final UpfileService upFileService;

    @Operation(summary = "获取应用设置")
    @GetMapping("/app")
    @Perm("sys:setting:app")
    public Result<AppSetting> getAppSetting() {
        return Result.okData(settingService.getAppSetting());
    }


    @Operation(summary = "更新应用设置")
    @PostMapping("/app")
    @Perm("sys:setting:app")
    @AuditLog(code = "sys:setting:app", value = "更新应用设置")
    public Result<Void> updateAppSetting(@RequestBody AppSetting appSetting) {
        settingService.updateAppSetting(appSetting);
        return Result.OK;
    }

    @Operation(summary = "获取安全设置")
    @GetMapping("/safe")
    @Perm("sys:setting:safe")
    public Result<SafeSetting> getSafeSetting() {
        return Result.okData(settingService.getSafeSetting());
    }

    @Operation(summary = "更新安全设置")
    @PostMapping("/safe")
    @Perm("sys:setting:safe")
    @AuditLog(code = "sys:setting:safe", value = "更新安全设置")
    public Result<Void> updateSafeSetting(@RequestBody SafeSetting safeSetting) {
        settingService.updateSafeSetting(safeSetting);
        return Result.OK;
    }

    @Operation(summary = "获取医院信息")
    @GetMapping("/hospitalInfo")
    @Perm("sys:setting:hospitalInfo")
    public Result<HospitalInfo> getHospitalInfo() {
        return Result.okData(settingService.getHospitalInfo());
    }

    @Operation(summary = "更新医院信息")
    @PostMapping("/hospitalInfo")
    @Perm("sys:setting:hospitalInfo")
    @AuditLog(code = "sys:setting:hospitalInfo", value = "更新医院信息")
    public Result<Void> updateHospitalInfo(@RequestBody HospitalInfo hospitalInfo) {
        settingService.updateHospitalInfo(hospitalInfo);
        return Result.OK;
    }
}