package infra.cache.impl;

import infra.cache.core.ILock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * 基于 Redis 的锁实现
 */
@Slf4j
public class RedisLock implements ILock {
    private final RLock rLock;

    /**
     * 构造函数
     *
     * @param redissonClient Redisson客户端
     * @param lockKey        锁的key
     */
    public RedisLock(RedissonClient redissonClient, String lockKey) {
        this.rLock = redissonClient.getLock(lockKey);
    }

    /**
     * 尝试获取锁
     *
     * @param timeoutSeconds 获取锁的超时时间（秒），小于等于0表示不带超时
     * @return true表示获取成功，false表示获取失败
     */
    @Override
    public boolean tryLock(long timeoutSeconds) {
        try {
            if (timeoutSeconds <= 0) {
                return rLock.tryLock();
            } else {
                return rLock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
            }
        } catch (InterruptedException e) {
            // 虚拟线程被中断时，恢复中断状态并返回false
            Thread.currentThread().interrupt();
            log.warn("获取锁时线程被中断", e);
            return false;
        }
    }

    /**
     * 释放锁
     */
    @Override
    public void unlock() {
        if (rLock.isHeldByCurrentThread()) {
            rLock.unlock();
        }
    }
}
