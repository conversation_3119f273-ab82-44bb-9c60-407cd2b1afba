package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import module.sys.entity.Dept;

public class DeptDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        private Long parentId;
        @NotBlank(message = "部门名称不能为空")
        private String name;
        @NotNull(message = "部门类型不能为空")
        private Dept.DeptType type;
        private String code;
        private Long leaderId;
        private String leaderName;
        private String contact;
        private Long addressDictId;
        private Integer sort;
        private Boolean disabled;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BasicInfo {
        private Long id;
        private String name;
        private String code;
    }
} 