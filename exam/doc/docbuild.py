import os
import mysql.connector
import pandas as pd
from datetime import datetime
import logging

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 配置管理 ---
DB_CONFIG = {
    'host': os.getenv('DB_HOST', '************'),
    'port': int(os.getenv('DB_PORT', 52388)),
    'database': os.getenv('DB_NAME', 'exam'),
    'user': os.getenv('DB_USER', 'exam'),
    'password': os.getenv('DB_PASSWORD', '6ZjdYeH3852wPBwK')
}

OUTPUT_DIR = '.'
INCLUDED_TABLE_PREFIXES = ['sys_', 'member_', 'health_']  # Example: ['sys_', 'user_']

# --- HTML 模板 ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{db_name} 数据库文档 - {timestamp}</title>
    <style>
        body {{ font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif; margin: 0; background-color: #f4f7f9; }}
        .header {{ text-align: center; }}
        .header p {{ color: #888; }}
        .tab-nav {{ display: flex; border-bottom: 1px solid #ddd; margin-bottom: 20px; }}
        .tab-button {{ padding: 10px 20px; cursor: pointer; background-color: #f4f7f9; border: 1px solid #aaa; border-bottom: none; border-radius: 5px 5px 0 0; margin-right: 5px; }}
        .tab-button.active {{ position: relative; top: 1px; }}
        .tab-content {{ display: none; }}
        .tab-content.active {{ display: block; }}
        .table-container {{ margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden;}}
        .table-header {{ background-color: #4a90e2; color: white; padding: 15px; }}
        .table-header h3 {{ margin: 0; }}
        .table-comment {{ font-style: italic; color: #e0e0e0; margin-top: 5px; font-weight: normal;}}
        .table-content {{ padding: 15px; overflow-x: auto; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ border: 1px solid #e0e0e0; padding: 12px; text-align: left; }}
        th {{ background-color: #f8f9fa; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #fdfdfd; }}
        .primary-key {{ background-color: #d4edda; }}
        .indexed {{ background-color: #fff3cd; }}
        .footer {{ margin-top: 40px; text-align: center; color: #999; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{db_name} 数据库文档</h1>
            <p>生成时间: {timestamp}</p>
        </div>
        <div class="tab-nav">
            {tab_buttons}
        </div>
        <div class="content-container">
            {tab_contents}
        </div>
        <div class="footer">
            <p>由数据库文档自动生成工具创建</p>
        </div>
    </div>
    <script>
        function openTab(evt, tabName) {{
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {{
                tabcontent[i].style.display = "none";
            }}
            tablinks = document.getElementsByClassName("tab-button");
            for (i = 0; i < tablinks.length; i++) {{
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }}
            document.getElementById(tabName).style.display = "block";
            if (evt) {{
                evt.currentTarget.className += " active";
            }}
        }}
        // 默认打开第一个tab
        if (document.getElementsByClassName("tab-button")[0]) {{
            document.getElementsByClassName("tab-button")[0].click();
        }}
    </script>
</body>
</html>
"""

def format_data_type(col):
    """格式化数据类型及其长度/精度"""
    return col.get('COLUMN_TYPE', '')

def get_database_schema(conn):
    """
    一次性从数据库中获取所有表、列、主键和索引的完整信息。
    """
    cursor = conn.cursor(dictionary=True)
    db_name = DB_CONFIG['database']

    try:
        # 1. 获取所有表及其注释
        table_query = f"""
            SELECT 
                table_name, 
                table_comment AS comment
            FROM information_schema.tables 
            WHERE table_schema = '{db_name}';
        """
        logging.info("Executing table_query...")
        cursor.execute(table_query)
        tables_list = cursor.fetchall()
        logging.info(f"Content of tables_list: {tables_list}") # Added logging
        tables = {}
        for t in tables_list:
            # Normalize table_name to lowercase for consistency
            table_name_key = t.get('table_name') or t.get('TABLE_NAME')
            if table_name_key:
                tables[table_name_key.lower()] = {'table_name': table_name_key, 'comment': t.get('comment'), 'columns': []}
            else:
                logging.warning(f"Skipping unexpected item in tables_list: {t}")
        logging.info(f"Found {len(tables)} tables.")

        # If no tables are found, return early to prevent KeyError
        if not tables:
            logging.warning(f"No tables found in database '{db_name}'. Skipping column and index queries.")
            return []

        # 2. 获取所有列信息
        columns_query = f"""
            SELECT 
                table_name, 
                column_name, 
                ordinal_position, 
                column_type, 
                is_nullable, 
                column_default, 
                column_comment AS comment,
                extra
            FROM information_schema.columns 
            WHERE table_schema = '{db_name}' 
            ORDER BY table_name, ordinal_position;
        """
        logging.info("Executing columns_query...")
        cursor.execute(columns_query)
        columns_list = cursor.fetchall()
        logging.info(f"Raw columns_list from DB: {columns_list[:5]} (showing first 5 entries)") # Log first 5 entries
        
        for col in columns_list:
            # Normalize table_name to lowercase for lookup
            col_table_name = col.get('table_name') or col.get('TABLE_NAME')
            if col_table_name and col_table_name.lower() in tables:
                tables[col_table_name.lower()]['columns'].append(col)
                logging.info(f"Added column {col['COLUMN_NAME']} to table {col_table_name}")
            else:
                logging.warning(f"Skipping column {col.get('column_name', 'N/A')} from unknown or un-matched table: {col_table_name}")
        logging.info("Fetched column information.")
        
        # Verify columns are added to tables
        for table_name, table_data in tables.items():
            if not table_data['columns']:
                logging.warning(f"Table '{table_name}' has no columns after processing.")
            else:
                logging.info(f"Table '{table_name}' has {len(table_data['columns'])} columns.")

        # 3. 获取所有主键和索引信息
        stats_query = f"""
            SELECT 
                table_name, 
                column_name, 
                index_name
            FROM information_schema.statistics
            WHERE table_schema = '{db_name}';
        """
        logging.info("Executing stats_query...")
        cursor.execute(stats_query)
        stats_list = cursor.fetchall()
        for stat in stats_list:
            # Normalize table_name to lowercase for lookup
            stat_table_name = stat.get('table_name') or stat.get('TABLE_NAME')
            if stat_table_name and stat_table_name.lower() in tables:
                for col in tables[stat_table_name.lower()]['columns']:
                    if col['COLUMN_NAME'] == stat['COLUMN_NAME']:
                        if stat['INDEX_NAME'] == 'PRIMARY':
                            col['is_primary_key'] = True
                        else:
                            col['is_indexed'] = True
        logging.info("Fetched index information.")

    finally:
        cursor.close()

    return list(tables.values())

def generate_html_doc(grouped_tables, output_path):
    """生成HTML格式的文档"""
    tab_buttons = ""
    tab_contents = ""
    
    group_keys = sorted(grouped_tables.keys())
    first_tab = True
    for group_name in group_keys:
        tables = grouped_tables[group_name]
        active_class = " active" if first_tab else ""
        tab_buttons += f'<button class="tab-button{active_class}" onclick="openTab(event, \'{group_name}\')">{group_name}</button>'
        
        table_html_parts = []
        for table in tables:
            columns_html = """
            <table>
                <thead>
                    <tr>
                        <th>字段名</th>
                        <th>数据类型</th>
                        <th>可为空</th>
                        <th>默认值</th>
                        <th>主键</th>
                        <th>是否自增</th>
                        <th>索引</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
            """
            for col in table['columns']:
                pk_class = 'primary-key' if col.get('is_primary_key') else ''
                idx_class = 'indexed' if not pk_class and col.get('is_indexed') else ''
                is_nullable_text = '是' if col['IS_NULLABLE'] == 'YES' else '否'
                is_pk_text = '是' if col.get('is_primary_key') else '否'
                is_auto_increment_text = '是' if 'auto_increment' in col.get('EXTRA', '') else '否'
                is_idx_text = '是' if col.get('is_indexed') else '否'
                col_comment_text = col.get('comment') or ''
                col_default_text = col.get('COLUMN_DEFAULT') or ''
                col_data_type = format_data_type(col)

                columns_html += f"""
                    <tr class="{pk_class} {idx_class}">
                        <td>{col['COLUMN_NAME']}</td>
                        <td>{col_data_type}</td>
                        <td>{is_nullable_text}</td>
                        <td>{col_default_text}</td>
                        <td>{is_pk_text}</td>
                        <td>{is_auto_increment_text}</td>
                        <td>{is_idx_text}</td>
                        <td>{col_comment_text}</td>
                    </tr>
                """
            columns_html += "</tbody></table>"
            
            table_html_parts.append(f"""
            <div class="table-container">

                <div class="table-header">

                    <h3>{table['table_name']}</h3>

                    <div class="table-comment">{table.get('comment') or '无表注释'}</div>

                </div>

                <div class="table-content">{columns_html}</div>

            </div>
            """)
        
        tab_contents += f'<div id="{group_name}" class="tab-content{active_class}">{" ".join(table_html_parts)}</div>'
        first_tab = False

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    html_content = HTML_TEMPLATE.format(
        db_name=DB_CONFIG['database'],
        timestamp=timestamp,
        tab_buttons=tab_buttons,
        tab_contents=tab_contents
    )
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    logging.info(f"HTML 文档已成功生成: {output_path}")

def generate_excel_doc(grouped_tables, output_path):
    """为每个表生成一个Sheet的Excel文档"""
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        sorted_groups = sorted(grouped_tables.items())

        for group_name, tables in sorted_groups:
            for table in tables:
                sheet_name = table['table_name'][:31]
                
                data = []
                for col in table['columns']:
                    data.append({
                        '字段名': col['COLUMN_NAME'],
                        '数据类型': format_data_type(col),
                        '可为空': '是' if col['IS_NULLABLE'] == 'YES' else '否',
                        '默认值': col.get('COLUMN_DEFAULT'),
                        '主键': '是' if col.get('is_primary_key') else '否',
                        '是否自增': '是' if 'auto_increment' in col.get('EXTRA', '') else '否',
                        '索引': '是' if col.get('is_indexed') else '否',
                        '备注': col.get('COLUMN_COMMENT')
                    })
                
                df = pd.DataFrame(data)
                
                # 创建包含表名和注释的标题行
                title_df = pd.DataFrame({
                    'A': [f"表名: {table['table_name']}"],
                    'B': [f"注释: {table.get('comment') or '无'}"]
                })
                title_df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=0)

                # 写入数据
                df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=2)
                
                # 自动调整列宽
                worksheet = writer.sheets[sheet_name]
                for i, col_name in enumerate(df.columns, 1):
                    column_letter = chr(ord('A') + i - 1)
                    # Get the max length of the header and the data
                    max_len = max(len(str(x)) for x in df[col_name].astype(str).dropna()) if not df.empty and not df[col_name].dropna().empty else 0
                    max_len = max(max_len, len(col_name))
                    worksheet.column_dimensions[column_letter].width = max_len + 4

    logging.info(f"Excel 文档已成功生成: {output_path}")

def main():
    """主函数"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        logging.info(f"创建输出目录: {OUTPUT_DIR}")
    
    conn = None
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        logging.info(f"成功连接到数据库: {DB_CONFIG['database']}")
        
        schema_data = get_database_schema(conn)
        
        if not schema_data:
            logging.warning(f"在数据库 '{DB_CONFIG['database']}' 中未找到任何表。")
            return

        # 过滤表
        if INCLUDED_TABLE_PREFIXES:
            filtered_schema_data = [
                table for table in schema_data 
                if 'table_name' in table and any(table['table_name'].startswith(prefix) for prefix in INCLUDED_TABLE_PREFIXES)
            ]
        else:
            filtered_schema_data = schema_data
        logging.info(f"Filtered schema data (after prefix filtering): {filtered_schema_data}") # Added logging

        # 按前缀分组
        grouped_tables = {}
        for table in filtered_schema_data:
            if 'table_name' in table:
                # Use the first part of the table name (split by '_') as the group key
                prefix = table['table_name'].split('_')[0]
                if prefix not in grouped_tables:
                    grouped_tables[prefix] = []
                grouped_tables[prefix].append(table)
        logging.info(f"Grouped tables: {grouped_tables}") # Added logging

        # 生成两种格式的文档
        db_name = DB_CONFIG['database']
        html_path = os.path.join(OUTPUT_DIR, f"{db_name}_docs.html")
        excel_path = os.path.join(OUTPUT_DIR, f"{db_name}_docs.xlsx")
        
        generate_html_doc(grouped_tables, html_path)
        generate_excel_doc(grouped_tables, excel_path)
        
        logging.info(f"所有文档均已生成完毕，保存在目录: {OUTPUT_DIR}")
        
    except mysql.connector.Error as e:
        logging.error(f"数据库连接失败: {e}")
        logging.error("请检查数据库配置 (主机, 端口, 用户名, 密码) 和网络连接。")
    except Exception as e:
        logging.error(f"发生未知错误: {e}", exc_info=True)
    finally:
        if conn and conn.is_connected():
            conn.close()
            logging.info("数据库连接已关闭。")

if __name__ == "__main__":
    main()
