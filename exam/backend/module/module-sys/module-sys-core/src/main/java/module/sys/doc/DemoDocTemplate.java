package module.sys.doc;

import org.springframework.stereotype.Component;

@Component
public class DemoDocTemplate implements IDocTemplate {
    @Override
    public String getName() {
        return "示例模板";
    }

    @Override
    public String getCode() {
        return "demo";
    }

    @Override
    public String getTemplate() {
        return """
            <!DOCTYPE html>
               <html xmlns:th="http://www.thymeleaf.org">
               <head>
                   <meta charset="UTF-8" />
                   <title th:text="${data.title}">文书模板示例</title>
                   <style>
                       body {
                           font-family: simhei, SimHei, SimSun, Microsoft YaHei, SansSerif, sans-serif;
                           font-size: 12pt;
                       }
                       table { border-collapse: collapse; width: 100% }
                       th, td { border: 1px solid #999; padding: 8px; }
                       .center { text-align: center }
                   </style>
               </head>
               <body>
                   <h1 class="center" th:text="${data.title}">文书模板示例</h1>
                   <div style="text-align: right; color: #aaa;">报告生成时间：<span th:text="${data.time}">2028-08-08 15:15</span></div>
                   <h2 class="center">用户列表</h2>
                   <table>
                       <tr>
                           <th>序号</th>
                           <th>姓名</th>
                           <th>年龄</th>
                           <th>职业</th>
                       </tr>
                       <tr th:each="user, stat : ${data.users}">
                           <td th:text="${stat.index + 1}">1</td>
                           <td th:text="${user.name}">张三</td>
                           <td th:text="${user.age}">18</td>
                           <td th:text="${user.work}">工程师</td>
                       </tr>
                   </table>
               </body>
               </html>
            """;
    }

    @Override
    public Integer getSort() {
        return 0;
    }

    @Override
    public Class<?> getModel() {
        return DemoDocModelDto.class;
    }
}
