package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.*;
import module.sys.mapper.UserMapper;
import module.sys.mapper.UserPermMapper;
import module.sys.mapper.UserSignMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

/**
 * 用户服务
 */
@Slf4j
@Service
public class UserService extends ServiceBase<UserMapper, User> {
    @Autowired
    private UserPermMapper userPermMapper;
    @Autowired
    private UserSignMapper userSignMapper;
    @Autowired
    private DeptService deptService;
    @Autowired
    private UserDeptService userDeptService;

    @Override
    protected Function<QueryWrapper<User>, QueryWrapper<User>> bizFilter() {
        return w -> w.ne("status", User.UserStatus.DISABLED);
    }

    @Override
    protected void beforeSave(User entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsUser(entity.getLoginName(), isUpdate ? entity.getId() : null)) {
            throw new BizException("登录名已存在");
        }

        setPinyin(entity);
    }

    /**
     * 根据用户名获取用户
     */
    public Optional<User> getByLoginName(String loginName) {
        if (Str.isEmpty(loginName)) {
            return Optional.empty();
        }
        return getFirst(w -> w.eq("login_name", loginName));
    }

    /**
     * 根据手机号获取用户
     */
    public Optional<User> getByPhone(String phone) {
        if (Str.isEmpty(phone)) {
            return Optional.empty();
        }
        return getFirst(w -> w.eq("phone", phone));
    }

    /**
     * 获取安全扩展字符
     */
    public static String getSafeExtra(User user) {
        if (user == null) return "";

        String disabledStatus = user.getStatus().getValue() + "_";
        if (Str.isEmpty(user.getPassword())) {
            return disabledStatus;
        }
        return disabledStatus + user.getPassword().substring(user.getPassword().length() - 8);
    }

    /**
     * 是否存在指定的用户
     */
    public boolean existsUser(String loginName, Long excludeId) {
        if (Str.isEmpty(loginName)) {
            return false;
        }

        return exists(w -> {
            w.eq("login_name", loginName);
            w.ne(excludeId != null, "id", excludeId);
        });
    }

    /**
     * 获取用户特殊权限
     */
    public List<UserPerm> getSpecialPerms(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        QueryWrapper<UserPerm> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        return userPermMapper.selectList(wrapper);
    }

    /**
     * 更新用户特殊权限
     */
    @Transactional
    public void updateSpecialPerms(Long userId, Set<String> allowPerms, Set<String> denyPerms) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        Set<String> allowSet = allowPerms != null ? new HashSet<>(allowPerms) : new HashSet<>();
        Set<String> denySet = denyPerms != null ? new HashSet<>(denyPerms) : new HashSet<>();

        // 检查是否有重复的权限
        Set<String> intersection = new HashSet<>(allowSet);
        intersection.retainAll(denySet);
        if (!intersection.isEmpty()) {
            throw new BizException("授予权限和排除权限不能冲突");
        }

        // 删除原有特殊权限
        QueryWrapper<UserPerm> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("user_id", userId);
        userPermMapper.delete(deleteWrapper);

        // 批量创建新权限
        List<UserPerm> newPerms = new ArrayList<>();

        // 添加允许权限
        for (String perm : allowSet) {
            UserPerm userPerm = new UserPerm();
            userPerm.setUserId(userId);
            userPerm.setPerm(perm);
            userPerm.setType(UserPerm.UserPermType.ALLOW);
            newPerms.add(userPerm);
        }

        // 添加拒绝权限
        for (String perm : denySet) {
            UserPerm userPerm = new UserPerm();
            userPerm.setUserId(userId);
            userPerm.setPerm(perm);
            userPerm.setType(UserPerm.UserPermType.DENY);
            newPerms.add(userPerm);
        }

        // 批量插入
        if (!newPerms.isEmpty()) {
            for (UserPerm perm : newPerms) {
                userPermMapper.insert(perm);
            }
        }
    }

    /**
     * 获取用户列表
     */
    public List<User> getList(Long dept, boolean children) {
        if (dept != null) {
            List<Long> deptIds;
            if (children) {
                deptIds = deptService.getAllChildren(dept, true).stream().map(Dept::getId).toList();
            } else {
                deptIds = List.of(dept);
            }

            if (deptIds.isEmpty()) {
                return Collections.emptyList();
            }

            var userIds = userDeptService.getByDepts(deptIds).stream()
                    .map(UserDept::getUserId)
                    .toList();

            if (userIds.isEmpty()) {
                return Collections.emptyList();
            }

            return getListByIds(userIds);
        } else {
            return getList();
        }
    }

    /**
     * 设置用户签名
     */
    public boolean setUserSign(Long userId, String signBase64) {
        if (userId == null || Str.isEmpty(signBase64)) {
            throw new BizException("用户及签名不能为空");
        }

        // 查询是否已存在签名记录
        QueryWrapper<UserSign> wrapper = new QueryWrapper<>();
        wrapper.eq("id", userId);
        UserSign existingSign = userSignMapper.selectOne(wrapper);

        if (existingSign != null) {
            // 如果存在，则更新
            existingSign.setSign(signBase64);
            existingSign.setUpdateTime(LocalDateTime.now());
            return userSignMapper.updateById(existingSign) > 0;
        } else {
            // 如果不存在，则新增
            UserSign newSign = UserSign.builder()
                    .sign(signBase64)
                    .updateTime(LocalDateTime.now())
                    .build();
            newSign.setId(userId);
            return userSignMapper.insert(newSign) > 0;
        }
    }

    /**
     * 获取用户签名
     */
    public String getUserSign(Long userId) {
        QueryWrapper<UserSign> wrapper = new QueryWrapper<>();
        wrapper.eq("id", userId);
        UserSign userSign = userSignMapper.selectOne(wrapper);
        return userSign != null ? userSign.getSign() : null;
    }

    /**
     * 设置拼音
     */
    private void setPinyin(User user) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(user.getUserName());
        user.setPy(pinyin.py());
    }
}
