package module.sys.entity;

import infra.core.common.IDictEnum;
import infra.domain.entity.TreeEntity;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 *  部门
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_dept")
public class Dept extends TreeEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 部门名称
    private String name;
    // 部门编码
    private String code;
    // 拼音
    private String py;
    // 部门类型
    @Builder.Default
    private DeptType type = DeptType.DEPT;
    // 负责人用户Id
    private Long leaderId;
    // 负责人名称
    private String leaderName;
    // 联系方式
    private String contact;
    // 地址字典id
    private Long addressDictId;
    // 是否禁用
    @Builder.Default
    private Boolean disabled = false;

    /**
     * 部门类型枚举
     */
    @Getter
    @RequiredArgsConstructor
    public enum DeptType implements IDictEnum {
        DEPT(0, "部门"),
        COMPANY(1, "公司");

        @EnumValue
        private final int value;
        private final String name;
    }
}
