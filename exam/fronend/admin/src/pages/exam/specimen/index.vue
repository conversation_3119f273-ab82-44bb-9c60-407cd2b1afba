<script setup lang="ts">
definePage({
  meta: {
    title: '风险管控',
    menu: true,
    order: 3,
    icon: 'tabler:alert-triangle',
  },
})
</script>

<template>
<p>1. 危急值 / 重阳预警：设置预警规则（如血糖＜2.8mmol/L、肿瘤标志物阳性），检测到异常结果时，自动触发弹窗 + 短信预警，通知医护人员与健管人员，记录响应措施；</p>
<p>2. 科室会诊：发起多科室会诊请求，共享客户体检数据（结果、病史），实时通知会诊人员，跟踪意见提交进度，归档会诊结论；</p>
<p>3. 流程协同：客户提出项目增删需求时，校验合理性并同步更新费用、体检指引，确保检中流程与前台、总检环节协同；</p>
<p>4. 超时提醒：记录检查操作时长，超限时自动标记，提醒科室优化流程，避免影响总检评估进度</p>

检中阶段 “危急值 / 重阳预警（自动触发预警、通知相关人员）”“科室间协调会诊（快速发起请求、共享数据）”“体检项目增弃（校验合理性、更新费用与指引）”
</template>
