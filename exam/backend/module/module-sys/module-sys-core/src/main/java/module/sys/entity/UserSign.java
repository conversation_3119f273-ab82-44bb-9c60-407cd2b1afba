package module.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 用户
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_user_sign")
public class UserSign extends IdEntity {
    // 签名
    private String sign;
    // 更新时间
    private LocalDateTime updateTime;
}
