package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.domain.model.TreeNode;

import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.DeptDto;
import module.sys.entity.Dept;
import module.sys.service.DeptService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "科室管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/dept")
public class DeptController {

    private final DeptService deptService;

    @Operation(summary = "组织结构树")
    @GetMapping("/tree")
    @Perm("sys:dept")
    public Result<List<TreeNode>> tree() {
        List<TreeNode> tree = deptService.getTreeWithPost(null, false, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(tree);
    }

    @Operation(summary = "新增科室")
    @PostMapping("/add")
    @AuditLog(code = "sys:dept:addDept", value = "新增科室")
    @Perm("sys:dept:addDept")
    public Result<Long> add(@RequestBody @Valid DeptDto.EditDto dto) {
        Dept entity = Dept.builder()
                .name(dto.getName())
                .type(dto.getType())
                .code(dto.getCode())
                .leaderId(dto.getLeaderId())
                .leaderName(dto.getLeaderName())
                .contact(dto.getContact())
                .addressDictId(dto.getAddressDictId())
                .disabled(dto.getDisabled())
                .build();
        entity.setSort(dto.getSort());
        entity.setParentId(dto.getParentId());

        return deptService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑科室")
    @GetMapping("/edit")
    @Perm("sys:dept:editDept")
    public Result<DeptDto.EditDto> getEdit(Long id) {
        var deptOpt = deptService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(deptOpt.orElse(null), new DeptDto.EditDto())
        );
    }

    @Operation(summary = "编辑科室")
    @PostMapping("/edit")
    @AuditLog(code = "sys:dept:editDept", value = "编辑科室")
    @Perm("sys:dept:editDept")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid DeptDto.EditDto dto) {
        var entity = deptService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("科室不存在");
        }

        Dept dept = entity.get();
        dept.setParentId(dto.getParentId());
        dept.setName(dto.getName());
        dept.setCode(dto.getCode());
        dept.setType(dto.getType());
        dept.setLeaderId(dto.getLeaderId());
        dept.setLeaderName(dto.getLeaderName());
        dept.setContact(dto.getContact());
        dept.setAddressDictId(dto.getAddressDictId());
        dept.setSort(dto.getSort());
        dept.setDisabled(dto.getDisabled());

        return deptService.update(dept) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除科室")
    @PostMapping("/delete")
    @AuditLog(code = "sys:dept:deleteDept", value = "删除科室")
    @Perm("sys:dept:deleteDept")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            for (Long id : ids) {
                deptService.deleteById(id);
            }
        }
        return Result.OK;
    }

    @Operation(summary = "获取组织结构树")
    @GetMapping("/commonDeptTree")
    @Perm
    public Result<List<TreeNode>> getCommonDeptTree(
            @RequestParam(required = false) Long parent,
            @RequestParam(defaultValue = "false") boolean post) {

        var tree = deptService.getTreeWithPost(parent, post, FilterOptions.DEFAULTS);
        return Result.okData(tree);
    }

    @Operation(summary = "获取科室列表")
    @GetMapping("/commonDept")
    @Perm
    public Result<List<DeptDto.BasicInfo>> getCommonDept() {
        var list = deptService.getList();
        return Result.okData(list.stream().map(item -> ObjectUtil.copyTo(item, new DeptDto.BasicInfo())).toList());
    }
}
