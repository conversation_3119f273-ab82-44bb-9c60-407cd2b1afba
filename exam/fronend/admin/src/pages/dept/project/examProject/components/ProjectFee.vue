<script setup lang="ts">
import * as api from '~/api/health/examProject'
import { useDict } from '~/composables'
import { tips } from '~/utils'

interface Props {
  projectId: number
}
const props = withDefaults(defineProps<Props>(), {
})

const { KEYS, getName } = useDict()
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef<any>('formRef')
const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)
const getProjectFeeName = getName(KEYS.PROJECT_FEE)

const initData = {
  feeProjectId: null,
  quantity: 1,
  examProjectId: props.projectId,
  remark: '',
}

const rules = {
  feeProjectId: [{ required: true, message: '请选择收费项目' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success('操作成功')
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.removeFee)
}
</script>

<template>
  <table-list ref="tableRef" :fetch-api="api.listFee" :pager="false" :query-params="{ id: props.projectId }">
    <template #left-actions>
      <el-button v-if="$hasPerm('health:examProject:fee:add')" type="primary" @click="formRef.add()">
        <template #icon>
          <icon icon="ep:plus" />
        </template>
        新增
      </el-button>
      <el-button-group class="ml-2">
        <el-popconfirm v-if="$hasPerm('health:examProject:fee:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
          <template #reference>
            <el-button title="删除" :disabled="!canAction">
              <template #icon>
                <icon icon="ep:delete" />
              </template>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </el-button-group>
    </template>
    <el-table-column type="selection" align="center" width="50" />
    <el-table-column prop="feeProjectId" label="收费项目" width="250">
      <template #default="{ row }">
        {{ getProjectFeeName(row.feeProjectId) }}
      </template>
    </el-table-column>
    <el-table-column prop="quantity" label="数量" width="100" align="center" />
    <el-table-column prop="remark" label="备注" show-overflow-tooltip />
    <el-table-column label="操作" align="center" width="150">
      <template #default="{ row }">
        <div class="space-x-2">
          <el-button v-if="$hasPerm('health:examProject:fee:edit')" link @click="formRef.edit(row.id)">
            编辑
          </el-button>
          <el-popconfirm
            v-if="$hasPerm('health:examProject:fee:delete')"
            title="确认要删除该项吗？"
            placement="left"
            @confirm="handleRemove([row.id])"
          >
            <template #reference>
              <el-button type="warning" link>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </div>
      </template>
    </el-table-column>
  </table-list>
  <detail-form
    ref="formRef"
    title="关联收费项目"
    width="550px"
    class="p-6"
    :init-data="initData"
    :rules="rules"
    :get-api="api.getEditFee"
    :add-api="api.addFee"
    :edit-api="api.postEditFee"
    @success="tableRef.fetchData()"
  >
    <el-form-item label="收费项目" prop="feeProjectId">
      <dict-select v-model="formRef.formData.feeProjectId" :dict-key="KEYS.PROJECT_FEE" placeholder="请选择收费项目" />
    </el-form-item>
    <el-form-item label="数量" prop="quantity">
      <el-input-number v-model="formRef.formData.quantity" :min="1" />
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="formRef.formData.remark" type="textarea" :rows="3" :maxlength="127" placeholder="请输入备注" />
    </el-form-item>
  </detail-form>
</template>
