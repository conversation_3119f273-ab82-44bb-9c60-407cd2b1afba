package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.core.common.Result;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.ExamProjectDto;
import module.sys.entity.ExamProject;
import module.sys.entity.ExamProjectDict;
import module.sys.entity.ExamProjectFee;
import module.sys.service.ExamProjectDictService;
import module.sys.service.ExamProjectFeeService;
import module.sys.service.ExamProjectService;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 体检项目管理
 */
@Tag(name = "体检项目管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/examProject")
public class ExamProjectController {

    private final ExamProjectService examProjectService;
    private final ExamProjectDictService examProjectDictService;
    private final ExamProjectFeeService examProjectFeeService;

    @Operation(summary = "体检项目列表")
    @GetMapping("/list")
    @Perm("health:examProject")
    public Result<PageResult<ExamProjectDto.ListItem>> list(
            String type,
            String key,
            Long deptId,
            Long projectType,
            Boolean disabled,
            PageParam pageParam) {

        var result = examProjectService.getPage(w -> applyQueryCondition(w, type, key, deptId, projectType, disabled), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result.to(ExamProjectDto.ListItem.class));
    }

    @Operation(summary = "新增体检项目")
    @PostMapping("/add")
    @AuditLog(code = "health:examProject:add", value = "体检项目-新增")
    @Perm("health:examProject:add")
    public Result<Long> add(@RequestBody @Valid ExamProjectDto.EditDto dto) {
        var entity = ExamProject.builder()
                .name(dto.getName())
                .shortName(dto.getShortName())
                .printName(dto.getPrintName())
                .reportPrintName(dto.getReportPrintName())
                .costPrice(dto.getCostPrice())
                .price(dto.getPrice())
                .comboPrice(dto.getComboPrice())
                .groupPrice(dto.getGroupPrice())
                .isDiscount(dto.getIsDiscount())
                .deptId(dto.getDeptId())
                .sampleTypeId(dto.getSampleTypeId())
                .barcodePrefix(dto.getBarcodePrefix())
                .projectType(dto.getProjectType())
                .sex(dto.getSex())
                .age(dto.getAge())
                .isSpinsterhood(dto.getIsSpinsterhood())
                .isPregnancy(dto.getIsPregnancy())
                .isDrug(dto.getIsDrug())
                .isDoctorSuggest(dto.getIsDoctorSuggest())
                .isOneReport(dto.getIsOneReport())
                .isReportPrint(dto.getIsReportPrint())
                .isGuideSheet(dto.getIsGuideSheet())
                .isGuideSheetDrug(dto.getIsGuideSheetDrug())
                .isQuestionnaire(dto.getIsQuestionnaire())
                .isApplySheet(dto.getIsApplySheet())
                .dining(dto.getDining())
                .diningTime(dto.getDiningTime())
                .urine(dto.getUrine())
                .billingType(dto.getBillingType())
                .billingMessage(dto.getBillingMessage())
                .examAddressDictId(dto.getExamAddressDictId())
                .dailyOrderVolume(dto.getDailyOrderVolume())
                .applyTemplate(dto.getApplyTemplate())
                .examSignificance(dto.getExamSignificance())
                .pacsExamType(dto.getPacsExamType())
                .pacsType(dto.getPacsType())
                .isGroup(dto.getIsGroup())
                .isPacsPrint(dto.getIsPacsPrint())
                .isImageText(dto.getIsImageText())
                .isGene(dto.getIsGene())
                .examType(dto.getExamType())
                .remark(dto.getRemark())
                .disabled(dto.getDisabled())
                .build();

        return examProjectService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑体检项目")
    @GetMapping("/edit")
    @Perm("health:examProject:edit")
    public Result<ExamProjectDto.EditDto> getEdit(Long id) {
        var entityOpt = examProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new ExamProjectDto.EditDto())
        );
    }

    @Operation(summary = "编辑体检项目")
    @PostMapping("/edit")
    @AuditLog(code = "health:examProject:edit", value = "体检项目-编辑")
    @Perm("health:examProject:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid ExamProjectDto.EditDto dto) {
        var entityOpt = examProjectService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("体检项目不存在");
        }

        var examProject = entityOpt.get();
        examProject.setName(dto.getName());
        examProject.setShortName(dto.getShortName());
        examProject.setPrintName(dto.getPrintName());
        examProject.setReportPrintName(dto.getReportPrintName());
        examProject.setCostPrice(dto.getCostPrice());
        examProject.setPrice(dto.getPrice());
        examProject.setComboPrice(dto.getComboPrice());
        examProject.setGroupPrice(dto.getGroupPrice());
        examProject.setIsDiscount(dto.getIsDiscount());
        examProject.setDeptId(dto.getDeptId());
        examProject.setSampleTypeId(dto.getSampleTypeId());
        examProject.setBarcodePrefix(dto.getBarcodePrefix());
        examProject.setProjectType(dto.getProjectType());
        examProject.setSex(dto.getSex());
        examProject.setAge(dto.getAge());
        examProject.setIsSpinsterhood(dto.getIsSpinsterhood());
        examProject.setIsPregnancy(dto.getIsPregnancy());
        examProject.setIsDrug(dto.getIsDrug());
        examProject.setIsDoctorSuggest(dto.getIsDoctorSuggest());
        examProject.setIsOneReport(dto.getIsOneReport());
        examProject.setIsReportPrint(dto.getIsReportPrint());
        examProject.setIsGuideSheet(dto.getIsGuideSheet());
        examProject.setIsGuideSheetDrug(dto.getIsGuideSheetDrug());
        examProject.setIsQuestionnaire(dto.getIsQuestionnaire());
        examProject.setIsApplySheet(dto.getIsApplySheet());
        examProject.setDining(dto.getDining());
        examProject.setDiningTime(dto.getDiningTime());
        examProject.setUrine(dto.getUrine());
        examProject.setBillingType(dto.getBillingType());
        examProject.setBillingMessage(dto.getBillingMessage());
        examProject.setExamAddressDictId(dto.getExamAddressDictId());
        examProject.setDailyOrderVolume(dto.getDailyOrderVolume());
        examProject.setApplyTemplate(dto.getApplyTemplate());
        examProject.setExamSignificance(dto.getExamSignificance());
        examProject.setPacsExamType(dto.getPacsExamType());
        examProject.setPacsType(dto.getPacsType());
        examProject.setIsGroup(dto.getIsGroup());
        examProject.setIsPacsPrint(dto.getIsPacsPrint());
        examProject.setIsImageText(dto.getIsImageText());
        examProject.setIsGene(dto.getIsGene());
        examProject.setExamType(dto.getExamType());
        examProject.setRemark(dto.getRemark());
        examProject.setDisabled(dto.getDisabled());

        if (!examProjectService.update(examProject))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除体检项目")
    @PostMapping("/delete")
    @AuditLog(code = "health:examProject:delete", value = "体检项目-删除")
    @Perm("health:examProject:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            examProjectService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:examProject:enable", value = "体检项目-启用")
    @Perm("health:examProject:enable")
    public Result<Void> enable(@RequestBody List<Long> ids) {
        examProjectService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:examProject:disable", value = "体检项目-禁用")
    @Perm("health:examProject:disable")
    public Result<Void> disable(@RequestBody List<Long> ids) {
        examProjectService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "关联项目字典列表")
    @GetMapping("/listDict")
    @Perm("health:examProject:dict")
    public Result<List<ExamProjectDict>> listDict(Long id) {
        var result = examProjectDictService.getListByProject(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增关联项目字典")
    @PostMapping("/addDict")
    @AuditLog(code = "health:examProject:dict:add", value = "体检项目关联项目字典-新增")
    @Perm("health:examProject:dict:add")
    public Result<Long> addDict(@RequestBody @Valid ExamProjectDto.ProjectDict dto) {
        var entity = ExamProjectDict.builder()
                .examProjectId(dto.getExamProjectId())
                .projectDictId(dto.getProjectDictId())
                .remark(dto.getRemark())
                .build();

        return examProjectDictService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑关联项目字典")
    @GetMapping("/editDict")
    @Perm("health:examProject:dict:edit")
    public Result<ExamProjectDto.ProjectDict> getEditDict(Long id) {
        var entityOpt = examProjectDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new ExamProjectDto.ProjectDict())
        );
    }

    @Operation(summary = "编辑关联项目字典")
    @PostMapping("/editDict")
    @AuditLog(code = "health:examProject:dict:edit", value = "体检项目关联项目字典-编辑")
    @Perm("health:examProject:dict:edit")
    public Result<Void> editDict(@RequestParam Long id, @RequestBody @Valid ExamProjectDto.ProjectDict dto) {
        var entityOpt = examProjectDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("关联数据不存在");
        }

        var examProjectDict = entityOpt.get();
        examProjectDict.setExamProjectId(dto.getExamProjectId());
        examProjectDict.setProjectDictId(dto.getProjectDictId());
        examProjectDict.setRemark(dto.getRemark());

        if (!examProjectDictService.update(examProjectDict))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除关联项目字典")
    @PostMapping("/deleteDict")
    @AuditLog(code = "health:examProject:dict:delete", value = "体检项目关联项目字典-删除")
    @Perm("health:examProject:dict:delete")
    public Result<Void> deleteDict(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            examProjectDictService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "关联收费项目列表")
    @GetMapping("/listFee")
    @Perm("health:examProject:fee")
    public Result<List<ExamProjectFee>> listFee(Long id) {
        var result = examProjectFeeService.getListByProject(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增关联收费项目")
    @PostMapping("/addFee")
    @AuditLog(code = "health:examProject:fee:add", value = "体检项目关联收费项目-新增")
    @Perm("health:examProject:fee:add")
    public Result<Long> addFee(@RequestBody @Valid ExamProjectDto.ProjectFee dto) {
        var entity = ExamProjectFee.builder()
                .examProjectId(dto.getExamProjectId())
                .feeProjectId(dto.getFeeProjectId())
                .quantity(dto.getQuantity())
                .remark(dto.getRemark())
                .build();

        return examProjectFeeService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑关联收费项目")
    @GetMapping("/editFee")
    @Perm("health:examProject:fee:edit")
    public Result<ExamProjectDto.ProjectFee> getEditFee(Long id) {
        var entityOpt = examProjectFeeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new ExamProjectDto.ProjectFee())
        );
    }

    @Operation(summary = "编辑关联收费项目")
    @PostMapping("/editFee")
    @AuditLog(code = "health:examProject:fee:edit", value = "体检项目关联收费项目-编辑")
    @Perm("health:examProject:fee:edit")
    public Result<Void> editFee(@RequestParam Long id, @RequestBody @Valid ExamProjectDto.ProjectFee dto) {
        var entityOpt = examProjectFeeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("关联数据不存在");
        }

        var examProjectFee = entityOpt.get();
        examProjectFee.setExamProjectId(dto.getExamProjectId());
        examProjectFee.setFeeProjectId(dto.getFeeProjectId());
        examProjectFee.setQuantity(dto.getQuantity());
        examProjectFee.setRemark(dto.getRemark());

        if (!examProjectFeeService.update(examProjectFee))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除关联收费项目")
    @PostMapping("/deleteFee")
    @AuditLog(code = "health:examProject:fee:delete", value = "体检项目关联收费项目-删除")
    @Perm("health:examProject:fee:delete")
    public Result<Void> deleteFee(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            examProjectFeeService.deleteByIds(ids);
        }
        return Result.OK;
    }

    /**
     * 条件处理
     */
    private QueryWrapper<ExamProject> applyQueryCondition(QueryWrapper<ExamProject> wrapper, String type,
                                                          String key,
                                                          Long deptId,
                                                          Long projectType,
                                                          Boolean disabled) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.like("name", keyTrim)
                        .or().like("py", keyTrim)
                        .or().like("short_name", keyTrim)
                        .or().like("print_name", keyTrim)
                        .or().like("report_print_name", keyTrim);
            }
        }
        wrapper.eq(deptId != null, "dept_id", deptId);
        wrapper.eq(projectType != null, "project_type", projectType);
        wrapper.eq(disabled != null, "disabled", disabled);

        return wrapper;
    }
}