package infra.cache.core;

/**
 * 原子计数器接口
 */
public interface ICounter extends AutoCloseable {
    /**
     * 初始化计数器
     *
     * @param key           键
     * @param expireSeconds 计数器有效期(秒)，小于等于0为永不过期，需要手动删除
     */
    void init(String key, long expireSeconds);

    /**
     * 增加指定数量的值
     *
     * @param key    键
     * @param amount 增加的数量
     * @return 增加后的值
     */
    long incr(String key, long amount);

    /**
     * 减少指定数量的值
     *
     * @param key    键
     * @param amount 减少的数量
     * @return 减少后的值
     */
    long decr(String key, long amount);

    /**
     * 获取当前计数器值
     *
     * @param key 键
     * @return 当前值，键不存在时返回0
     */
    long get(String key);

    /**
     * 删除计数器
     *
     * @param key 键
     */
    void delete(String key);
}
