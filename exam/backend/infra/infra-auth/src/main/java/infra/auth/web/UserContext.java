package infra.auth.web;

import infra.auth.core.IUser;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 用户上下文
 * 基于Spring RequestContextHolder实现，支持虚拟线程
 */
public final class UserContext {
    private static final String USER_ATTRIBUTE_KEY = "CURRENT_USER";

    private UserContext() {
    }

    /**
     * 设置当前用户
     */
    public static void setCurrentUser(IUser user) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            attributes.setAttribute(USER_ATTRIBUTE_KEY, user, RequestAttributes.SCOPE_REQUEST);
        }
    }

    /**
     * 获取当前用户
     */
    public static IUser getCurrentUser() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return (IUser) attributes.getAttribute(USER_ATTRIBUTE_KEY, RequestAttributes.SCOPE_REQUEST);
        }
        return null;
    }

    /**
     * 清除当前用户
     */
    public static void clearCurrentUser() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            attributes.removeAttribute(USER_ATTRIBUTE_KEY, RequestAttributes.SCOPE_REQUEST);
        }
    }

    /**
     * 检查是否有当前用户
     */
    public static boolean hasCurrentUser() {
        return getCurrentUser() != null;
    }
}
