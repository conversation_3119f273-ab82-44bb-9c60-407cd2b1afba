server:
  compression:
    enabled: true

spring:
  application:
    name: admin
  profiles:
    active: dev
  threads:
    virtual:
      enabled: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************
    username: exam
    # password: ${DB-PASSWORD}
    password: 6ZjdYeH3852wPBwK
    hikari:
      pool-name: HikariCP
      minimum-idle: 4
      maximum-pool-size: 16
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  data:
    redis:
        host: ************
        port: 52390
        password: 6ZjdYeH3852wPBwK
        # password: ${REDIS-PASSWORD}
        lettuce:
          pool:
            max-active: 16
            max-idle: 8
            min-idle: 0
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1024MB
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
  main:
    banner-mode: off
  flyway:
    enabled: false
    baseline-on-migrate: true
    validate-on-migrate: true
    locations: classpath:db
    table: schema-history
    baseline-version: 0
    encoding: UTF-8
    placeholder-replacement: true
    placeholders:
      app.name: ${spring.application.name}

mybatis-plus:
  configuration:
    local-cache-scope: statement
    cache-enabled: false
    # 打印SQL日志（开发环境）
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    banner: false
    db-config:
      id-type: auto

liteflow:
  rule-source: flow/flow.xml
  print-banner: false

app:
  doc:
    swagger: true
  cache:
    provider: two-level
  auth:
    secret-key: 'VIUFm9I5DGARzcnBgsVm0dVkncRC3sqJx4HkHfWW'
    redis-token-prefix: 'auth:token:'
  oss:
    provider: s3
    local:
      path: upload
      base-url: http://127.0.0.1:8080/upload/
    s3:
      access-key: 'qDYFQ3ieI104pbbERU5F'
      secret-key: '9iiuPbXQO2NYiC0XrLHZVUlwFDsYJ7niRZq33jdR'
      endpoint: 'http://************:52391'
      bucket: 'exam-dev'
  audit:
    enabled: true
    async:
      max-pool-size: 36
  sms:
    provider: log
  domain:
    field-encrypt:
      enabled: false
      provider: aes
      aes:
        secret-key: 'uk83YUQQ5UMxxGnBzpeibQuQYl2flMcyGlIWwVSqUVg='
  cors:
    enabled: true
    allowed-origins:
      - '*'
    allowed-methods: [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ]
    allowed-headers: [ "*" ]
    allow-credentials: true
    max-age: 3600
  sse:
    enabled: true
    notice:
      enabled: true
      secret-key: 'uk83YUQQ5UMxxGnBzpeibQuQYl2flMcyGlIWwVSqUVg='
      redis: true
  protect:
    enabled: true
    rate-limit:
      provider: redis
      default-strategy:
        count: 10
        window: 60
  module:
    sys:
      default-password: admin
      audit-log-days: 3
  integration:
#    api1:
#      datasource:
#        # HikariCP配置，使用jdbcUrl而不是url
#        jdbc-url: ********************************
#        username: root
#        password: ruson523
#        driver-class-name: com.mysql.cj.jdbc.Driver
#        # HikariCP连接池配置
#        maximum-pool-size: 10
#        minimum-idle: 2
#      config:
#        # 其他API配置
#        test: aa
#        timeout: 30000
#        retries: 3
#        endpoint: "https://api1.example.com"