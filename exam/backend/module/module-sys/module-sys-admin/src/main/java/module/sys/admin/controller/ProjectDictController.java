package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.entity.IdEntity;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.ProjectDictDto;
import module.sys.entity.DataDictValue;
import module.sys.entity.ProjectDict;
import module.sys.service.DataDictService;
import module.sys.service.ProjectDictService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "项目字典")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/projectDict")
public class ProjectDictController {
    private static final String PROJECT_TYPE_DICT_ID = "project_type";
    private static final String BODY_PART_DICT_ID = "body_part";

    private final ProjectDictService projectDictService;
    private final DataDictService dataDictService;

    @Operation(summary = "项目字典列表")
    @GetMapping("/list")
    @Perm("health:projectDict")
    public Result<PageResult<ProjectDict>> list(
            String type,
            String key,
            Long typeId,
            PageParam pageParam) {

        var result = projectDictService.getPage(w -> applyQueryCondition(w, type, key, typeId), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增项目字典")
    @PostMapping("/add")
    @AuditLog(code = "health:projectDict:add", value = "新增项目字典")
    @Perm("health:projectDict:add")
    public Result<Long> add(@RequestBody @Valid ProjectDictDto.EditDto dto) {
        ProjectDict projectDict = ProjectDict.builder()
                .name(dto.getName())
                .typeId(dto.getTypeId())
                .englishName(dto.getEnglishName())
                .printName(dto.getPrintName())
                .code(dto.getCode())
                .valueType(dto.getValueType())
                .valueUnit(dto.getValueUnit())
                .bodyPartIds(Str.idsToString(dto.getBodyPartIds()))
                .remark(dto.getRemark())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        if (!projectDictService.add(projectDict))
            return Result.FAIL_LONG;
        return Result.okData(projectDict.getId());
    }

    @Operation(summary = "编辑项目字典")
    @GetMapping("/edit")
    @Perm("health:projectDict:edit")
    public Result<ProjectDictDto.EditDto> getEdit(Long id) {
        var entity = projectDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return entity.map(item -> {
            ProjectDictDto.EditDto dto = ObjectUtil.copyTo(item, new ProjectDictDto.EditDto());
            dto.setBodyPartIds(Str.idsToArray(item.getBodyPartIds()));
            return Result.okData(dto);
        }).orElseGet(() -> Result.fail("项目字典不存在"));
    }

    @Operation(summary = "编辑项目字典")
    @PostMapping("/edit")
    @AuditLog(code = "health:projectDict:edit", value = "编辑项目字典")
    @Perm("health:projectDict:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid ProjectDictDto.EditDto dto) {
        var entity = projectDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("项目字典不存在");
        }

        ProjectDict projectDict = entity.get();
        projectDict.setName(dto.getName());
        projectDict.setTypeId(dto.getTypeId());
        projectDict.setEnglishName(dto.getEnglishName());
        projectDict.setPrintName(dto.getPrintName());
        projectDict.setCode(dto.getCode());
        projectDict.setValueType(dto.getValueType());
        projectDict.setValueUnit(dto.getValueUnit());
        projectDict.setBodyPartIds(Str.idsToString(dto.getBodyPartIds()));
        projectDict.setRemark(dto.getRemark());
        projectDict.setSort(dto.getSort());
        projectDict.setDisabled(dto.getDisabled());

        if (!projectDictService.update(projectDict))
            return Result.FAIL;
        return Result.OK;
    }

    @Operation(summary = "删除项目字典")
    @PostMapping("/delete")
    @AuditLog(code = "health:projectDict:delete", value = "删除项目字典")
    @Perm("health:projectDict:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            projectDictService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "批量启用项目字典")
    @PostMapping("/enable")
    @AuditLog(code = "health:projectDict:enable", value = "批量启用项目字典")
    @Perm("health:projectDict:enable")
    public Result<Void> enable(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            projectDictService.updateBatch(wrapper ->
                    wrapper.in("id", ids)
                            .eq("disabled", true)
                            .set("disabled", false));
        }
        return Result.OK;
    }

    @Operation(summary = "批量禁用项目字典")
    @PostMapping("/disable")
    @AuditLog(code = "health:projectDict:disable", value = "批量禁用项目字典")
    @Perm("health:projectDict:disable")
    public Result<Void> disable(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            projectDictService.updateBatch(wrapper ->
                    wrapper.in("id", ids)
                            .eq("disabled", false)
                            .set("disabled", true));
        }
        return Result.OK;
    }

    @Operation(summary = "导出项目字典")
    @PostMapping("/export")
    @AuditLog(code = "health:projectDict:export", value = "项目字典-导出")
    @Perm("health:projectDict:export")
    public void export(
            String type,
            String key,
            Long typeId,
            HttpServletResponse response) throws IOException {

        var projectTypes = dataDictService.getValuesByCode(PROJECT_TYPE_DICT_ID);
        var bodyParts = dataDictService.getValuesByCode(BODY_PART_DICT_ID);

        ExcelExporter.<ProjectDict>create()
                .sheet("项目字典")
                .columns(cols ->
                        cols.add("项目名称", ProjectDict::getName)
                                .add("项目类型", value -> projectTypes.stream()
                                        .filter(item -> item.getId().equals(value.getTypeId()))
                                        .findFirst()
                                        .map(DataDictValue::getName)
                                        .orElse(""))
                                .add("英文名称", ProjectDict::getEnglishName)
                                .add("打印名称", ProjectDict::getPrintName)
                                .add("接口编码", ProjectDict::getCode)
                                .add("值类型", dict -> "C".equals(dict.getValueType()) ? "字符" : "数值")
                                .add("值单位", ProjectDict::getValueUnit)
                                .add("身体部位", value -> {
                                    if (Str.isEmpty(value.getBodyPartIds())) {
                                        return "";
                                    }
                                    String[] bodyPartIdArray = value.getBodyPartIds().split(",");
                                    return bodyParts.stream()
                                            .filter(item -> {
                                                for (String bodyPartId : bodyPartIdArray) {
                                                    if (item.getId().equals(Long.valueOf(bodyPartId.trim()))) {
                                                        return true;
                                                    }
                                                }
                                                return false;
                                            })
                                            .map(DataDictValue::getName)
                                            .reduce((a, b) -> a + "," + b)
                                            .orElse("");
                                })
                                .add("排序值", ProjectDict::getSort)
                                .add("备注", ProjectDict::getRemark)
                                .add("状态", dict -> dict.getDisabled() ? "禁用" : "启用")
                )
                .fromService(projectDictService, w -> applyQueryCondition(w, type, key, typeId), FilterOptions.DISABLE_ALL_FILTER)
                .export(response, "项目字典");
    }

    @Operation(summary = "导入项目字典")
    @PostMapping("/import")
    @AuditLog(code = "health:projectDict:import", value = "项目字典-导入")
    @Perm("health:projectDict:import")
    @Transactional
    public Result<Void> importData(@RequestParam("files") MultipartFile file) {
        var projectTypes = dataDictService.getValuesByCode(PROJECT_TYPE_DICT_ID);
        var bodyParts = dataDictService.getValuesByCode(BODY_PART_DICT_ID);

        try {
            ImportResult<ProjectDict> parseResult = ExcelImporter.create(ProjectDict::new)
                    .columns(cols -> {
                        cols.required("项目名称", ExcelParser.string(), ProjectDict::setName);
                        cols.required("项目类型", value ->
                                        projectTypes.stream().filter(item -> item.getName().equals(String.valueOf(value)))
                                                .findFirst().map(IdEntity::getId)
                                                .orElseThrow(() -> new BizException("项目类型不存在"))
                                , ProjectDict::setTypeId);
                        cols.add("英文名称", ExcelParser.string(), ProjectDict::setEnglishName);
                        cols.add("打印名称", ExcelParser.string(), ProjectDict::setPrintName);
                        cols.add("接口编码", ExcelParser.string(), ProjectDict::setCode);
                        cols.withDefault("值类型", value -> "字符".equals(value) ? "C" : "N", ProjectDict::setValueType, "C");
                        cols.add("值单位", ExcelParser.string(), ProjectDict::setValueUnit);
                        cols.add("身体部位", value -> {
                            if (value == null || String.valueOf(value).trim().isEmpty()) {
                                return null;
                            }
                            String[] bodyPartNames = String.valueOf(value).split(",");
                            StringBuilder bodyPartIds = new StringBuilder();
                            for (String bodyPartName : bodyPartNames) {
                                String trimmedName = bodyPartName.trim();
                                var bodyPart = bodyParts.stream()
                                        .filter(item -> item.getName().equals(trimmedName))
                                        .findFirst()
                                        .orElseThrow(() -> new BizException("身体部位不存在: " + trimmedName));
                                
                                if (!bodyPartIds.isEmpty()) {
                                    bodyPartIds.append(",");
                                }
                                bodyPartIds.append(bodyPart.getId());
                            }
                            return bodyPartIds.toString();
                        }, ProjectDict::setBodyPartIds);
                        cols.withDefault("排序值", ExcelParser.integer(), ProjectDict::setSort, 0);
                        cols.add("备注", ExcelParser.string(), ProjectDict::setRemark);
                        cols.withDefault("状态", "禁用"::equals, ProjectDict::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            projectDictService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "获取项目字典")
    @GetMapping("/dict")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<Long>>> getDict() {
        return Result.okData(projectDictService.getList().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getId(), w.getName()))
                .toList());
    }

    /**
     * 条件处理
     */
    private QueryWrapper<ProjectDict> applyQueryCondition(QueryWrapper<ProjectDict> wrapper, String type, String key, Long typeId) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.like("name", keyTrim).or()
                        .like("py", keyTrim).or()
                        .like("english_name", keyTrim).or()
                        .like("print_name", keyTrim);
            }

            wrapper.like("code".equals(type), "code", keyTrim);
        }
        wrapper.eq(typeId != null, "type_id", typeId);
        return wrapper;
    }
}
