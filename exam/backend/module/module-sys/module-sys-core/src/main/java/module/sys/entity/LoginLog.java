package module.sys.entity;

import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 登录日志
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_login_log")
public class LoginLog extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 用户Id
    private Long userId;
    // 登录账号
    private String loginName;
    // 用户名称
    private String userName;
    // 登录时间
    private LocalDateTime loginTime;
    // 登录方式
    private LoginType loginType;
    // IP
    private String ip;
    // 客户端/浏览器标识
    private String client;
    // 是否成功
    private Boolean success;
    // 错误消息
    private String error;

    /**
     * 登录方式枚举
     */
    @Getter
    @AllArgsConstructor
    public enum LoginType {
        USER_PASSWORD(0),
        WEIXIN_QRCODE(1);

        @EnumValue
        private final Integer value;
    }
}
