import { get, post } from '~/utils/request'

const baseUrl = '/sys/idBuilder'

/**
 * Id生成器列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增Id生成器
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑Id生成器(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑Id生成器(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除Id生成器
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}
