package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.doc.IDocTemplate;
import module.sys.entity.DocTemplate;
import module.sys.mapper.DocTemplateMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;

/**
 * 文书模板服务
 */
@Slf4j
@Service
public class DocTemplateService extends ServiceBase<DocTemplateMapper, DocTemplate> {
    @Override
    protected Function<QueryWrapper<DocTemplate>, QueryWrapper<DocTemplate>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<DocTemplate>, QueryWrapper<DocTemplate>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    /**
     * 注册文书模板
     */
    @Transactional
    public void register(List<IDocTemplate> templates) {
        if (templates == null || templates.isEmpty())
            return;

        int add = 0, update = 0;

        for (IDocTemplate template : templates) {
            var templateOpt = getFirst(w ->
                            w.eq("code", template.getCode())
                                    .eq("is_sys", true),
                    FilterOptions.DISABLE_ALL_FILTER
            );

            if (templateOpt.isPresent()) {
                var entity = templateOpt.get();
                // 检查是否有变化
                if (
                        !template.getTemplate().equals(entity.getTemplate()) ||
                                !template.getName().equals(entity.getName())
                ) {
                    entity.setTemplate(template.getTemplate());
                    entity.setName(template.getName());
                    update(entity);
                    update++;
                }
            } else {
                var entity = new DocTemplate();
                entity.setName(template.getName());
                entity.setCode(template.getCode());
                entity.setTemplate(template.getTemplate());
                entity.setSort(template.getSort());
                entity.setIsSys(true);
                add(entity);
                add++;
            }
        }

        log.info("注册文书模板，新增{}条，更新{}条", add, update);
    }

    /**
     * 根据编号获取所有文书模板
     */
    public List<DocTemplate> getListByCode(String code) {
        return getList(w -> w.eq("code", code));
    }
}
