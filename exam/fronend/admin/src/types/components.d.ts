/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChangePassword: typeof import('./../components/auth/ChangePassword.vue')['default']
    DeptSelect: typeof import('./../components/common/DeptSelect.vue')['default']
    DetailForm: typeof import('./../components/base/DetailForm.vue')['default']
    DictSelect: typeof import('./../components/common/DictSelect.vue')['default']
    DocView: typeof import('./../components/base/DocView.vue')['default']
    ElAffix: typeof import('element-plus/es')['ElAffix']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLine: typeof import('element-plus/es')['ElLine']
    ElLineChart: typeof import('element-plus/es')['ElLineChart']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSplitter: typeof import('element-plus/es')['ElSplitter']
    ElSplitterPanel: typeof import('element-plus/es')['ElSplitterPanel']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ElXAxis: typeof import('element-plus/es')['ElXAxis']
    ElYAxis: typeof import('element-plus/es')['ElYAxis']
    EpFileUpload: typeof import('./../components/base/EpFileUpload.vue')['default']
    FileUpload: typeof import('./../components/base/FileUpload.vue')['default']
    GridPane: typeof import('./../components/base/GridPane.vue')['default']
    GridPanel: typeof import('./../components/base/GridPanel.vue')['default']
    GridPanl: typeof import('./../components/base/GridPanl.vue')['default']
    Header: typeof import('./../components/layouts/Header.vue')['default']
    LeftSide: typeof import('./../components/layouts/LeftSide.vue')['default']
    NameSelect: typeof import('./../components/base/NameSelect.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SliderVerify: typeof import('./../components/auth/SliderVerify.vue')['default']
    TableList: typeof import('./../components/base/TableList.vue')['default']
    Tabs: typeof import('./../components/layouts/Tabs.vue')['default']
    UserSelect: typeof import('./../components/common/UserSelect.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
