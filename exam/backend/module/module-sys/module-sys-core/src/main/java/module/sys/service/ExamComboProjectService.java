package module.sys.service;

import infra.core.exception.BizException;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ExamComboProject;
import module.sys.mapper.ExamComboProjectMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 体检套餐与体检项目关联服务
 */
@Slf4j
@Service
public class ExamComboProjectService extends ServiceBase<ExamComboProjectMapper, ExamComboProject> {
    @Override
    protected void beforeSave(ExamComboProject entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (exists(w ->
                w.eq("exam_combo_id", entity.getExamComboId())
                        .eq("exam_project_id", entity.getExamProjectId())
                        .ne(isUpdate, "id", entity.getId()))) {
            throw new BizException("该关联数据已存在");
        }
    }

    /**
     * 获取体检套餐与体检项目关联列表
     */
    public List<ExamComboProject> getListByCombo(Long id) {
        return getList(w -> w.eq("exam_combo_id", id));
    }
}
