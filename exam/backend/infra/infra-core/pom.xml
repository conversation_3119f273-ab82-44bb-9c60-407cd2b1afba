<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>infra</groupId>
        <artifactId>infra</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>infra-core</artifactId>
    <description>常用基础功能</description>

    <dependencies>
        <!-- 基础依赖 -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- json -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- 加密算法，用于支持国密SM4 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
        </dependency>

        <!-- 拼音 -->
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin-lexicons-java-cncity</artifactId>
        </dependency>

        <!-- 压缩库 -->
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
        </dependency>

        <!-- 文档 -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>