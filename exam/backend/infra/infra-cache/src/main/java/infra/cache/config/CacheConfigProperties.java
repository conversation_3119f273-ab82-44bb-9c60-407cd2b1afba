package infra.cache.config;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 缓存配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.cache")
public class CacheConfigProperties {
    // 缓存类型： local, redis, two-level, none
    @NotBlank
    private String provider;
    // 本地缓存配置
    private LocalConfig local = new LocalConfig();
    // Redis缓存配置
    private RedisConfig redis = new RedisConfig();
    // 二级缓存配置
    private TwoLevelConfig twoLevel = new TwoLevelConfig();

    /**
     * 本地缓存配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocalConfig {
        private int maxSize = 10_000;
    }

    /**
     * Redis缓存配置(保留，默使用用SpringBoot配置)
     */
    @Data
    public static class RedisConfig {
    }

    /**
     * 二级缓存配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TwoLevelConfig {
        // 本地缓存最大数量
        private int localMaxSize = 2_000;
        // 缓存失效消息频道前缀
        private String invalidateChannelPrefix = "cache:invalidate:";
    }
}
