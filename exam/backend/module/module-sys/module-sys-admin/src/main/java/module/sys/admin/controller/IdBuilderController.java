package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.IdBuilderDto;
import module.sys.entity.IdBuilder;
import module.sys.service.IdBuilderService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "Id生成器配置")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/idBuilder")
public class IdBuilderController {
    private final IdBuilderService idBuilderService;

    @Operation(summary = "ID生成器列表")
    @GetMapping("/list")
    @Perm("sys:idBuilder")
    public Result<PageResult<IdBuilder>> list(
            String code,
            PageParam pageParam) {

        var result = idBuilderService.getPage(w -> w.like(!Str.isEmpty(code), "code", code.trim()), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增ID生成器")
    @PostMapping("/add")
    @AuditLog(code = "sys:idBuilder:add", value = "ID生成器-新增")
    @Perm("sys:idBuilder:add")
    public Result<Long> add(@RequestBody @Valid IdBuilderDto.EditDto dto) {
        IdBuilder entity = IdBuilder.builder()
                .code(dto.getCode())
                .len(dto.getLen())
                .startNo(dto.getStartNo())
                .about(dto.getAbout())
                .build();

        return idBuilderService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑ID生成器")
    @GetMapping("/edit")
    @Perm("sys:idBuilder:edit")
    public Result<IdBuilderDto.EditDto> getEdit(Long id) {
        var entityOpt = idBuilderService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new IdBuilderDto.EditDto())
        );
    }

    @Operation(summary = "编辑ID生成器")
    @PostMapping("/edit")
    @AuditLog(code = "sys:idBuilder:edit", value = "ID生成器-编辑")
    @Perm("sys:idBuilder:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid IdBuilderDto.EditDto dto) {
        var entityOpt = idBuilderService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("ID生成器不存在");
        }

        IdBuilder entity = entityOpt.get();
        entity.setCode(dto.getCode());
        entity.setLen(dto.getLen());
        entity.setStartNo(dto.getStartNo());
        entity.setAbout(dto.getAbout());

        return idBuilderService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除ID生成器")
    @PostMapping("/delete")
    @AuditLog(code = "sys:idBuilder:delete", value = "ID生成器-删除")
    @Perm("sys:idBuilder:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            idBuilderService.deleteByIds(ids);
        }
        return Result.OK;
    }
}