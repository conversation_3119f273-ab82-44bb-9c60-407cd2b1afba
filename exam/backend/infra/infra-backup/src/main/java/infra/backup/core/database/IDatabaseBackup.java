package infra.backup.core.database;

import infra.backup.config.BackupConfigProperties;
import infra.backup.exception.BackupException;

import java.io.File;

/**
 * 数据库备份接口
 */
public interface IDatabaseBackup {
    
    /**
     * 支持的数据库类型
     * 
     * @param url 数据库连接URL
     * @return true表示支持
     */
    boolean supports(String url);
    
    /**
     * 备份数据库到临时文件
     * 
     * @param connectionConfig 连接配置
     * @return 备份文件，调用方负责删除
     * @throws BackupException 备份失败时抛出
     */
    File backup(BackupConfigProperties.ConnectionConfig connectionConfig);
}