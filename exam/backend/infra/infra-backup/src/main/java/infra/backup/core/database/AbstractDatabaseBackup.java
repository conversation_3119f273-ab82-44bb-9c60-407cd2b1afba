package infra.backup.core.database;

import infra.backup.config.BackupConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 数据库备份抽象基类
 * 提供通用的表过滤和工具方法
 */
@Slf4j
public abstract class AbstractDatabaseBackup implements IDatabaseBackup {
    
    private static final Pattern WILDCARD_PATTERN = Pattern.compile("[*?]");
    
    /**
     * 获取需要备份的表列表
     * 
     * @param allTables 数据库中所有表
     * @param config 连接配置
     * @return 需要备份的表列表
     */
    protected List<String> filterBackupTables(List<String> allTables, BackupConfigProperties.ConnectionConfig config) {
        if (CollectionUtils.isEmpty(allTables)) {
            return Collections.emptyList();
        }

        return allTables.stream()
                .filter(item -> shouldBackupTable(item, config))
                .toList();
    }
    
    /**
     * 判断表是否需要备份
     * 
     * @param tableName 表名
     * @param config 连接配置
     * @return true表示需要备份
     */
    protected boolean shouldBackupTable(String tableName, BackupConfigProperties.ConnectionConfig config) {
        // 检查排除列表
        if (!CollectionUtils.isEmpty(config.getExcludeTables())) {
            for (String excludePattern : config.getExcludeTables()) {
                if (matchesPattern(tableName, excludePattern)) {
                    log.debug("表 {} 匹配排除模式 {}, 跳过备份", tableName, excludePattern);
                    return false;
                }
            }
        }

        return true;
    }
    
    /**
     * 通配符模式匹配
     * 支持 * 和 ? 通配符
     * 
     * @param text 待匹配的文本
     * @param pattern 模式字符串
     * @return true表示匹配
     */
    protected boolean matchesPattern(String text, String pattern) {
        if (text == null || pattern == null) {
            return false;
        }
        
        // 如果模式中没有通配符，直接比较
        if (!WILDCARD_PATTERN.matcher(pattern).find()) {
            return text.equals(pattern);
        }
        
        // 将通配符转换为正则表达式
        String regex = pattern
                .replace(".", "\\.")  // 转义点号
                .replace("*", ".*")   // * 匹配任意字符
                .replace("?", ".");   // ? 匹配单个字符
        
        return text.matches(regex);
    }

    /**
     * 转义SQL字符串值
     * 
     * @param value 原始值
     * @return 转义后的值
     */
    protected String escapeSqlValue(String value) {
        if (value == null) {
            return "NULL";
        }
        return "'" + value.replace("'", "''").replace("\\", "\\\\") + "'";
    }
}