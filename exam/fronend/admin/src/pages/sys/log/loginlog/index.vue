<script setup lang="ts">
import * as api from '~/api/sys/loginLog'
import { formatDate, formatElCellDateTime, resetForm } from '~/utils'

definePage({
  meta: {
    title: '登录日志',
    perm: 'sys:loginlog',
    menu: true,
    order: 100,
  },
})

// 状态及数据
const query = reactive({
  type: 'userName',
  key: '',
  status: undefined as undefined | boolean,
  startTime: '',
  endTime: '',
  timeRange: undefined as any,
})
const formRef = useTemplateRef('formRef')
const tableRef = useTemplateRef<any>('tableRef')
const queryParams = computed(() => {
  return {
    ...query,
    timeRange: undefined,
  }
})

// 监听变更
watch(() => query.timeRange, (newVal) => {
  if (newVal) {
    query.startTime = formatDate(newVal[0] as Date)
    query.endTime = formatDate(newVal[1] as Date)
  }
  else {
    query.startTime = ''
    query.endTime = ''
  }
})
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="queryParams">
      <template #left-actions>
        <el-form
          ref="formRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(formRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="用户" value="userName" />
              <el-option label="IP" value="ip" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="status" label="状态" class="w-36">
            <el-select v-model="query.status" clearable>
              <el-option label="成功" :value="true" />
              <el-option label="失败" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item prop="timeRange" label="登录时间" class="w-80">
            <el-date-picker
              v-model="query.timeRange" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="loginName" label="登录账号" width="150" />
      <el-table-column prop="userName" label="用户姓名" width="150" />
      <el-table-column prop="loginTime" label="登录时间" align="center" width="200" :formatter="formatElCellDateTime" />
      <el-table-column prop="ip" label="登录IP" align="center" width="200" />
      <el-table-column prop="success" label="状态" align="center" width="130">
        <template #default="{ row }">
          <el-tag :type="row.success ? 'success' : 'danger'">
            {{ row.success ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="error" label="提示信息" show-overflow-tooltip />
    </table-list>
  </div>
</template>
