<script setup lang="ts">
definePage({
  meta: {
    title: '意向跟踪',
    menu: true,
    order: 2,
    icon: 'fluent-mdl2:issue-tracking-mirrored',
  },
})
</script>

<template>
  <p>1. 登记每次客户联系记录（沟通时间、需求反馈、跟进计划）；</p>
  <p>2. 同步客户过往体检记录（若有），整合历史健康数据展示；</p>
  <p>3. 录入健康指导意见并关联对应意向记录，形成跟进闭环；</p>
  <p>4. 跟踪意向状态变化（如 “潜在意向→预约中→已预约→已完成体检”），对超期未转化意向自动标记提醒；</p>
  <p>5. 按意向来源、状态、时间维度筛选跟踪记录，支持导出</p>

  <p>客户挖掘阶段 “客户意向导入（标记潜在体检客户）”“体检套餐推荐（基于客户基本信息、过往健康数据（若有）以及健康需求调研结果，自动匹配个性化套餐推荐方案）”；业务部门 “客户信息全链路管理（整合客户从咨询到健康管理的全周期信息，实时查看客户来源渠道、套餐选择偏好、历史体检记录等数据）”</p>
</template>
