package infra.sse.notice.core;

import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.sse.notice.model.NoticeMessage;

import java.util.List;

/**
 * SSE通知服务接口
 */
public interface INoticeService {
    /**
     * 发送通知消息给指定用户
     */
    void noticeUser(Long userId, NoticeMessage message);

    /**
     * 发送通知消息给多个用户
     */
    void noticeUsers(List<Long> userIds, NoticeMessage message);

    /**
     * 获取用户未读消息数量
     */
    int getUnreadCount(Long userId);
    
    /**
    * 标记消息为已读
     */
    void markAsRead(Long userId, List<Long> ids);

    /**
     * 标记用户所有消息为已读
     */
    void markAllAsRead(Long userId);

    /**
     * 用户删除消息
     */
    void delete(Long userId, List<Long> ids);

    /**
     * 用户删除所有消息
     */
    void deleteAll(Long userId);

    /**
     * 获取用户最新n条通知
     */
    List<NoticeMessage> getList(Long userId, int limit);

    /**
     * 分页获取通知
     */
    PageResult<NoticeMessage> getPage(Long userId, NoticeMessage query, PageParam pageParam);
}
