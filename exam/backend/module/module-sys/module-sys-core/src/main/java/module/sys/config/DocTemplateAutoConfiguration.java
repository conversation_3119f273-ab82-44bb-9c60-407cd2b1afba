package module.sys.config;

import lombok.extern.slf4j.Slf4j;
import module.sys.doc.IDocTemplate;
import module.sys.service.DocTemplateService;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;

/**
 * 文书模板自动配置
 */
@AutoConfiguration
@Slf4j
public class DocTemplateAutoConfiguration {
    /**
     * 在启动后自动注册所有文书模板
     */
    @Bean
    public ApplicationListener<ApplicationReadyEvent> sysDocTemplateRegistrationListener(
            ApplicationContext applicationContext,
            DocTemplateService docTemplateService
    ) {
        return event -> {
            try {
                var docBeans = applicationContext.getBeansOfType(IDocTemplate.class);
                if (!docBeans.isEmpty()) {
                    var templates = docBeans.values().stream().toList();
                    docTemplateService.register(templates);
                    log.info("[配置] 共注册 {} 个文书模板", templates.size());
                }
            } catch (Exception e) {
                log.error("[配置] 注册文书模板失败", e);
            }
        };
    }
}
