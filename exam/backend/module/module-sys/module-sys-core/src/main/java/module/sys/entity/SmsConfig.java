package module.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 短信配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_sms_config")
public class SmsConfig extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 消息id
    private String messageId;
    // 内容模板
    private String contentTemplate;
    // 对应短信提供商的模板编号
    private String supplierTemplateCode;
    // 排序值
    private Integer sort;
    // 是否禁用
    private Boolean disabled;
    // 修改时间
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    // 修改人
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    // 修改人名称
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;
}