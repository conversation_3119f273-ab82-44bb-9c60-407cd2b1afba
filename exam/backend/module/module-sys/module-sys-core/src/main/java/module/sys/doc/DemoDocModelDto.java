package module.sys.doc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DemoDocModelDto {
    @Schema(description = "标题")
    private String title;
    @Schema(description = "时间")
    private String time;
    @Schema(description = "用户列表")
    private List<UserDto> users;

    @Data
    @AllArgsConstructor
    @Schema(description = "用户信息")
    public static class UserDto {
        @Schema(description = "姓名")
        private String name;
        @Schema(description = "年龄")
        private int age;
        @Schema(description = "职业")
        private String work;
    }
}
