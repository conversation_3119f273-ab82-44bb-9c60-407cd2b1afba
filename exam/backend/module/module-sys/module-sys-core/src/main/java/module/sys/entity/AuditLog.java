package module.sys.entity;

import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 审计日志
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("sys_audit_log")
public class AuditLog extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    // 模块
    private String module;
    // 操作编码(可以考虑同权限编号)
    private String code;
    // 操作详情
    private String detail;
    // 用户ID
    private Long userId;
    // 登录账号
    private String loginName;
    // 用户名
    private String userName;
    // URL
    private String url;
    // 操作IP
    private String ip;
    // 附属数据
    private String data;
    // 是否成功
    private Boolean success;
    // 错误信息
    private String error;
    // 请求时间
    private LocalDateTime startTime;
    // 处理耗时(毫秒)
    private Long useTime;
}
