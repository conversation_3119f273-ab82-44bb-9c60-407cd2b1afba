package module.sys.admin.dto;

import jakarta.validation.constraints.*;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.CustomerLimit;
import module.sys.model.SexLimit;

import java.math.BigDecimal;

/**
 * 体检套餐Dto
 */
public class ExamComboDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 套餐代码
        private String code;
        // 套餐名称
        @NotBlank(message = "套餐名称不能为空")
        private String name;
        // 套餐价格
        private BigDecimal price;
        // 体检类型字典ID
        private Long examType;
        // 适用性别
        private SexLimit sex;
        // 适用年龄阶段
        private AgeLimit age;
        // 适用客户类型 0：个人团队皆可 1：个人 2：团队
        private CustomerLimit customer;
        // 排序值
        private Integer sort;
        // 备注
        private String remark;
        // 禁用
        private Boolean disabled;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ComboProject {
        // 体检套餐ID
        private Long examComboId;
        // 体检项目ID
        private Long examProjectId;
        // 体检项目名称(仅用于导出或显示)
        private String examProjectName;
        // 套餐价格(仅用于显示)
        private BigDecimal comboPrice;
    }
}