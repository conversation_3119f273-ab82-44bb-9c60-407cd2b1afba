package module.sys.admin.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 收费项目Dto
 */
public class FeeProjectDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 编号
        private String feeNo;
        // 国家编码
        private String countryNo;
        // 项目名称
        private String projectName;
        // 标准金额
        private BigDecimal standardAmount;
        // 收费金额
        private BigDecimal feeAmount;
        // 发票金额
        private BigDecimal invoiceAmount;
        // 批准文号
        private String approvalNumber;
        // 单位
        private String unit;
        // 规格
        private String spec;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
    }    
}