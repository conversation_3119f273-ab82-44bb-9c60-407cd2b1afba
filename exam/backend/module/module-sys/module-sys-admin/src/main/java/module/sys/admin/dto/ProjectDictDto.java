package module.sys.admin.dto;

import lombok.*;

/**
 * 检查项目字典Dto
 */
public class ProjectDictDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 项目字典名称
        private String name;
        // 项目类型ID
        private Long typeId;
        // 项目字典英文名称
        private String englishName;
        // 项目字典打印名称
        private String printName;
        // 项目字典第三方接口编码
        private String code;
        // 值类型 C：字符 N：数值
        private String valueType;
        // 值单位
        private String valueUnit;
        // 对应身体部位字典ids
        private Long[] bodyPartIds;
        // 排序值
        private Integer sort;
        // 备注
        private String remark;
        // 禁用
        private Boolean disabled;
    }    
}