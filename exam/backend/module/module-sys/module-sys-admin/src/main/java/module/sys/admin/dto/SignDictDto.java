package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * 体征词字典Dto
 */
public class SignDictDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 体征名称
        @NotBlank(message = "体征名称名称不能为空")
        private String name;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
        // 体征描述
        private String info;
    }    
}