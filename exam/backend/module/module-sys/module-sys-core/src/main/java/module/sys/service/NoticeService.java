package module.sys.service;

import com.fasterxml.jackson.core.type.TypeReference;
import infra.core.text.JSON;
import infra.core.text.Str;
import infra.domain.entity.IdEntity;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.ServiceBase;
import infra.sse.notice.core.INoticeStorage;
import infra.sse.notice.model.NoticeMessage;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Notice;
import module.sys.mapper.NoticeMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 基于数据库的通知存储实现
 */
@Slf4j
@Service
public class NoticeService extends ServiceBase<NoticeMapper, Notice> implements INoticeStorage {

    @Override
    public Long saveMessage(Long userId, NoticeMessage message) {
        if (userId == null || message == null) {
            return 0L;
        }

        Notice record = convertToRecord(userId, message);
        add(record);
        return record.getId();
    }

    @Override
    public List<Long> saveMessage(List<Long> userIds, NoticeMessage message) {
        if (CollectionUtils.isEmpty(userIds) || message == null) {
            return Collections.emptyList();
        }

        List<Notice> records = userIds.stream()
                .map(userId -> convertToRecord(userId, message))
                .toList();

        // 因要回填id，所以只能单个插入
        records.forEach(this::add);
        return records.stream().map(IdEntity::getId).toList();
    }

    @Override
    public void markAsRead(Long userId, List<Long> ids) {
        if (userId == null || CollectionUtils.isEmpty(ids)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        updateBatch(w -> w.set("is_read", true)
                .set("update_time", now)
                .eq("user_id", userId)
                .in("id", ids));
    }

    @Override
    public void markAllAsRead(Long userId) {
        if (userId == null) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        updateBatch(w ->
                w.set("is_read", true)
                        .set("update_time", now)
                        .eq("user_id", userId));
    }

    @Override
    public int getUnreadCount(Long userId) {
        if (userId == null) {
            return 0;
        }

        return (int) count(w -> w.eq("user_id", userId)
                .eq("is_read", false));
    }

    @Override
    public void delete(Long userId, List<Long> ids) {
        if (userId == null || CollectionUtils.isEmpty(ids)) {
            return;
        }

        deleteBatch(w -> w.eq("user_id", userId)
                .in("id", ids));
    }

    @Override
    public void deleteAll(Long userId) {
        if (userId == null) {
            return;
        }

        deleteBatch(w -> w.eq("user_id", userId));
    }

    @Override
    public List<NoticeMessage> getList(Long userId, int limit) {
        if (userId == null) {
            return Collections.emptyList();
        }

        List<Notice> records = getList(w -> w.eq("user_id", userId), limit);
        return records.stream()
                .map(this::convertToMessage)
                .toList();
    }

    @Override
    public PageResult<NoticeMessage> getPage(Long userId, NoticeMessage query, PageParam pageParam) {
        if (userId == null || pageParam == null) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        PageResult<Notice> recordPage = getPage(w -> {
            w.eq("user_id", userId);

            // 添加查询条件
            if (query != null) {
                if (!Str.isEmpty(query.getTitle())) {
                    w.like("title", query.getTitle())
                            .or()
                            .like("content", query.getTitle());
                }
                if (query.getType() != null) {
                    w.eq("type", query.getType());
                }
                if (query.getIsRead() != null) {
                    w.eq("is_read", query.getIsRead());
                }
                if (query.getSenderId() != null) {
                    w.eq("sender_id", query.getSenderId());
                }
                if (!Str.isEmpty(query.getSenderName())) {
                    w.like("sender_name", query.getSenderName());
                }
            }
        }, pageParam);

        List<NoticeMessage> messages = recordPage.items().stream()
                .map(this::convertToMessage)
                .toList();

        return new PageResult<>(messages, recordPage.total());
    }

    /**
     * 转换NoticeMessage到Notice
     */
    private Notice convertToRecord(Long userId, NoticeMessage message) {
        Notice record = Notice.builder()
                .userId(userId)
                .title(message.getTitle())
                .content(message.getContent())
                .type(message.getType())
                .isRead(Boolean.TRUE.equals(message.getIsRead()))
                .link(message.getLink())
                .senderId(message.getSenderId())
                .senderName(message.getSenderName())
                .createTime(message.getCreateTime())
                .build();

        // 处理额外数据
        if (message.getData() != null && !message.getData().isEmpty()) {
            try {
                record.setData(JSON.toJson(message.getData()));
            } catch (Exception e) {
                log.warn("序列化消息data数据失败: {}", e.getMessage());
            }
        }

        return record;
    }

    /**
     * 转换Notice到NoticeMessage
     */
    private NoticeMessage convertToMessage(Notice record) {
        NoticeMessage.NoticeMessageBuilder builder = NoticeMessage.builder()
                .id(record.getId())
                .title(record.getTitle())
                .content(record.getContent())
                .type(record.getType())
                .isRead(record.getIsRead())
                .link(record.getLink())
                .senderId(record.getSenderId())
                .senderName(record.getSenderName())
                .createTime(record.getCreateTime());

        // 处理额外数据
        if (!Str.isEmpty(record.getData())) {
            try {
                Map<String, Object> data = JSON.fromJson(record.getData(), new TypeReference<>() {
                });
                builder.data(data);
            } catch (Exception e) {
                log.warn("反序列化消息data数据失败: {}", e.getMessage());
            }
        }

        return builder.build();
    }
}
