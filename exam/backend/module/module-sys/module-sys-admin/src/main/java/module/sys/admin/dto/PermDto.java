package module.sys.admin.dto;

import infra.auth.annotation.DataPerm;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public class PermDto {
    // 用户权限信息
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UserPermInfo {
        // 用户所属岗位信息
        private List<String> posts;
        // 岗位权限集合
        private Set<String> postPerms;
        // 用户额外允许权限
        private Set<String> allows;
        // 用户拒绝权限
        private Set<String> denys;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditPostPerm {
        // 数据权限
        private DataPerm data;
        // 功能权限
        private Set<String> perms;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditUserPerm {
        // 用户额外允许权限
        private Set<String> allows;
        // 用户拒绝权限
        private Set<String> denys;
    }
}
