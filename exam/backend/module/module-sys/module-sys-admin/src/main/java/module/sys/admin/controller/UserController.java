package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.CallUtil;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.CommonDto;
import module.sys.admin.dto.UserDto;
import module.sys.config.SysConfigProperties;
import module.sys.entity.User;
import module.sys.entity.UserDept;
import module.sys.service.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

@Tag(name = "账户管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/user")
public class UserController {

    private final UserService userService;
    private final DeptService deptService;
    private final PostService postService;
    private final UserDeptService userDeptService;
    private final AuthUserService authUserService;
    private final SysConfigProperties config;

    @Operation(summary = "账户列表")
    @GetMapping("/list")
    @Perm("sys:user")
    public Result<PageResult<UserDto.InfoDto>> list(
            String type,
            String key,
            Long deptId,
            Long postId,
            Integer status,
            PageParam pageParam) {

        // 根据部门或岗位过滤用户ID
        List<Long> userIds;
        if (deptId != null) {
            List<Long> deptIds = deptService.getAllChildrenIds(deptId, true, FilterOptions.DISABLE_ALL_FILTER);
            if (!deptIds.isEmpty()) {
                userIds = userDeptService.getByDepts(deptIds)
                        .stream()
                        .map(UserDept::getUserId)
                        .toList();
            } else {
                return Result.okData(PageResult.empty());
            }
        } else if (postId != null) {
            userIds = userDeptService.getByPosts(List.of(postId))
                    .stream()
                    .map(UserDept::getUserId)
                    .toList();
        } else {
            userIds = null;
        }

        if (userIds != null && userIds.isEmpty()) {
            return Result.okData(PageResult.empty());
        }

        var result = userService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("userName".equals(type)) {
                    w.and(w1 -> w1.like("user_name", keyTrim)
                            .or().like("py", keyTrim));
                }
                w.like("loginName".equals(type), "login_name", keyTrim);
                w.like("phone".equals(type), "phone", keyTrim);
                w.like("email".equals(type), "email", keyTrim);
                w.like("hisCode".equals(type), "his_code", keyTrim);
            }
            w.in(userIds != null, "id", userIds);
            w.eq(status != null, "status", status);
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result.to(UserDto.InfoDto.class));
    }

    @Operation(summary = "新增账户")
    @PostMapping("/add")
    @AuditLog(code = "sys:user:add", value = "新增账户")
    @Perm("sys:user:add")
    @Transactional
    public Result<Long> add(@RequestBody @Valid UserDto.EditDto dto) {
        User entity = User.builder()
                .loginName(dto.getLoginName())
                .userName(dto.getUserName())
                .hisCode(dto.getHisCode())
                .sex(dto.getSex())
                .phone(dto.getPhone())
                .email(dto.getEmail())
                .idNo(dto.getIdNo())
                .titleDictId(dto.getTitleDictId())
                .titleDictName(dto.getTitleDictName())
                .certificateNo(dto.getCertificateNo())
                .entryDate(dto.getEntryDate())
                .categoryDictId(dto.getCategoryDictId())
                .categoryDictName(dto.getCategoryDictName())
                .image(dto.getImage())
                .intro(dto.getIntro())
                .status(dto.getStatus())
                .build();

        if (!Str.isEmpty(config.getDefaultPassword())) {
            entity.setPassword(authUserService.hashPassword(config.getDefaultPassword()));
        }

        entity.setNeedChangePassword(true);
        if (!userService.add(entity))
            return Result.FAIL_LONG;

        // 处理部门岗位关联
        if (dto.getDeptPosts() != null) {
            for (int index = 0; index < dto.getDeptPosts().size(); index++) {
                String deptPost = dto.getDeptPosts().get(index);
                boolean isMain = index == 0;

                if (deptPost.startsWith("p")) {
                    Long postId = Long.parseLong(deptPost.substring(1));
                    var post = postService.getById(postId);
                    if (post.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(entity.getId());
                        userDept.setDeptId(post.get().getDeptId());
                        userDept.setPostId(post.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        userDeptService.add(userDept);
                    }
                } else if (deptPost.startsWith("d")) {
                    Long deptId = Long.parseLong(deptPost.substring(1));
                    var dept = deptService.getById(deptId);
                    if (dept.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(entity.getId());
                        userDept.setDeptId(dept.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        userDeptService.add(userDept);
                    }
                }
            }
        }

        return Result.okData(entity.getId());
    }

    @Operation(summary = "编辑账户")
    @GetMapping("/edit")
    @Perm("sys:user:edit")
    public Result<UserDto.EditDto> getEdit(Long id) {
        var entity = userService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("账户不存在");
        }

        User user = entity.get();
        List<String> deptPosts = userDeptService.getDepts(user.getId()).stream()
                .sorted((a, b) -> {
                    if (a.getMain() && !b.getMain()) return -1;
                    if (!a.getMain() && b.getMain()) return 1;
                    return Long.compare(a.getId(), b.getId());
                })
                .map(item -> item.getPostId() != null ? "p" + item.getPostId() : "d" + item.getDeptId())
                .toList();

        var result = ObjectUtil.copyTo(user, new UserDto.EditDto());
        result.setDeptPosts(deptPosts);

        return Result.okData(result);
    }

    @Operation(summary = "编辑账户")
    @PostMapping("/edit")
    @AuditLog(code = "sys:user:edit", value = "编辑账户")
    @Perm("sys:user:edit")
    @Transactional
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid UserDto.EditDto dto) {
        var entity = userService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("账户不存在");
        }

        User user = entity.get();
        user.setLoginName(dto.getLoginName());
        user.setUserName(dto.getUserName());
        user.setHisCode(dto.getHisCode());
        user.setSex(dto.getSex());
        user.setPhone(dto.getPhone());
        user.setEmail(dto.getEmail());
        user.setIdNo(dto.getIdNo());
        user.setTitleDictId(dto.getTitleDictId());
        user.setTitleDictName(dto.getTitleDictName());
        user.setCertificateNo(dto.getCertificateNo());
        user.setEntryDate(dto.getEntryDate());
        user.setCategoryDictId(dto.getCategoryDictId());
        user.setCategoryDictName(dto.getCategoryDictName());
        user.setImage(dto.getImage());
        user.setIntro(dto.getIntro());
        user.setStatus(dto.getStatus());

        if (!userService.update(user))
            return Result.FAIL;

        // 获取现有的部门岗位关联
        List<UserDept> currentUserDepts = userDeptService.getDepts(id);
        List<String> deptPosts = dto.getDeptPosts() == null ? Collections.emptyList() : dto.getDeptPosts();

        // 处理新的部门岗位关联
        List<UserDept> newRelations = new ArrayList<>();

        for (int index = 0; index < deptPosts.size(); index++) {
            String deptPost = deptPosts.get(index);
            boolean isMain = index == 0;

            if (deptPost.startsWith("p")) {
                Long postId = Long.parseLong(deptPost.substring(1));

                // 查找是否已存在该岗位关联
                List<UserDept> exists = currentUserDepts.stream()
                        .filter(dept -> Objects.equals(dept.getPostId(), postId))
                        .toList();

                if (!exists.isEmpty()) {
                    // 已存在，更新main状态（如果需要）
                    for (UserDept ex : exists) {
                        if (ex.getMain() != isMain) {
                            ex.setMain(isMain);
                            CallUtil.falseTrowBizEx(userDeptService.update(ex), "更新组织关联失败");
                        }
                        // 从待删除列表中移除
                        currentUserDepts.remove(ex);
                    }
                } else {
                    // 不存在，准备创建新关联
                    var post = postService.getById(postId);
                    if (post.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(id);
                        userDept.setDeptId(post.get().getDeptId());
                        userDept.setPostId(post.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        newRelations.add(userDept);
                    }
                }
            } else if (deptPost.startsWith("d")) {
                Long deptId = Long.parseLong(deptPost.substring(1));

                // 查找是否已存在该部门关联（且无岗位）
                List<UserDept> exists = currentUserDepts.stream()
                        .filter(dept -> Objects.equals(dept.getDeptId(), deptId) && dept.getPostId() == null)
                        .toList();

                if (!exists.isEmpty()) {
                    // 已存在，更新main状态（如果需要）
                    for (UserDept ex : exists) {
                        if (ex.getMain() != isMain) {
                            ex.setMain(isMain);
                            CallUtil.falseTrowBizEx(userDeptService.update(ex), "更新组织关联失败");
                        }
                        // 从待删除列表中移除
                        currentUserDepts.remove(ex);
                    }
                } else {
                    // 不存在，准备创建新关联
                    var dept = deptService.getById(deptId);
                    if (dept.isPresent()) {
                        UserDept userDept = new UserDept();
                        userDept.setUserId(id);
                        userDept.setDeptId(dept.get().getId());
                        userDept.setMain(isMain);
                        userDept.setStartTime(LocalDateTime.now());
                        newRelations.add(userDept);
                    }
                }
            }
        }

        // 批量创建新关联
        if (!userDeptService.addBatch(newRelations))
            throw new BizException("创建关联数据失败");
        // 删除剩余的旧关联
        userDeptService.deleteBatch(currentUserDepts);

        return Result.OK;
    }


    @Operation(summary = "删除账户")
    @PostMapping("/delete")
    @AuditLog(code = "sys:user:delete", value = "删除账户")
    @Perm("sys:user:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            userService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "重置密码")
    @PostMapping("/resetPassword")
    @AuditLog(code = "sys:user:resetPassword", value = "重置账户密码")
    @Perm("sys:user:resetPassword")
    public Result<String> resetPassword(@RequestParam Long id) {
        String newPassword = AuthUserService.randomPassword();
        authUserService.resetPassword(id, newPassword, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(newPassword);
    }

    @Operation(summary = "获取用户数据")
    @GetMapping("/commonUsers")
    @Perm
    public Result<List<CommonDto.UserInfoDto>> getCommonUsers(
            @RequestParam(required = false) Long dept,
            @RequestParam(defaultValue = "false") boolean children) {

        List<User> users = userService.getList(dept, children);
        return Result.okData(users.stream().map(user -> new CommonDto.UserInfoDto(
                user.getId(),
                user.getUserName(),
                user.getPy(),
                user.getPhone(),
                user.getEmail()
        )).toList());
    }
}
