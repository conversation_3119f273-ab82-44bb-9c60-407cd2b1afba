package infra.domain.query;

import infra.core.common.ObjectUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Collection;
import java.util.Collections;

/**
 * 分页结果
 *
 * @param <T>
 */
public record PageResult<T>(
        @Schema(description = "分页数据")
        Collection<T> items,

        @Schema(description = "总记录数")
        Long total
) {
    /**
     * 空的，没有数据的分页结果
     */
    private static final PageResult<?> EMPTY = new PageResult<>(Collections.emptyList(), 0L);

    /**
     * 空的，没有数据的分页结果
     */
    @SuppressWarnings("unchecked")
    public static <T> PageResult<T> empty() {
        return (PageResult<T>) EMPTY;
    }

    /**
     * 转换为另一个类型的分页结果
     */
    public <Target> PageResult<Target> to(Class<Target> targetClass) {
        return new PageResult<>(items == null ? null : items.stream().map(value ->
                ObjectUtil.copyTo(value, ObjectUtil.newInstance(targetClass))
        ).toList(), total);
    }
}
