<script setup lang="ts">
import * as api from '~/api/health/feeProject'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '收费项目',
    perm: 'health:feeProject',
    keepAlive: true,
    menu: true,
    order: 2,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 表单
const query = reactive({
  type: 'name',
  key: '',
})
const initData = {
  feeNo: '',
  countryNo: '',
  projectName: '',
  standardAmount: null,
  feeAmount: null,
  invoiceAmount: null,
  approvalNumber: '',
  unit: '',
  spec: '',
  sort: 0,
  disabled: false,
}

// 验证规则
const rules = {
  feeNo: [{ required: true, message: '请输入编号' }],
  projectName: [{ required: true, message: '请输入项目名称' }],
}

// 删除
async function handleRemove(ids: number[]) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await api.remove(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 操作
async function handleBy(ids: number[], fn: any) {
  await tableRef.value.withLoading(async () => {
    await fn(ids)
    tips.success()
    await tableRef.value.fetchData()
  })
}

// 启用
async function handleEnable(ids: number[]) {
  await handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  await handleBy(ids, api.disable)
}

// 导入
async function handleImport(file: File) {
  await api.importData(file)
}

async function handleImportSuccess() {
  tips.success('导入成功')
  await tableRef.value.fetchData()
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query" :export-api="$hasPerm('health:feeProject:export') ? api.exportData : undefined">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="项目名称" value="name" />
              <el-option label="编号" value="feeNo" />
              <el-option label="国家编码" value="countryNo" />
              <el-option label="批准文号" value="approvalNumber" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:feeProject:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group>
          <el-button v-if="$hasPerm('health:feeProject:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('health:feeProject:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm
            v-if="$hasPerm('health:feeProject:delete')" title="确认要删除选中项吗？" placement="bottom"
            @confirm="handleRemove(tableRef.selected)"
          >
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <template #right-actions>
        <el-button-group>
          <file-upload
            v-if="$hasPerm('health:feeProject:import')" accept=".xlsx,.xls" :max-size="5" :upload="handleImport" @success="handleImportSuccess"
          >
            <template #default="{ loading, triggerUpload }">
              <el-button round :loading="loading" @click="triggerUpload">
                <template #icon>
                  <icon icon="lets-icons:import" />
                </template>
                导入
              </el-button>
            </template>
          </file-upload>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="80" />
      <el-table-column prop="feeNo" label="编号" width="120" />
      <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip />
      <el-table-column prop="countryNo" label="国家编码" width="120" />
      <el-table-column prop="standardAmount" label="标准金额" width="120" align="right">
        <template #default="{ row }">
          <span v-if="row.standardAmount">{{ row.standardAmount.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="feeAmount" label="收费金额" width="120" align="right">
        <template #default="{ row }">
          <span v-if="row.feeAmount">{{ row.feeAmount.toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unit" label="单位" width="100" />
      <el-table-column prop="spec" label="规格" width="120" />
      <el-table-column prop="sort" align="center" label="排序值" width="80" />
      <el-table-column prop="disabled" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('health:feeProject:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:feeProject:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 收费项目编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="收费项目" :init-data="initData" :rules="rules" width="700px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="formRef.formData.projectName" placeholder="请输入项目名称" maxlength="127" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="编号" prop="feeNo">
          <el-input v-model="formRef.formData.feeNo" placeholder="请输入编号" maxlength="31" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="国家编码" prop="countryNo">
          <el-input v-model="formRef.formData.countryNo" placeholder="请输入国家编码" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="标准金额" prop="standardAmount">
          <el-input-number v-model="formRef.formData.standardAmount" :precision="2" :min="0" placeholder="请输入标准金额" class="w-full!" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收费金额" prop="feeAmount">
          <el-input-number v-model="formRef.formData.feeAmount" :precision="2" :min="0" placeholder="请输入收费金额" class="w-full!" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="发票金额" prop="invoiceAmount">
          <el-input-number v-model="formRef.formData.invoiceAmount" :precision="2" :min="0" placeholder="请输入发票金额" class="w-full!" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="批准文号" prop="approvalNumber">
          <el-input v-model="formRef.formData.approvalNumber" placeholder="请输入批准文号" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="单位" prop="unit">
          <el-input v-model="formRef.formData.unit" placeholder="请输入单位" maxlength="15" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格" prop="spec">
          <el-input v-model="formRef.formData.spec" placeholder="请输入规格" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formRef.formData.sort" :min="0" class="w-full" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
