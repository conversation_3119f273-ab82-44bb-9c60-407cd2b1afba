package infra.cache.impl;

import infra.cache.core.ICache;
import infra.core.text.Str;
import infra.core.text.JSON;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.Map;

/**
 * 基于 Redis 的缓存实现
 */
@Slf4j
@AllArgsConstructor
public class RedisCache implements ICache {
    /**
     * Redis客户端
     */
    private final RedissonClient redissonClient;

    /**
     * 获取缓存中的值
     *
     * @param key  缓存键
     * @param type 值类型的Class对象
     * @param <T>  值类型
     * @return 缓存值，不存在时返回null
     */
    @Override
    public <T> T get(String key, Class<T> type) {
        if (Str.isEmpty(key)) {
            return null;
        }

        RBucket<String> bucket = redissonClient.getBucket(key);
        String jsonValue = bucket.get();

        if (jsonValue == null) {
            return null;
        }

        // 如果是String类型，直接返回
        if (type == String.class) {
            return type.cast(jsonValue);
        }

        return JSON.fromJson(jsonValue, type);
    }

    /**
     * 设置缓存
     *
     * @param key           缓存键
     * @param value         缓存值
     * @param expireSeconds 过期时间（秒）
     * @param <T>           值类型
     */
    @Override
    public <T> void set(String key, T value, long expireSeconds) {
        if (Str.isEmpty(key)) {
            return;
        }

        if (value == null) {
            log.warn("尝试设置null值到缓存，key: {}", key);
            return;
        }

        String jsonValue;
        if (value instanceof String) {
            jsonValue = (String) value;
        } else {
            jsonValue = JSON.toJson(value);
        }

        RBucket<String> bucket = redissonClient.getBucket(key);
        if (expireSeconds > 0) {
            bucket.set(jsonValue, Duration.ofSeconds(expireSeconds));
        } else {
            bucket.set(jsonValue);
        }
    }

    /**
     * 批量设置缓存(可用于缓存预热)
     *
     * @param kvMap         键值对映射
     * @param expireSeconds 统一的过期时间（秒）
     * @param <T>           值类型
     * @throws IllegalArgumentException 当过期时间小于等于0时抛出
     */
    @Override
    public <T> void setBatch(Map<String, T> kvMap, long expireSeconds) {
        if (kvMap == null || kvMap.isEmpty()) {
            return;
        }

        RBatch batch = redissonClient.createBatch();

        for (Map.Entry<String, T> entry : kvMap.entrySet()) {
            String key = entry.getKey();
            T value = entry.getValue();

            if (Str.isEmpty(key) || value == null) {
                log.warn("跳过无效的键值对，key: {}, value: {}", key, value);
                continue;
            }

            String jsonValue;
            if (value instanceof String) {
                jsonValue = (String) value;
            } else {
                jsonValue = JSON.toJson(value);
            }

            batch.getBucket(key).setAsync(jsonValue, Duration.ofSeconds(expireSeconds));
        }

        batch.execute();
    }

    /**
     * 删除指定键的缓存
     *
     * @param key 缓存键
     */
    @Override
    public void delete(String key) {
        if (Str.isEmpty(key)) {
            return;
        }

        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }

    /**
     * 批量删除缓存
     *
     * @param keys 缓存键的集合
     */
    @Override
    public void deleteBatch(Iterable<String> keys) {
        if (keys == null) {
            return;
        }

        RBatch batch = redissonClient.createBatch();
        int count = 0;

        for (String key : keys) {
            if (!Str.isEmpty(key)) {
                batch.getBucket(key).deleteAsync();
                count++;
            }
        }

        if (count > 0) {
            batch.execute();
        }
    }

    /**
     * 按前缀删除缓存
     * <p>
     * 删除所有以指定前缀开头的缓存键
     *
     * @param prefix 键前缀
     */
    @Override
    public void deletePattern(String prefix) {
        if (Str.isEmpty(prefix)) {
            return;
        }

        String pattern = prefix + "*";
        redissonClient.getKeys().deleteByPattern(pattern);
    }

    /**
     * 判断缓存是否存在
     *
     * @param key 缓存键
     * @return true表示存在，false表示不存在
     */
    @Override
    public boolean exists(String key) {
        if (Str.isEmpty(key)) {
            return false;
        }

        RBucket<String> bucket = redissonClient.getBucket(key);
        return bucket.isExists();
    }

    /**
     * 清空所有缓存
     * <p>
     * 谨慎使用，这会删除该缓存实例中的所有数据
     */
    @Override
    public void clear() {
        redissonClient.getKeys().flushall();
    }
}
