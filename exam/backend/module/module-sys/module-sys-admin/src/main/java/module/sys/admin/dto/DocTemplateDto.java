package module.sys.admin.dto;

import jakarta.validation.constraints.*;
import lombok.*;

/**
 * 文书模板Dto
 */
public class DocTemplateDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 模板名称
        @NotBlank(message = "模板名称不能为空")
        private String name;
        // 模板编号
        @NotBlank(message = "模板编号不能为空")
        private String code;
        // 模板内容
        @NotBlank(message = "模板内容不能为空")
        private String template;
        // 排序值
        private Integer sort;
        // 系统默认
        private Boolean isSys;
        // 禁用
        private Boolean disabled;
    }    
}