<script setup lang="ts">
import * as api from '~/api/health/examProject'
import { useDict } from '~/composables'
import { resetForm, tips } from '~/utils'
import ProjectDict from './components/ProjectDict.vue'
import ProjectFee from './components/ProjectFee.vue'

definePage({
  meta: {
    title: '体检项目',
    perm: 'health:examProject',
    menu: true,
    order: 3,
  },
})

// 状态及数据
const { KEYS, getName } = useDict()
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef<any>('formRef')
const activeTab = ref('base')

const getDeptName = getName(KEYS.DEPT_LIST)
const getProjectTypeName = getName(KEYS.DICT.PROJECT_TYPE)
const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 表单
const query = reactive({
  type: 'name',
  key: '',
  deptId: undefined,
  projectType: undefined,
  disabled: undefined,
})

const initData = {
  name: '',
  shortName: '',
  printName: '',
  reportPrintName: '',
  costPrice: 0,
  price: 0,
  comboPrice: 0,
  groupPrice: 0,
  isDiscount: false,
  deptId: null,
  sampleTypeId: null,
  barcodePrefix: '',
  projectType: null,
  sex: 0,
  age: 0,
  isSpinsterhood: false,
  isPregnancy: false,
  isDrug: false,
  isDoctorSuggest: false,
  isOneReport: false,
  isReportPrint: true,
  isGuideSheet: true,
  isGuideSheetDrug: false,
  isQuestionnaire: false,
  isApplySheet: true,
  dining: 0,
  diningTime: null,
  urine: 0,
  billingType: null,
  billingMessage: '',
  examAddressDictId: null,
  dailyOrderVolume: null,
  applyTemplate: null,
  examSignificance: '',
  pacsExamType: '',
  pacsType: '',
  isGroup: false,
  isPacsPrint: false,
  isImageText: false,
  isGene: false,
  examType: null,
  remark: '',
  disabled: false,
}

// 验证规则
const rules = {
  name: [{ required: true, message: '请输入项目名称' }],
  price: [{ required: true, message: '请输入单价' }],
  projectType: [{ required: true, message: '请选择项目类型' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success('操作成功')
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}

// 启用
async function handleEnable(ids: number[]) {
  handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  handleBy(ids, api.disable)
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef"
          :model="query"
          :inline="true"
          @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="名称" value="name" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item label="所属科室" prop="deptId">
            <dept-select v-model="query.deptId" />
          </el-form-item> -->
          <el-form-item label="项目类型" prop="projectType" class="w-50">
            <dict-select v-model="query.projectType" :dict-key="KEYS.DICT.PROJECT_TYPE" clearable />
          </el-form-item>
          <el-form-item label="状态" prop="disabled" class="w-36">
            <el-select v-model="query.disabled" clearable>
              <el-option label="启用" :value="false" />
              <el-option label="禁用" :value="true" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('health:examProject:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-button v-if="$hasPerm('health:examProject:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('health:examProject:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="$hasPerm('health:examProject:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="name" label="项目名称" show-overflow-tooltip />
      <el-table-column prop="projectType" label="项目类型" width="200">
        <template #default="{ row }">
          {{ getProjectTypeName(row.projectType) }}
        </template>
      </el-table-column>
      <el-table-column prop="deptId" label="所属科室" width="300">
        <template #default="{ row }">
          {{ getDeptName(row.deptId) }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="单价" width="180" align="right">
        <template #default="{ row }">
          {{ row.price?.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="disabled" label="状态" width="180" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('health:examProject:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('health:examProject:delete')"
              title="确认要删除该项吗？"
              placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef"
    title="体检项目"
    class="p-6 pl-4 pt-0"
    :init-data="initData"
    :rules="rules"
    :dialog-props="{ top: '5vh' }"
    :hide-footer="activeTab !== 'base'"
    width="1200px"
    :get-api="api.getEdit"
    :add-api="api.add"
    :edit-api="api.postEdit"
    @success="tableRef.fetchData()"
  >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基础资料" name="base" class="mt-4">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="formRef.formData.name" placeholder="请输入项目名称" maxlength="127" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目简称" prop="shortName">
              <el-input v-model="formRef.formData.shortName" placeholder="请输入项目简称" maxlength="31" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="打印名称" prop="printName">
              <el-input v-model="formRef.formData.printName" placeholder="请输入打印名称" maxlength="127" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请单模板" prop="applyTemplate">
              <dict-select v-model="formRef.formData.applyTemplate" :dict-key="KEYS.DOC_TEMPLATE" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="体检类型" prop="examType">
              <dict-select v-model="formRef.formData.examType" :dict-key="KEYS.DICT.EXAM_TYPE" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告打印名称" prop="reportPrintName">
              <el-input v-model="formRef.formData.reportPrintName" placeholder="请输入报告打印名称" maxlength="127" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-4">
          <el-col :span="6">
            <el-form-item label="单价" prop="price">
              <el-input-number v-model="formRef.formData.price" :precision="2" :min="0" class="w-full!" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="成本价" prop="costPrice">
              <el-input-number v-model="formRef.formData.costPrice" :precision="2" :min="0" class="w-full!" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="套餐价" prop="comboPrice">
              <el-input-number v-model="formRef.formData.comboPrice" :precision="2" :min="0" class="w-full!" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="团购价" prop="groupPrice">
              <el-input-number v-model="formRef.formData.groupPrice" :precision="2" :min="0" class="w-full!" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="项目类型" prop="projectType">
              <dict-select v-model="formRef.formData.projectType" :dict-key="KEYS.DICT.PROJECT_TYPE" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属科室" prop="deptId">
              <dept-select v-model="formRef.formData.deptId" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="样本类型" prop="sampleTypeId">
              <dict-select v-model="formRef.formData.sampleTypeId" :dict-key="KEYS.SAMPLE_TYPE" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="条码前缀" prop="barcodePrefix">
              <dict-select v-model="formRef.formData.barcodePrefix" :dict-key="KEYS.BAR_CODE" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="开单分类" prop="billingType">
              <dict-select v-model="formRef.formData.billingType" :dict-key="KEYS.DICT.BILLING_TYPE" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="每日开单量" prop="dailyOrderVolume">
              <el-input-number v-model="formRef.formData.dailyOrderVolume" :min="0" class="w-full!" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开单提示" prop="billingMessage">
              <el-input v-model="formRef.formData.billingMessage" placeholder="开立此单时，将提示用户" maxlength="127" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-4">
          <el-col :span="6">
            <el-form-item label="性别限制" prop="sex">
              <dict-select v-model="formRef.formData.sex" :dict-key="KEYS.SEX_LIMIT" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年龄阶段" prop="age">
              <dict-select v-model="formRef.formData.age" :dict-key="KEYS.AGE_LIMIT" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="未婚不宜" prop="isSpinsterhood">
              <el-switch v-model="formRef.formData.isSpinsterhood" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="怀孕禁检" prop="isPregnancy">
              <el-switch v-model="formRef.formData.isPregnancy" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="涨尿标记" prop="urine">
              <dict-select v-model="formRef.formData.urine" :dict-key="KEYS.URINE" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="餐前餐后" prop="dining">
              <dict-select v-model="formRef.formData.dining" :dict-key="KEYS.DINING" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item v-if="formRef.formData.dining !== 0" label="餐前餐后间隔" prop="diningTime">
              <el-input-number v-model="formRef.formData.diningTime" :min="0" class="w-full!">
                <template #suffix>
                  <span>分钟</span>
                </template>
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="检查地址" prop="examAddressDictId">
              <dict-select v-model="formRef.formData.examAddressDictId" :dict-key="KEYS.DICT.ADDRESS" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目说明" prop="remark">
              <el-input v-model="formRef.formData.remark" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查意义" prop="examSignificance">
              <el-input v-model="formRef.formData.examSignificance" type="textarea" :rows="2" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-4">
          <el-col :span="8">
            <el-form-item label="PACS检查类型" prop="pacsExamType">
              <el-input v-model="formRef.formData.pacsExamType" maxlength="31" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="PACS类别" prop="pacsType">
              <el-input v-model="formRef.formData.pacsType" maxlength="15" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="PACS打印图片" prop="isPacsPrint">
              <el-switch v-model="formRef.formData.isPacsPrint" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-4">
          <el-col :span="4">
            <el-form-item label="可打折" prop="isDiscount">
              <el-switch v-model="formRef.formData.isDiscount" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="需要药品" prop="isDrug">
              <el-switch v-model="formRef.formData.isDrug" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="需要问卷" prop="isQuestionnaire">
              <el-switch v-model="formRef.formData.isQuestionnaire" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="需要医生建议" prop="isDoctorSuggest">
              <el-switch v-model="formRef.formData.isDoctorSuggest" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="图文项目" prop="isImageText">
              <el-switch v-model="formRef.formData.isImageText" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="基因项目" prop="isGene">
              <el-switch v-model="formRef.formData.isGene" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="生成申请单" prop="isApplySheet">
              <el-switch v-model="formRef.formData.isApplySheet" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="打印指引单" prop="isGuideSheet">
              <el-switch v-model="formRef.formData.isGuideSheet" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="打印药品指引单" prop="isGuideSheetDrug">
              <el-switch v-model="formRef.formData.isGuideSheetDrug" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="打印报告" prop="isReportPrint">
              <el-switch v-model="formRef.formData.isReportPrint" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="独立报告" prop="isOneReport">
              <el-switch v-model="formRef.formData.isOneReport" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="已禁用" prop="disabled">
              <el-switch v-model="formRef.formData.disabled" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane v-if="$hasPerm('health:examProject:dict')" lazy label="项目字典" name="dict" :disabled="!formRef.isEdit" class="min-h-80">
        <ProjectDict :project-id="formRef.editId" />
      </el-tab-pane>
      <el-tab-pane v-if="$hasPerm('health:examProject:fee')" lazy label="收费项目" name="fee" :disabled="!formRef.isEdit" class="min-h-80">
        <ProjectFee :project-id="formRef.editId" />
      </el-tab-pane>
    </el-tabs>
  </detail-form>
</template>
