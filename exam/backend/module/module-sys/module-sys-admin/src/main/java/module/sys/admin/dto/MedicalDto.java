package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.SexLimit;

/**
 * 病史字典Dto
 */
public class MedicalDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        @NotNull(message = "病史类别不能为空")
        private Long typeId;
        // 病史名称
        @NotBlank(message = "病史名称不能为空")
        private String name;
        // ICD编码
        private String icdCode;
        // 性别 0：不限 1：男 2：女
        private SexLimit sex;
        // 用于成人或儿童 0：不限 1：成人 2：儿童
        private AgeLimit age;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
    }    
}