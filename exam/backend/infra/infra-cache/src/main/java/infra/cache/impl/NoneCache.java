package infra.cache.impl;

import infra.cache.core.ICache;

import java.util.Map;

/**
 * 空缓存，即不缓存，在某将情况下不想缓存，但代码中又没有判断空时使用。
 */
public class NoneCache implements ICache {
    /**
     * 单例实例
     */
    public static final NoneCache INSTANCE = new NoneCache();

    @Override
    public <T> T get(String key, Class<T> type) {
        return null;
    }

    @Override
    public <T> void set(String key, T value, long expireSeconds) {

    }

    @Override
    public <T> void setBatch(Map<String, T> kvMap, long expireSeconds) {

    }

    @Override
    public void delete(String key) {

    }

    @Override
    public void deleteBatch(Iterable<String> keys) {

    }

    @Override
    public void deletePattern(String prefix) {

    }

    @Override
    public boolean exists(String key) {
        return false;
    }

    @Override
    public void clear() {

    }
}
