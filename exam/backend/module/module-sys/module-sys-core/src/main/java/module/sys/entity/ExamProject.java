package module.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.EntityBase;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.Dining;
import module.sys.model.SexLimit;
import module.sys.model.Urine;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 体检项目
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_exam_project")
public class ExamProject extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 项目名称
    private String name;
    
    // 拼音
    private String py;
    
    // 项目名称简称
    private String shortName;
    
    // 项目打印名称
    private String printName;
    
    // 报告打印名称
    private String reportPrintName;
    
    // 成本价
    private BigDecimal costPrice;
    
    // 单价
    private BigDecimal price;
    
    // 套餐价
    private BigDecimal comboPrice;
    
    // 团购价
    private BigDecimal groupPrice;
    
    // 是否可打折
    private Boolean isDiscount;
    
    // 所属科室ID
    private Long deptId;
    
    // 标本类型ID
    private Long sampleTypeId;
    
    // 条码前缀
    private String barcodePrefix;
    
    // 检查项目类型
    private Long projectType;
    
    // 性别限制 0：不限 1：男 2：女
    @Builder.Default
    private SexLimit sex = SexLimit.NORMAL;
    
    // 年龄阶段 0:不限 1:成人 2:儿童
    @Builder.Default
    private AgeLimit age = AgeLimit.NORMAL;
    
    // 是否未婚不宜
    private Boolean isSpinsterhood;
    
    // 是否怀孕禁检
    private Boolean isPregnancy;
    
    // 是否需要药品
    private Boolean isDrug;
    
    // 是否需要医生建议
    private Boolean isDoctorSuggest;
    
    // 是否独立报告
    private Boolean isOneReport;
    
    // 是否打印报告
    @Builder.Default
    private Boolean isReportPrint = true;
    
    // 是否打印指引单
    @Builder.Default
    private Boolean isGuideSheet = true;
    
    // 是否打印药品指引单
    private Boolean isGuideSheetDrug;
    
    // 是否需要问卷
    private Boolean isQuestionnaire;
    
    // 是否生成申请单
    @Builder.Default
    private Boolean isApplySheet = true;
    
    // 餐前餐后标记 0：不限 1：餐前 2：餐后
    @Builder.Default
    private Dining dining = Dining.NORMAL;
    
    // 餐前后时间间隔(分钟)
    private Integer diningTime;
    
    // 涨尿标记 0：不限 1：涨尿 2：排尿
    @Builder.Default
    private Urine urine = Urine.NORMAL;
    
    // 开单分类ID
    private Long billingType;
    
    // 开单提示信息
    private String billingMessage;
    
    // 检查地点字典id
    private Long examAddressDictId;
    
    // 每日开单量
    private Integer dailyOrderVolume;
    
    // 申请单模板
    private Long applyTemplate;
    
    // 项目检查意义
    private String examSignificance;
    
    // PACS检查项目类型
    private String pacsExamType;
    
    // PACS类别
    private String pacsType;
    
    // 是否组合项目
    private Boolean isGroup;
    
    // PACS打印图片
    private Boolean isPacsPrint;
    
    // 是否图文项目
    private Boolean isImageText;
    
    // 是否基因项目
    private Boolean isGene;
    
    // 体检报告类型
    private Long examType;
    
    // 项目说明
    private String remark;
    
    // 是否禁用
    private Boolean disabled;
}