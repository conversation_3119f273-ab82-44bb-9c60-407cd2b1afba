package infra.audit.core;

import lombok.Getter;

/**
 * 审计日志数据上下文
 */
public final class AuditLogContext {
    // 线程本地数据
    private static final ScopedValue<AuditLogContext.WithData> DATA_SCOPED_VALUE = ScopedValue.newInstance();
    // 用于在没有启用审计时，返回一个占位数据，避免程序中使用WithData空引用
    private static final AuditLogContext.WithData PLACE_HOLDER_DATA = new AuditLogContext.WithData();

    private AuditLogContext() {
    }

    public static <R, X extends Throwable> R callWithData(AuditLogContext.WithData data, ScopedValue.CallableOp<? extends R, X> op) throws X {
        return ScopedValue.where(DATA_SCOPED_VALUE, data).call(op);
    }

    /**
     * 设置当前线程的审计详情信息
     */
    public static AuditLogContext.WithData withDetail(String detail) {
        return getScopedDataOrPlaceHolder().withDetail(detail);
    }


    /**
     * 设置当前线程的审计数据对象
     */
    public static AuditLogContext.WithData withData(Object extData) {
        return getScopedDataOrPlaceHolder().withData(extData);
    }

    /**
     * 获取当前线程的审计数据对象
     */
    public static AuditLogContext.WithData getScopedData() {
        return DATA_SCOPED_VALUE.isBound() ? DATA_SCOPED_VALUE.get() : null;
    }

    private static AuditLogContext.WithData getScopedDataOrPlaceHolder() {
        var data = getScopedData();
        return data == null ? PLACE_HOLDER_DATA : data;
    }

    // 数据
    @Getter
    public static class WithData {
        private String detail;
        private Object data;

        public AuditLogContext.WithData withDetail(String detail) {
            this.detail = detail;
            return this;
        }

        public AuditLogContext.WithData withData(Object data) {
            this.data = data;
            return this;
        }
    }
}
