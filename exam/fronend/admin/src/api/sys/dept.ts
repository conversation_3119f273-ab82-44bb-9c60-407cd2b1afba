import { get, post } from '~/utils/request'

const baseUrl = '/sys/dept'

/**
 * 获取组织结构树
 */
export function getTree(): Promise<any> {
  return get<any>(`${baseUrl}/tree`)
}

/**
 * 新增科室
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑科室(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑科室(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除科室
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 获取组织结构树
 */
export function getCommonDeptTree(params?: any): Promise<any> {
  return get<any>(`${baseUrl}/commonDeptTree`, params)
}
