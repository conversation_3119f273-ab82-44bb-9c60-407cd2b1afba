-- 导出  表 exam.health_back_info 结构
CREATE TABLE IF NOT EXISTS `health_back_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，积案信息ID',
  `seed_user_id` bigint DEFAULT NULL COMMENT '发送者ID',
  `seed_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送者名称',
  `seed_time` datetime DEFAULT NULL COMMENT '发送时间',
  `seed_content` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送内容',
  `receiver_user_id` bigint DEFAULT NULL COMMENT '接收者ID',
  `receiver_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '接收者名称',
  `receiver_view_time` datetime DEFAULT NULL COMMENT '接收者查看时间',
  `branch_feedback_content` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分科反馈内容',
  `branch_feedback_time` datetime DEFAULT NULL COMMENT '分科反馈时间',
  `customer_id` bigint DEFAULT NULL COMMENT '体检号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积案信息';



-- 导出  表 exam.health_bill 结构
CREATE TABLE IF NOT EXISTS `health_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '收费单据号',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `customer_type_dict_id` bigint NOT NULL DEFAULT '0' COMMENT '体检人类型字典id 0:个人 1:团体 2:公务员',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `project_dict_id` bigint NOT NULL COMMENT '项目ID',
  `project_dict_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目名称',
  `is_refuse_check` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否弃检 0:正常 1:弃检',
  `price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '单价',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `original_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '原始金额',
  `actual_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际金额',
  `derate_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '减免金额',
  `operator_user_id` bigint NOT NULL COMMENT '操作人',
  `operator_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作人名称',
  `operator_time` datetime NOT NULL COMMENT '操作时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结账时间',
  `settle_no` bigint NOT NULL DEFAULT '0' COMMENT '结账单号',
  `yb_seed_no` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '医保发送单号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检账单';



-- 导出  表 exam.health_branch_exam_time 结构
CREATE TABLE IF NOT EXISTS `health_branch_exam_time` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint NOT NULL COMMENT '部门科室ID',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `operator_user_id` bigint DEFAULT NULL COMMENT '操作员ID',
  `operator_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员名称',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室分检时间';



-- 导出  表 exam.health_business 结构
CREATE TABLE IF NOT EXISTS `health_business` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，业务ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `apply_project_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申请项目名称',
  `original_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '原始金额',
  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '统收金额',
  `servant_amount` decimal(12,2) NOT NULL COMMENT '公务员金额',
  `self_pay_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '自费金额',
  `is_register` smallint NOT NULL DEFAULT '0' COMMENT '是否登记 0:未登记 1:已登记 2:反登记',
  `register_user_id` bigint DEFAULT NULL COMMENT '登记人ID',
  `register_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记人名称',
  `register_time` datetime DEFAULT NULL COMMENT '登记时间',
  `bar_code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `collect_user_id` bigint DEFAULT NULL COMMENT '采集人',
  `collect_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '采集人名称',
  `collect_time` datetime DEFAULT NULL COMMENT '采集时间',
  `collect_dept_id` bigint DEFAULT NULL COMMENT '采集科室ID',
  `is_test_result` smallint NOT NULL DEFAULT '0' COMMENT '检验已录结果 0:未收到 1:收到 2:已发LIS',
  `result_time` datetime DEFAULT NULL COMMENT '检验录入结果时间',
  `is_refuse_check` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否弃检 0:正常 1:弃检',
  `is_exam` bit(1) NOT NULL DEFAULT b'0' COMMENT '检查标志 0:未检 1:已检',
  `is_return` smallint NOT NULL DEFAULT '0' COMMENT '退费拟退标志 0:正常 1:拟退 2:退项',
  `exam_dept_id` bigint NOT NULL COMMENT '检查科室ID',
  `bill_time` datetime DEFAULT NULL COMMENT '开单时间',
  `bill_user_id` bigint DEFAULT NULL COMMENT '开单医生',
  `bill_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开单医生名称',
  `is_pay` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否收费 0:未收 1:已收',
  `is_keep` smallint NOT NULL DEFAULT '0' COMMENT '是否记账 0:未记账 1:已记账 2:已结账',
  `pay_time` datetime DEFAULT NULL COMMENT '收费时间',
  `return_time` datetime DEFAULT NULL COMMENT '退费时间',
  `return_user_id` bigint DEFAULT NULL COMMENT '退费操作员ID',
  `return_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '退费操作员名称',
  `exam_user_id` bigint DEFAULT NULL COMMENT '检查医生ID',
  `exam_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检查医生姓名',
  `exam_time` datetime DEFAULT NULL COMMENT '检查时间',
  `reserve_date` date DEFAULT NULL COMMENT '预约日期',
  `film_printed` bit(1) NOT NULL DEFAULT b'0' COMMENT '胶片是否已打印 0:否 1:是',
  `film_dept_id` bigint DEFAULT NULL COMMENT '胶片科室ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检业务';



-- 导出  表 exam.health_customer_conclusion 结构
CREATE TABLE IF NOT EXISTS `health_customer_conclusion` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检查结论ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint NOT NULL COMMENT '部门科室ID',
  `project_id` bigint DEFAULT NULL COMMENT '检查项目ID',
  `project_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检查项目名称',
  `project_result_id` bigint DEFAULT NULL COMMENT '项目结果ID',
  `conclusion_dict_id` bigint NOT NULL COMMENT '结论字典ID',
  `conclusion_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结论名称',
  `brief_summary` bit(1) NOT NULL DEFAULT b'0' COMMENT '小结是否生成 0:未生成 1:生成',
  `is_invalid` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否无效 0:有效 1:无效',
  `conclusion_type` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结论类型',
  `disease_explanation` text COLLATE utf8mb4_unicode_ci COMMENT '疾病解释',
  `project_result` text COLLATE utf8mb4_unicode_ci COMMENT '检查项目结果',
  `result_explain` text COLLATE utf8mb4_unicode_ci COMMENT '结果说明',
  `dept_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '科室建议',
  `chief_inspector_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '总检建议',
  `health_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '健康建议',
  `diet_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '饮食指导',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查结论';



-- 导出  表 exam.health_customer_conclusion_suggest 结构
CREATE TABLE IF NOT EXISTS `health_customer_conclusion_suggest` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，结论建议ID',
  `conclusion_id` bigint NOT NULL COMMENT '检查结论ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint DEFAULT NULL COMMENT '科室ID',
  `conclusion_dict_id` bigint DEFAULT NULL COMMENT '结论字典ID',
  `conclusion_name` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结论名称',
  `total_exam_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '总检建议',
  `health_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '健康建议',
  `conclusion_type_dict_id` bigint DEFAULT NULL COMMENT '结论类型字典id 1:疾病诊断(主要) 2:阳性发现(次要)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查结论建议';



-- 导出  表 exam.health_daily_processing 结构
CREATE TABLE IF NOT EXISTS `health_daily_processing` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `billing_date` date NOT NULL COMMENT '开单日期',
  `dept_id` bigint NOT NULL DEFAULT '0' COMMENT '申请科室ID',
  `dept_code` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请科室编号',
  `apply_project_id` bigint NOT NULL DEFAULT '0' COMMENT '申请项目ID',
  `apply_project_name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请项目名称',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `billing_number_max` int NOT NULL DEFAULT '0' COMMENT '最大开单数量',
  `billing_number` int NOT NULL DEFAULT '0' COMMENT '已经开单数量',
  `billing_number_residue` int NOT NULL DEFAULT '0' COMMENT '剩余开单数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日开单量';



-- 导出  表 exam.health_dept_consult_doctor 结构
CREATE TABLE IF NOT EXISTS `health_dept_consult_doctor` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，科室会诊医生信息ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint DEFAULT NULL COMMENT '部门科室ID',
  `consult_user_id` bigint NOT NULL COMMENT '会诊医生ID',
  `consult_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会诊医生名称',
  `consult_amount` decimal(12,2) NOT NULL COMMENT '会诊金额',
  `total_amount` decimal(12,2) DEFAULT NULL COMMENT '总金额',
  `is_audit_user` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否审核医生 0:否 1:是',
  `consult_time` datetime DEFAULT NULL COMMENT '会诊时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室会诊医生信息';



-- 导出  表 exam.health_dept_consult_project 结构
CREATE TABLE IF NOT EXISTS `health_dept_consult_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，会诊项目ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint DEFAULT NULL COMMENT '部门科室ID',
  `business_id` bigint NOT NULL COMMENT '收费业务ID',
  `branch_exam_id` bigint DEFAULT NULL COMMENT '分科检查ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室会诊项目信息';



-- 导出  表 exam.health_doctor_suggest_project 结构
CREATE TABLE IF NOT EXISTS `health_doctor_suggest_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，医生建议项目ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `suggest_time` datetime DEFAULT NULL COMMENT '建议时间',
  `suggest_user_id` bigint NOT NULL COMMENT '建议医生ID',
  `suggest_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '建议医生名称',
  `suggest_dict_id` bigint NOT NULL COMMENT '建议类型字典id 0:建议放弃检查 1:检后咨询建议 2:科室建议',
  `dept_id` bigint DEFAULT NULL COMMENT '部门科室ID',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生建议项目';



-- 导出  表 exam.health_exam_branch 结构
CREATE TABLE IF NOT EXISTS `health_exam_branch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，分科检查ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint NOT NULL COMMENT '部门科室ID',
  `dept_preliminary_title` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科室初步小结命名',
  `dept_preliminary` text COLLATE utf8mb4_unicode_ci COMMENT '科室小结',
  `dept_user_id` bigint DEFAULT NULL COMMENT '科室医生ID',
  `dept_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科室医生名称',
  `is_audit` bit(1) NOT NULL DEFAULT b'0' COMMENT '科室是否审核 0:未审 1:已审',
  `audit_time` datetime DEFAULT NULL COMMENT '科室审核时间',
  `dept_info` text COLLATE utf8mb4_unicode_ci COMMENT '科室信息',
  `consultation_user_id` bigint DEFAULT NULL COMMENT '会诊医师ID (主审医生)',
  `consultation_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会诊医师名称',
  `positive_result` text COLLATE utf8mb4_unicode_ci COMMENT '阳性结果',
  `image_url` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图像路径',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室分检';



-- 导出  表 exam.health_exam_project 结构
CREATE TABLE IF NOT EXISTS `health_exam_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检查ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `branch_id` bigint NOT NULL COMMENT '分科ID',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `exam_project_id` bigint NOT NULL COMMENT '检查项目ID',
  `exam_project_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '检查项目名称',
  `exam_part_id` bigint DEFAULT NULL COMMENT '检查部位ID',
  `exam_part_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检查部位名称(冗余)',
  `reference_range` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参考范围',
  `reference_range_top` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参考范围上限',
  `reference_range_below` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参考范围下限',
  `sign_id` bigint DEFAULT NULL COMMENT '体检者检查体征词ID',
  `exam_sign_name` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检查项目体征词',
  `exam_sign_desc` text COLLATE utf8mb4_unicode_ci COMMENT '检查项目体征描述',
  `test_result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检验结果',
  `test_number_result` decimal(15,3) DEFAULT NULL COMMENT '检验数字结果',
  `result_desc` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果说明,(↑↓)',
  `exam_project_result` text COLLATE utf8mb4_unicode_ci COMMENT '检查项目结果',
  `is_critical_value` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否存在危急值 0：否 1：是',
  `test_drug_result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检验结果(药敏)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检者检查项目';



-- 导出  表 exam.health_exam_project_image 结构
CREATE TABLE IF NOT EXISTS `health_exam_project_image` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，项目图像ID',
  `exam_id` bigint NOT NULL COMMENT '检查ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `business_id` bigint DEFAULT NULL COMMENT '业务ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门科室ID',
  `image_url` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片相对路径',
  `image_save_time` datetime DEFAULT NULL COMMENT '保存图片时间',
  `image_title` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片标题',
  `image_desc` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片描述',
  `is_print` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否可打印 0：不可打印 1：可打印',
  `exam_part_id` bigint DEFAULT NULL COMMENT '检查部位ID',
  `branch_exam_id` bigint DEFAULT NULL COMMENT '分科检查ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目图像';



-- 导出  表 exam.health_exam_project_sign 结构
CREATE TABLE IF NOT EXISTS `health_exam_project_sign` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检查项目体征词关联表ID',
  `project_id` bigint NOT NULL COMMENT '体检检查ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `sign_id` bigint DEFAULT NULL COMMENT '项目体征词ID',
  `sign_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目体征词名称(冗余)',
  `nature` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性质',
  `location` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '位置',
  `size` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '大小',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '内容描述',
  `branch_id` bigint DEFAULT NULL COMMENT '分科ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查项目体征';



-- 导出  表 exam.health_exam_project_sign_symptoms 结构
CREATE TABLE IF NOT EXISTS `health_exam_project_sign_symptoms` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `customer_id` bigint NOT NULL COMMENT '体检ID',
  `project_id` bigint DEFAULT NULL COMMENT '检查项目ID',
  `project_sign_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '检查项目体征词名称',
  `py` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `severe_level_dict_id` bigint DEFAULT NULL COMMENT '重症级别字典id',
  `conclusion_dict_id` bigint DEFAULT NULL COMMENT '结论词字典ID',
  `medical_id` bigint DEFAULT NULL COMMENT '病史ID',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查项目体征词';



-- 导出  表 exam.health_exam_sign 结构
CREATE TABLE IF NOT EXISTS `health_exam_sign` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_project_id` bigint NOT NULL COMMENT '检查项目ID',
  `sign_id` bigint NOT NULL COMMENT '体征ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查与体征词关联';



-- 导出  表 exam.health_heart_measurement 结构
CREATE TABLE IF NOT EXISTS `health_heart_measurement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，心脏测量ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint DEFAULT NULL COMMENT '科室ID',
  `measurement_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '测值名称',
  `measurement_value` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '测量值',
  `measurement_number_value` decimal(12,2) DEFAULT NULL COMMENT '测量值(数值)',
  `unit` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `standard_encoding` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标准编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='心脏测量值';



-- 导出  表 exam.health_image_audit 结构
CREATE TABLE IF NOT EXISTS `health_image_audit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，图像报告审核ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `apply_desc` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申请审核医生说明',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核医生ID',
  `audit_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核医生名称',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_desc` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核医生说明',
  `audit_result` smallint DEFAULT NULL COMMENT '审核结果 -1:图片未审核 0:审核通过 1:审核未通过 2:科室已重新审核',
  `dept_id` bigint NOT NULL COMMENT '部门科室ID',
  `branch_exam_id` bigint DEFAULT NULL COMMENT '分科检查ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图像审核';



-- 导出  表 exam.health_invoice 结构
CREATE TABLE IF NOT EXISTS `health_invoice` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，票据ID',
  `customer_type_dict_id` bigint NOT NULL COMMENT '体检人类型字典id 0:个人 1:团体 2:充值(个人) 3:医保结账',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '票据单位名称',
  `company_ticket_no` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '票号',
  `total_amount` decimal(12,2) DEFAULT NULL COMMENT '票据总金额',
  `self_pay_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '自费金额',
  `yb_pay_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '医保支付金额',
  `pay_time` datetime NOT NULL COMMENT '收费时间',
  `is_to_account` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否上账 0:未上账, 1:已上账',
  `print_count` int DEFAULT NULL COMMENT '打印次数',
  `first_print_time` datetime DEFAULT NULL COMMENT '首次打印时间',
  `last_print_time` datetime DEFAULT NULL COMMENT '最后打印时间',
  `return_user_id` bigint DEFAULT NULL COMMENT '退票操作者ID',
  `return_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '退票操作者名称',
  `return_time` datetime DEFAULT NULL COMMENT '退票时间',
  `is_reprint` smallint NOT NULL DEFAULT '0' COMMENT '补打标志 0:正常 1:补打',
  `settle_no` bigint NOT NULL DEFAULT '0' COMMENT '结账单号',
  `invoice_type_dict_id` bigint NOT NULL COMMENT '票据类型字典id 1:发票 2:预交金收款票据 3:预交金退款票据',
  `settle_id` bigint NOT NULL DEFAULT '0' COMMENT '收费结账ID',
  `yb_type` varchar(4) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保中心类别',
  `yb_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保中心名称',
  `ts_amount` decimal(12,2) DEFAULT NULL COMMENT '票据统收金额',
  `else_amount` decimal(12,2) DEFAULT NULL COMMENT '票据其他金额',
  `custom_date` date DEFAULT NULL COMMENT '自定义日期',
  `custom_content` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义内容',
  `fee_name` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '费用名称',
  `bill_status` smallint NOT NULL DEFAULT '0' COMMENT '票据状态 0:正常 1:退票 2:作废',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据';



-- 导出  表 exam.health_invoice_pay_detail 结构
CREATE TABLE IF NOT EXISTS `health_invoice_pay_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，票据支付明细ID',
  `invoice_id` bigint NOT NULL COMMENT '票据ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `payment_dict_id` bigint NOT NULL COMMENT '支付方式字典ID',
  `payment_name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据支付详情';



-- 导出  表 exam.health_invoice_project 结构
CREATE TABLE IF NOT EXISTS `health_invoice_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `invoice_id` bigint NOT NULL COMMENT '票据ID',
  `project_id` bigint DEFAULT NULL COMMENT '票据项目ID',
  `apply_project_id` bigint DEFAULT NULL COMMENT '申请项目ID',
  `project_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '票据项目名称',
  `amount` decimal(12,2) DEFAULT NULL COMMENT '票据金额',
  `team_id` bigint DEFAULT NULL COMMENT '团队ID',
  `task_id` bigint DEFAULT NULL COMMENT '任务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据对应项目';



-- 导出  表 exam.health_project_result 结构
CREATE TABLE IF NOT EXISTS `health_project_result` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，体检项目结果ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `project_id` bigint NOT NULL COMMENT '体检项目ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门科室ID',
  `project_result` text COLLATE utf8mb4_unicode_ci COMMENT '项目检查结果',
  `branch_id` bigint DEFAULT NULL COMMENT '分科检查ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目结果';



-- 导出  表 exam.health_register 结构
CREATE TABLE IF NOT EXISTS `health_register` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，登记报到ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `register_time` datetime DEFAULT NULL COMMENT '报到时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报到登记';



-- 导出  表 exam.health_test_collect 结构
CREATE TABLE IF NOT EXISTS `health_test_collect` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检验采集ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint NOT NULL COMMENT '部门科室',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `project_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目名称',
  `price` decimal(12,2) DEFAULT NULL COMMENT '单价',
  `bar_code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `operation_mark` smallint NOT NULL COMMENT '操作标记 0：采集 1：反采集',
  `operator_user_id` bigint NOT NULL COMMENT '操作员ID',
  `operator_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作员名称',
  `operator_time` datetime NOT NULL COMMENT '操作时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检验采集';



-- 导出  表 exam.health_test_inspect 结构
CREATE TABLE IF NOT EXISTS `health_test_inspect` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，送检单ID',
  `inspect_time` datetime NOT NULL COMMENT '送检时间',
  `inspect_no` varchar(7) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '送检批号',
  `inspect_user_id` bigint NOT NULL COMMENT '送检人ID',
  `inspect_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '送检人名称',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `customer_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '性别 0：未知 1：男 2：女',
  `age` smallint DEFAULT NULL COMMENT '年龄',
  `bar_code_prefix` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码前缀（检验项目）',
  `bar_code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `no_print` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否不打印 0：否 1：是',
  `g_position` smallint DEFAULT NULL COMMENT '管位',
  `sample_no` int DEFAULT NULL COMMENT '样本号',
  `audit_user_id` bigint DEFAULT NULL COMMENT '送检审核人ID',
  `audit_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '送检审核人名称',
  `audit_time` datetime DEFAULT NULL COMMENT '送检审核时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检验送检';



-- 导出  表 exam.health_test_outside_order 结构
CREATE TABLE IF NOT EXISTS `health_test_outside_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，外送单ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `bar_code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条形码',
  `out_user_id` bigint DEFAULT NULL COMMENT '外送人员ID',
  `out_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外送人员名称',
  `out_time` datetime DEFAULT NULL COMMENT '外送时间',
  `cancel_user_id` bigint DEFAULT NULL COMMENT '取消人员ID',
  `cancel_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '取消人员名称',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检验外送单';



-- 导出  表 exam.member_bill_relevance 结构
CREATE TABLE IF NOT EXISTS `member_bill_relevance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，收费单据关联信息ID',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `project_name` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收费项目名称',
  `quantity` smallint DEFAULT NULL COMMENT '数量',
  `original_amount` decimal(12,2) DEFAULT NULL COMMENT '原始金额',
  `actual_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际金额',
  `invoice_project_id` bigint DEFAULT NULL COMMENT '发票项目ID',
  `settle_no` bigint DEFAULT NULL COMMENT '结账单号',
  `settle_date` date DEFAULT NULL COMMENT '结账日期',
  `the_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
  `his_project_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'HIS收费项目名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账单项目关联';



-- 导出  表 exam.member_bill_temp 结构
CREATE TABLE IF NOT EXISTS `member_bill_temp` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，收费临时表',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `business_id` bigint NOT NULL COMMENT '收费业务ID',
  `project_id` bigint NOT NULL COMMENT '收费项目ID',
  `original_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '原始金额',
  `self_pay_amount` decimal(12,2) DEFAULT NULL COMMENT '自付金额(实际金额)',
  `total_amount` decimal(12,2) DEFAULT NULL COMMENT '统收金额',
  `pay_type` smallint NOT NULL COMMENT '收费标志 0退费; 1收费',
  `is_keep` smallint NOT NULL DEFAULT '0' COMMENT '是否记账 0:未记账 1:已记账 2:已结账',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='临时账单';



-- 导出  表 exam.member_customer 结构
CREATE TABLE IF NOT EXISTS `member_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，体检流水ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `patient_id` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '病人门诊ID（HIS生成）',
  `record_id` bigint DEFAULT NULL COMMENT '档案ID',
  `id_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '性别 0：未知 1：男 2：女',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `age` smallint DEFAULT NULL COMMENT '年龄',
  `phone` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码',
  `address` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `marriage_dict_id` bigint DEFAULT NULL COMMENT '婚姻状况字典ID',
  `is_pregnancy` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否怀孕, 0:未怀 1:怀孕',
  `nation_dict_id` bigint DEFAULT NULL COMMENT '民族字典ID',
  `education_dict_id` bigint DEFAULT NULL COMMENT '教育程度ID',
  `email` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `photo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '照片',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `group_id` bigint DEFAULT NULL COMMENT '分组ID',
  `combo_id` bigint DEFAULT NULL COMMENT '套餐ID',
  `quota` decimal(12,2) DEFAULT NULL COMMENT '统收限额',
  `original` decimal(12,2) DEFAULT NULL COMMENT '原始金额（折前统收）',
  `prescribing_user_id` bigint DEFAULT NULL COMMENT '开单医生ID',
  `prescribing_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开单医生名称',
  `customer_type_dict_id` bigint NOT NULL COMMENT '体检者类型字典ID 1:普通人员 2:内部人员 3:贵宾人员 5:重要人物 6:爱健康人员 7:特贵人员',
  `health_type_dict_id` bigint NOT NULL COMMENT '体检类型字典ID 1:健康体检 2:招工体检 3:贵宾体检 4:征兵体检 6:公务员招工 7:驾驶员体检 8:入学体检 9:军检 10:童检 11:公务员 15:职业健康体检 16:健康证 18:特贵体检',
  `is_preferential` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否优待 0：否 1：是',
  `record_date` date DEFAULT NULL COMMENT '登记日期',
  `billing_lock` bit(1) NOT NULL DEFAULT b'0' COMMENT '开单锁定, 0:未锁 1:锁定',
  `exam_address_dict_id` bigint DEFAULT NULL COMMENT '检查地址字典id',
  `date_reservation` date DEFAULT NULL COMMENT '预约日期',
  `record_user_id` bigint DEFAULT NULL COMMENT '登记员ID',
  `record_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记员名称',
  `is_record` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已登记 0：未登记  1：登记',
  `guidance_remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指引说明',
  `is_manual_print` bit(1) NOT NULL DEFAULT b'0' COMMENT '指引单是否打印 0:未打 1:已打',
  `is_pay` smallint NOT NULL DEFAULT '0' COMMENT '是否缴费 0:未缴 1:已缴 2:部分缴',
  `is_start_exam` smallint NOT NULL DEFAULT '0' COMMENT '是否开始体检 0:未开始 1:全部开始 2:部分开始',
  `sort_end` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否分检完成 0:未完 1:完成',
  `sort_end_time` datetime DEFAULT NULL COMMENT '分检完成时间',
  `total_exam_lock` bit(1) NOT NULL DEFAULT b'0' COMMENT '总检锁定 0:未锁 1:已锁',
  `total_exam_unlock_reason` text COLLATE utf8mb4_unicode_ci COMMENT '总检解锁原因',
  `total_exam_unlock_time` datetime DEFAULT NULL COMMENT '总检解锁时间',
  `total_exam_complete` bit(1) NOT NULL DEFAULT b'0' COMMENT '总检是否完成 0:未完成 1:已完成',
  `total_audit_complete` bit(1) NOT NULL DEFAULT b'0' COMMENT '总审是否完成 0:未总审 1:已总审',
  `total_exam_user_id` bigint DEFAULT NULL COMMENT '总检医生ID',
  `total_exam_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '总检医生名称',
  `total_exam_time` datetime DEFAULT NULL COMMENT '总检通过时间',
  `total_audit_user_id` bigint DEFAULT NULL COMMENT '总审医生ID',
  `total_audit_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '总审医生名称',
  `is_back` bit(1) NOT NULL DEFAULT b'0' COMMENT '退回标志 0:未退回 1:退回',
  `total_audit_back_reason_zj` text COLLATE utf8mb4_unicode_ci COMMENT '总审退回原因(总检)',
  `total_audit_back_reason_fk` text COLLATE utf8mb4_unicode_ci COMMENT '总审退回原因(分科)',
  `total_audit_time` datetime DEFAULT NULL COMMENT '总审通过时间',
  `report_printed` bit(1) NOT NULL DEFAULT b'0' COMMENT '报告是否打印 0:未打印 1:打印',
  `report_print_user_id` bigint DEFAULT NULL COMMENT '报告打印者ID',
  `report_print_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报告打印者名称',
  `report_print_time` datetime DEFAULT NULL COMMENT '报告打印时间',
  `report_get_notification` bit(1) NOT NULL DEFAULT b'0' COMMENT '报告领取通知 0:未通知 1:已通知',
  `report_get_notification_user_id` bigint DEFAULT NULL COMMENT '报告领取通知人',
  `report_get_notification_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报告领取通知人名称',
  `report_issuer` bigint DEFAULT NULL COMMENT '报告发出者ID',
  `report_issuer_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报告发出者名称',
  `report_get_notification_time` datetime DEFAULT NULL COMMENT '报告领取通知时间',
  `report_get_notification_dict_id` bigint DEFAULT NULL COMMENT '报告领取通知方式字典ID',
  `report_get_type_dict_id` bigint DEFAULT NULL COMMENT '报告领取方式字典ID',
  `is_report_get` bit(1) NOT NULL DEFAULT b'0' COMMENT '报告是否领取 0:未取 1:已取',
  `report_get_time` datetime DEFAULT NULL COMMENT '报告领取时间',
  `is_close` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否结案 0:未结 1:已结',
  `close_time` datetime DEFAULT NULL COMMENT '结案时间',
  `is_track` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要跟踪 0:不需要 1:需要',
  `is_follow_up` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否随访 0：不随访 1：随访',
  `check_remark` text COLLATE utf8mb4_unicode_ci COMMENT '体检者备注',
  `overview` text COLLATE utf8mb4_unicode_ci COMMENT '综述',
  `conclusion` text COLLATE utf8mb4_unicode_ci COMMENT '结论',
  `suggest` text COLLATE utf8mb4_unicode_ci COMMENT '建议',
  `self_funded_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '自费金额',
  `accounting_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '记账金额',
  `fee_dict_id` bigint NOT NULL COMMENT '费用类别字典id 1:自费 2:公务员 3:普通医保 4:统收 5:免费 6:二保 7:非保健',
  `yb_organization_no` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保机构编号',
  `yb_type_dict_id` bigint DEFAULT NULL COMMENT '医保类别字典',
  `yb_card_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保卡号(社会保障卡号、保健卡号)',
  `yb_bz_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '社会保障号(或保健证号)',
  `yb_record_time` datetime DEFAULT NULL COMMENT '医保登记时间',
  `is_jn` bit(1) NOT NULL DEFAULT b'0' COMMENT '积案是否处理 0:未处理 1:已处理',
  `jn_remark` text COLLATE utf8mb4_unicode_ci COMMENT '积案备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检者';



-- 导出  表 exam.member_customer_extend 结构
CREATE TABLE IF NOT EXISTS `member_customer_extend` (
  `id` bigint NOT NULL COMMENT '体检人ID(同member_customer id)',
  `guide_user_id` bigint DEFAULT NULL COMMENT '导诊人员ID',
  `guide_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '导诊人员名称',
  `guide_time` date DEFAULT NULL COMMENT '导诊日期',
  `date_board` date DEFAULT NULL COMMENT '入职日期',
  `work_status` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作状态名称',
  `yb_department_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保单位名称',
  `servant_quota` decimal(12,2) DEFAULT NULL COMMENT '公务员限额',
  `servant_amount` decimal(12,2) DEFAULT NULL COMMENT '公务员调整金额',
  `health_conclusion` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '体检结论(入职体检，显示:合格、不合格)',
  `contagion_j` text COLLATE utf8mb4_unicode_ci COMMENT '甲类传染病(船员置业限制）',
  `contagion_y` text COLLATE utf8mb4_unicode_ci COMMENT '乙类传染病',
  `psychosis` text COLLATE utf8mb4_unicode_ci COMMENT '精神病发病期',
  `health_status_analysis` text COLLATE utf8mb4_unicode_ci COMMENT '健康状态分析',
  `health_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '健康建议',
  `diet_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '饮食建议',
  `exercise_suggest` text COLLATE utf8mb4_unicode_ci COMMENT '运动建议',
  `health_knowledge` text COLLATE utf8mb4_unicode_ci COMMENT '健康知识',
  `tips` text COLLATE utf8mb4_unicode_ci COMMENT '温馨提示',
  `positive_overview` text COLLATE utf8mb4_unicode_ci COMMENT '阳性综诉',
  `is_agent_result` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否代领 0:否 1:是',
  `invoice_info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发票单位名称(发票打印)',
  `is_seed_yb` smallint DEFAULT NULL COMMENT '是否发送医保 (0未发送，1发送，2取消发送)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检者扩展信息';



-- 导出  表 exam.member_record 结构
CREATE TABLE IF NOT EXISTS `member_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，档案ID',
  `id_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '性别 0：未知 1：男 2：女',
  `phone` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话',
  `birthday` date DEFAULT NULL COMMENT '出生日期',
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '住址',
  `marriage_status` smallint DEFAULT NULL COMMENT '婚姻状态 0: 未婚 1: 已婚',
  `nation_dict_id` bigint DEFAULT NULL COMMENT '民族',
  `education_dict_id` bigint DEFAULT NULL COMMENT '教育程度ID',
  `occupation` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职业',
  `registrant_id` bigint DEFAULT NULL COMMENT '登记人ID',
  `registrant_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记人名称',
  `registrant_time` datetime DEFAULT NULL COMMENT '登记时间',
  `anamnesis` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '既往病史',
  `health_manager_user_id` bigint DEFAULT NULL COMMENT '健康管理师ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案信息';



-- 导出  表 exam.member_record_medical 结构
CREATE TABLE IF NOT EXISTS `member_record_medical` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_id` bigint NOT NULL COMMENT '档案ID',
  `medical_id` bigint NOT NULL COMMENT '病史ID',
  `diagnosis_date` date DEFAULT NULL COMMENT '诊断日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检者档案病史关联';



-- 导出  表 exam.member_report_unlock 结构
CREATE TABLE IF NOT EXISTS `member_report_unlock` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，报告解锁ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `total_user_id` bigint DEFAULT NULL COMMENT '总检医生ID',
  `total_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '总检医生名称',
  `unlock_reason` text COLLATE utf8mb4_unicode_ci COMMENT '解锁原因',
  `unlock_time` datetime DEFAULT NULL COMMENT '解锁时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报告解锁';



-- 导出  表 exam.member_settle_account 结构
CREATE TABLE IF NOT EXISTS `member_settle_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，结账ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID',
  `task_id` bigint NOT NULL DEFAULT '0' COMMENT '任务ID',
  `health_nature_dict_id` bigint NOT NULL COMMENT '体检性质字典ID 0:个人 1:团体 2:医保结账',
  `invoice_id` bigint NOT NULL DEFAULT '0' COMMENT '打印的票据流水号',
  `original_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '原始金额',
  `actual_amount` decimal(12,2) NOT NULL COMMENT '实际金额',
  `is_servant` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否公务员 0：否 1：是',
  `servant_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '公务员金额',
  `invoice_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '发票金额',
  `into_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `derate_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '结账后减免金额',
  `settle_type` smallint NOT NULL DEFAULT '0' COMMENT '结账类型 0:正常 1:结账后减免 2:以人工方式',
  `settle_user_id` bigint NOT NULL COMMENT '结账人ID',
  `settle_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '结账人名称',
  `settle_time` datetime NOT NULL COMMENT '结账时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人',
  `audit_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核人名称',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `yb_audit_start_time` date DEFAULT NULL COMMENT '医保审核开始日期',
  `yb_audit_end_time` date DEFAULT NULL COMMENT '医保审核终止日期',
  `yb_client_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保中心名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结账信息';



-- 导出  表 exam.member_settle_account_detail 结构
CREATE TABLE IF NOT EXISTS `member_settle_account_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，结账明细ID',
  `customer_id` bigint DEFAULT NULL COMMENT '体检号',
  `settle_id` bigint NOT NULL COMMENT '结账ID',
  `invoices_id` bigint NOT NULL COMMENT '收费单据号',
  `business_id` bigint NOT NULL COMMENT '收费业务ID',
  `invoice_project_id` bigint DEFAULT NULL COMMENT '发票项目ID',
  `invoice_project_amount` decimal(12,2) NOT NULL COMMENT '发票项目金额',
  `settle_user_id` bigint DEFAULT NULL COMMENT '结账人ID',
  `settle_time` datetime DEFAULT NULL COMMENT '结账时间',
  `invoice_id` bigint DEFAULT NULL COMMENT '票据ID',
  `invoice_amount` decimal(12,2) DEFAULT NULL COMMENT '票据金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结账信息详情';



-- 导出  表 exam.member_settle_account_detail_project 结构
CREATE TABLE IF NOT EXISTS `member_settle_account_detail_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，结账项目明细ID',
  `settle_detail_id` bigint NOT NULL COMMENT '结账明细ID',
  `settle_no` bigint NOT NULL COMMENT '结账单号',
  `business_id` bigint NOT NULL COMMENT '收费业务ID',
  `apply_project_id` bigint DEFAULT NULL COMMENT '申请项目ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `dept_id` bigint DEFAULT NULL COMMENT '科室ID',
  `dept_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '科室名称',
  `invoice_project_id` bigint DEFAULT NULL COMMENT '发票项目ID',
  `invoice_project_name` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发票项目名称',
  `invoice_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '发票项目金额',
  `quantity` smallint DEFAULT NULL COMMENT '数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='结账信息项目详情';



-- 导出  表 exam.member_team 结构
CREATE TABLE IF NOT EXISTS `member_team` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，团队ID',
  `team_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '团队全称',
  `team_abbreviation` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '团队简称',
  `py` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `team_type_dict_id` bigint DEFAULT NULL COMMENT '团队类型ID 1:一般客户 2:有意向客户 3:无意向客户 4:贵宾客户 5:普通客户 6:垃圾站',
  `team_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '团队地址',
  `contacts_user` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人(经办人)',
  `contacts_phone` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系方式(经办人电话)',
  `register_user_id` bigint NOT NULL COMMENT '登记人',
  `register_user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登记人名称',
  `register_time` datetime NOT NULL COMMENT '登记时间',
  `company_business_dict_id` bigint DEFAULT NULL COMMENT '单位行业类型ID',
  `company_email` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位邮箱',
  `company_phone` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位电话',
  `company_boss_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位领导名称',
  `company_boss_phone` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位领导电话',
  `register_job` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经办人职务',
  `register_phone` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '经办人手机号',
  `account_balance` decimal(12,2) DEFAULT NULL COMMENT '账户金额',
  `organization_code` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织机构代码',
  `postcode` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮编',
  `partnered` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已合作',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队';



-- 导出  表 exam.member_team_group_project 结构
CREATE TABLE IF NOT EXISTS `member_team_group_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，分组项目ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `task_group_id` bigint NOT NULL COMMENT '任务分组ID',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `penalty_rate` decimal(5,1) NOT NULL DEFAULT '0.0' COMMENT '扣率',
  `final_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '折后价格',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队分组项目';



-- 导出  表 exam.member_team_task 结构
CREATE TABLE IF NOT EXISTS `member_team_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，任务ID',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `task_reserve_date` date DEFAULT NULL COMMENT '团体任务预定日期',
  `reserve_stop_date` date DEFAULT NULL COMMENT '预定终止日期',
  `settle_accounts_date` date DEFAULT NULL COMMENT '最后结账日期',
  `predict_person_quantity` int DEFAULT NULL COMMENT '预计体检者数量',
  `List_items_quantity` int DEFAULT NULL COMMENT '备单体检者数量',
  `register_quantity` int DEFAULT NULL COMMENT '已登记体检者数量',
  `all_finish_quantity` int DEFAULT NULL COMMENT '已全部体检结束体检者数量',
  `portion_finish_quantity` int DEFAULT NULL COMMENT '部分完成体检者数量',
  `payment` smallint NOT NULL DEFAULT '0' COMMENT '付款情况 0:未付 1:部分付 2:全付',
  `completed` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已完成',
  `settle_accounts` smallint NOT NULL DEFAULT '0' COMMENT '结算是否完成 0:未结 1:部分结 2:全结',
  `total_collection_amount` decimal(12,2) DEFAULT NULL COMMENT '统收金额',
  `contacts` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
  `contacts_way` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系方式',
  `sales_id` bigint DEFAULT NULL COMMENT '销售员ID',
  `sales_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '销售员名称',
  `remark_client` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注（前台可见）',
  `remark_server` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注（内部可见）',
  `register_user_id` bigint NOT NULL COMMENT '登记人ID',
  `register_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记人名称',
  `register_time` datetime DEFAULT NULL COMMENT '登记时间',
  `audit_user_id` bigint DEFAULT NULL COMMENT '任务审核人',
  `audit_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核人名称',
  `audit_time` datetime DEFAULT NULL COMMENT '任务审核时间',
  `list_items_user_id` bigint DEFAULT NULL COMMENT '备单人',
  `list_items_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备单人名称',
  `list_items_time` datetime DEFAULT NULL COMMENT '备单时间',
  `get_user` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '领取人',
  `get_time` datetime DEFAULT NULL COMMENT '领取时间',
  `get_address` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '取单位置',
  `is_list_items` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否备单 0:非备单 1:已备单',
  `task_year` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务年度',
  `is_self_get` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否报表自取 0:否 1:是',
  `health_category_id` bigint DEFAULT NULL COMMENT '体检类别ID',
  `health_type_id` bigint DEFAULT NULL COMMENT '体检类型',
  `is_rush` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否急单 0:否 1:是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队任务';



-- 导出  表 exam.member_team_task_extend 结构
CREATE TABLE IF NOT EXISTS `member_team_task_extend` (
  `id` bigint NOT NULL COMMENT '同任务id(member_team_task表的id)',
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `list_items_user_id` bigint DEFAULT NULL COMMENT '备单员ID',
  `list_items_time` datetime DEFAULT NULL COMMENT '备单时间',
  `list_items_date` date DEFAULT NULL COMMENT '备单日期',
  `to_into_user_id` bigint DEFAULT NULL COMMENT '导入员ID',
  `to_into_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '导入员名称',
  `to_into_time` datetime DEFAULT NULL COMMENT '导入时间',
  `check_user_id` bigint DEFAULT NULL COMMENT '核对员ID',
  `check_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '核对员名称',
  `check_time` datetime DEFAULT NULL COMMENT '核对时间',
  `print_user_id` bigint DEFAULT NULL COMMENT '打印员ID',
  `print_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '打印员名称',
  `print_time` datetime DEFAULT NULL COMMENT '打印时间',
  `relevance_user_id` bigint DEFAULT NULL COMMENT '关联员ID',
  `relevance_user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联员名称',
  `relevance_time` datetime DEFAULT NULL COMMENT '关联时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队任务扩展';



-- 导出  表 exam.member_team_task_group 结构
CREATE TABLE IF NOT EXISTS `member_team_task_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，任务分组ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `group_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分组名称',
  `py` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `team_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '团队名称',
  `group_sex` smallint NOT NULL DEFAULT '0' COMMENT '选择性别 0：不选 1：男性 2：女性',
  `group_marriage` smallint NOT NULL DEFAULT '0' COMMENT '婚姻状况 0：不选 1：已婚 2：未婚',
  `age_top` smallint NOT NULL DEFAULT '0' COMMENT '年龄上限',
  `age_below` smallint NOT NULL DEFAULT '0' COMMENT '年龄下限',
  `health_type_id` bigint DEFAULT NULL COMMENT '体检类型ID',
  `combo_id` bigint DEFAULT NULL COMMENT '体检套餐ID',
  `health_quantity` smallint DEFAULT NULL COMMENT '体检者数量',
  `collect_quota` decimal(12,2) DEFAULT NULL COMMENT '统收限额',
  `instruction_manual_remark` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指引单说明',
  `group_settlement_amount` decimal(12,2) DEFAULT NULL COMMENT '分组结算金额',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队任务分组';



-- 导出  表 exam.member_user_review 结构
CREATE TABLE IF NOT EXISTS `member_user_review` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，用户复查ID',
  `customer_id` bigint NOT NULL COMMENT '体检号',
  `customer_name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '体检人名称',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '复查说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='复检';



-- 导出  表 exam.member_yb_register 结构
CREATE TABLE IF NOT EXISTS `member_yb_register` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，医保登记ID',
  `yb_client_no` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保分中心编号',
  `yb_client_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保分中心名称',
  `social_security_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '社会保障号(或ID0000)',
  `yb_no` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保卡号',
  `yb_company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医保单位名称',
  `work_status_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工作状态',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `sex` smallint DEFAULT NULL COMMENT '性别 1：男 2：女',
  `yb_balance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '医保余额',
  `register_invoice_no` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登记单据号(医保)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医保登记';



-- 导出  表 exam.sys_apply_project 结构
CREATE TABLE IF NOT EXISTS `sys_apply_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，申请项目ID',
  `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `py` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `short_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目名称简称',
  `print_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目打印名称',
  `report_print_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报告打印名称',
  `cost_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '成本价',
  `price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `combo_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '套餐价',
  `group_price` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '团购价',
  `is_discount` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否可打折 0：否 1：是',
  `dept_id` bigint DEFAULT NULL COMMENT '所属科室ID',
  `sample_type_id` bigint DEFAULT NULL COMMENT '标本类型ID',
  `barcode_prefix` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '条码前缀，如血常规 01、生化 02、免疫 03',
  `exam_type` smallint NOT NULL DEFAULT '0' COMMENT '检查类型 0：检查项目 1：检验项目',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '性别限制 0：不限 1：男 2：女',
  `age` smallint NOT NULL DEFAULT '0' COMMENT '年龄阶段 0:不限 1:成人 2:儿童',
  `is_spinsterhood` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否未婚不宜 0：否 1：是',
  `is_pregnancy` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否怀孕禁检 0：否 1：是',
  `is_drug` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要药品 0：不需要 1：需要',
  `is_doctor_suggest` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要医生建议 0：否 1：是',
  `is_one_report` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否独立报告 0：不独立 1：独立',
  `is_report_print` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否打印报告 0：不打印 1：打印',
  `is_guide_sheet` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否打印指引单 0：不打印 1：打印',
  `is_guide_sheet_drug` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否打印药品指引单 0：不打印 1：打印',
  `is_questionnaire` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要问卷 0：不需要 1：需要',
  `dining` smallint NOT NULL DEFAULT '0' COMMENT '餐前餐后标记 0：不限 1：餐前 2：餐后',
  `dining_time` int DEFAULT NULL COMMENT '餐前后时间间隔(分钟，用于就餐提醒)',
  `urine` smallint NOT NULL DEFAULT '0' COMMENT '涨尿标记 0：不限 1：涨尿 2：排尿',
  `billing_type` bigint DEFAULT NULL COMMENT '开单分类ID',
  `billing_message` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开单提示信息',
  `exam_address_dict_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '检查地点字典Id',
  `daily_order_volume` int DEFAULT NULL COMMENT '每日开单量',
  `is_apply_sheet` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否生成申请表 0：否 1：是',
  `apply_template` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申请书模板',
  `project_exam_significance` text COLLATE utf8mb4_unicode_ci COMMENT '项目检查意义',
  `pacs_exam_type` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PACS检查项目类型(接口码), 体检超声、体检放射、体检CT、体检MR、体检胃肠镜、体检病理',
  `pacs_type` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PACS类别 (可用于PACS生成条码)',
  `is_group` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否组合项目 0：不是 1：是',
  `is_pacs_print` bit(1) NOT NULL DEFAULT b'0' COMMENT 'PACS打印图片 0：否 1：是',
  `is_image_text` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否图文项目 0：否 1：是',
  `is_gene` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否基因项目 0：否 1：是',
  `report_type_id` bigint DEFAULT NULL COMMENT '报告名称类别ID',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '项目说明',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='申请项目';



-- 导出  表 exam.sys_apply_project_exam 结构
CREATE TABLE IF NOT EXISTS `sys_apply_project_exam` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，申请项目与检查项目关联ID',
  `appy_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `exam_project_id` bigint NOT NULL COMMENT '检查项目ID',
  `remark` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='申请项目与检查项目关联';



-- 导出  表 exam.sys_apply_project_fee 结构
CREATE TABLE IF NOT EXISTS `sys_apply_project_fee` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，申请项目与收费项目关联ID',
  `appy_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `fee_project_id` bigint NOT NULL COMMENT '收费项目ID',
  `quantity` int DEFAULT NULL COMMENT '数量',
  `remark` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='申请项目与收费项目关联';



-- 导出  表 exam.sys_audit_log 结构
CREATE TABLE IF NOT EXISTS `sys_audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `module` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  `detail` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `login_name` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(2047) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` text COLLATE utf8mb4_unicode_ci,
  `success` bit(1) NOT NULL DEFAULT b'0',
  `error` varchar(2047) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `use_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=518 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志';



-- 导出  表 exam.sys_bar_code_config 结构
CREATE TABLE IF NOT EXISTS `sys_bar_code_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检验条码类别ID',
  `prefix` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '条形码前缀',
  `name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '条形码名称',
  `py` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `len` int NOT NULL COMMENT '条形码长度(不含前缀)',
  `start_no` int NOT NULL COMMENT '起始编号',
  `current_no` bigint NOT NULL COMMENT '当前编号',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注说明',
  `sample_volume` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '样本体积',
  `sample_color` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '样本颜色',
  `sample_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '样本说明',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `version` bigint NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检验条码设置';



-- 导出  表 exam.sys_bar_code_use 结构
CREATE TABLE IF NOT EXISTS `sys_bar_code_use` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `config_id` bigint NOT NULL COMMENT '条形码配置id',
  `prefix` varchar(7) COLLATE utf8mb4_general_ci NOT NULL COMMENT '前缀',
  `no` bigint NOT NULL COMMENT '编号',
  `customer_id` bigint NOT NULL COMMENT '关联客户id',
  `customer_name` varchar(31) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联客户姓名',
  `create_time` datetime NOT NULL COMMENT '生成时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sys_bar_code_use_unique` (`no`,`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='条形码使用记录';



-- 导出  表 exam.sys_data_dict 结构
CREATE TABLE IF NOT EXISTS `sys_data_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `info` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` int NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用字典';



-- 导出  表 exam.sys_data_dict_value 结构
CREATE TABLE IF NOT EXISTS `sys_data_dict_value` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `dict_id` bigint NOT NULL,
  `code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  `py` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort` int NOT NULL DEFAULT '0',
  `disabled` bit(1) NOT NULL DEFAULT b'0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用字典值';



-- 导出  表 exam.sys_dept 结构
CREATE TABLE IF NOT EXISTS `sys_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` smallint NOT NULL DEFAULT '0',
  `py` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` bigint DEFAULT NULL,
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `his_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对应HIS编码',
  `dept_type_dict_id` bigint DEFAULT NULL COMMENT '科室类型字典ID（一般,骨密,收费,检验,超声,影像,采样(血),采样(体液),总检,中医,中医1,病理,TTM,电生理,C13,妇科）',
  `leader_id` bigint DEFAULT NULL,
  `leader_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` int NOT NULL DEFAULT '0',
  `disabled` bit(1) NOT NULL DEFAULT b'0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=100001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门科室';



-- 导出  表 exam.sys_doctor_work 结构
CREATE TABLE IF NOT EXISTS `sys_doctor_work` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，医生排班ID',
  `work_date` date NOT NULL COMMENT '排班日期',
  `word_day_of` smallint NOT NULL DEFAULT '0' COMMENT '上下午标志 0:全天 1:上午 2:下午',
  `user_id` bigint NOT NULL COMMENT '医师ID',
  `user_name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '医师名称',
  `dept_id` bigint DEFAULT NULL COMMENT '排班科室ID',
  `dept_code` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '排班科室编号',
  `work_content` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '排班内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生排班';



-- 导出  表 exam.sys_exam_combo 结构
CREATE TABLE IF NOT EXISTS `sys_exam_combo` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '套餐代码',
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '套餐名称',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `price` decimal(12,2) DEFAULT NULL COMMENT '套餐价格',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '适用性别 0: 男女皆可 1：适宜男 2：适宜女',
  `apply_type` smallint NOT NULL DEFAULT '0' COMMENT '适用团队还是个人 0：个人团队皆可 1：个人 2：团队',
  `apply_child` bit(1) NOT NULL DEFAULT b'0' COMMENT '适用于儿童 0：否 1：是',
  `health_type_dict_id` bigint DEFAULT NULL COMMENT '体检类型字典ID',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体检套餐';



-- 导出  表 exam.sys_exam_combo_project 结构
CREATE TABLE IF NOT EXISTS `sys_exam_combo_project` (
  `id` bigint NOT NULL COMMENT '套餐ID',
  `apply_project_id` bigint NOT NULL COMMENT '申请项目ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='体检套餐与申请项目关联';



-- 导出  表 exam.sys_exam_part 结构
CREATE TABLE IF NOT EXISTS `sys_exam_part` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，科室部位关联ID',
  `project_id` bigint NOT NULL COMMENT '检查项目ID',
  `part_id` bigint NOT NULL COMMENT '部位ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查项目与部位关联';



-- 导出  表 exam.sys_fee_project 结构
CREATE TABLE IF NOT EXISTS `sys_fee_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，收费项目ID',
  `fee_no` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '编号',
  `country_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家编码',
  `project_name` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `py` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `standard_amount` decimal(12,2) DEFAULT NULL COMMENT '标准金额',
  `fee_amount` decimal(12,2) DEFAULT NULL COMMENT '收费金额',
  `invoice_amount` decimal(12,2) DEFAULT NULL COMMENT '发票金额',
  `approval_number` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批准文号',
  `unit` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `spec` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收费项目';



-- 导出  表 exam.sys_icd_dict 结构
CREATE TABLE IF NOT EXISTS `sys_icd_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，ICD编码ID',
  `icd_no` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ICD编码',
  `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '疾病名称',
  `py` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `is_contagion` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否传染病 0：否 1：是',
  `is_chronic` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否慢性病 0：否 1：是',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '男性病或女性病 0：无 1：男 2：女',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ICD编码';



-- 导出  表 exam.sys_id_builder 结构
CREATE TABLE IF NOT EXISTS `sys_id_builder` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `len` int NOT NULL COMMENT '长度',
  `start_no` int NOT NULL DEFAULT '0' COMMENT '起始编号',
  `current_no` bigint NOT NULL DEFAULT '0' COMMENT '当前编号',
  `about` varchar(31) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '说明',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ID生成器';



-- 导出  表 exam.sys_invoice_project 结构
CREATE TABLE IF NOT EXISTS `sys_invoice_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，发票项目ID',
  `invoice_no` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发票编号',
  `name` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发票项目';



-- 导出  表 exam.sys_job 结构
CREATE TABLE IF NOT EXISTS `sys_job` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_id` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `job_desc` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cron` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cron_desc` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` int NOT NULL DEFAULT '0',
  `status` smallint NOT NULL DEFAULT '0',
  `run_last_time` datetime(3) DEFAULT NULL,
  `next_run_time` datetime DEFAULT NULL,
  `run_count` bigint NOT NULL DEFAULT '0',
  `run_success` bigint NOT NULL DEFAULT '0',
  `run_fail` bigint NOT NULL DEFAULT '0',
  `version` bigint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台日志';



-- 导出  表 exam.sys_job_log 结构
CREATE TABLE IF NOT EXISTS `sys_job_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `job_id` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_time` datetime(3) NOT NULL,
  `end_time` datetime(3) DEFAULT NULL,
  `use_time` bigint NOT NULL,
  `success` bit(1) NOT NULL DEFAULT b'0',
  `msg` varchar(2047) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=241 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台任务日志';



-- 导出  表 exam.sys_login_log 结构
CREATE TABLE IF NOT EXISTS `sys_login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `login_name` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_name` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_time` datetime NOT NULL,
  `login_type` smallint NOT NULL,
  `ip` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `client` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `success` bit(1) NOT NULL DEFAULT b'0',
  `error` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户登录日志';



-- 导出  表 exam.sys_medical 结构
CREATE TABLE IF NOT EXISTS `sys_medical` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，病史字典ID',
  `name` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '病史名称',
  `py` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `icd_code` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ICD编码',
  `type_id` bigint NOT NULL COMMENT '病史类别ID',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '用于性别 0：不限 1：男 2：女',
  `age` smallint NOT NULL DEFAULT '0' COMMENT '用于成人或儿童 0：不限 1：成人 2：儿童',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病史字典';



-- 导出  表 exam.sys_medical_type 结构
CREATE TABLE IF NOT EXISTS `sys_medical_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，病史类别ID',
  `name` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '病史类别名称',
  `py` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `sex` smallint NOT NULL DEFAULT '0' COMMENT '性别 0：不限 1：男 2：女',
  `age` smallint NOT NULL DEFAULT '0' COMMENT '用于成人或儿童 0：不限 1：成人 2：儿童',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病史类别';



-- 导出  表 exam.sys_notice 结构
CREATE TABLE IF NOT EXISTS `sys_notice` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `type` int DEFAULT NULL,
  `is_read` bit(1) NOT NULL DEFAULT b'0',
  `link` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sender_id` bigint DEFAULT NULL,
  `sender_name` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` text COLLATE utf8mb4_unicode_ci,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT NULL,
  `deleted` smallint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统通知';



-- 导出  表 exam.sys_post 结构
CREATE TABLE IF NOT EXISTS `sys_post` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `dept_id` bigint NOT NULL,
  `name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `py` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `perm` smallint NOT NULL DEFAULT '0',
  `sort` int NOT NULL DEFAULT '0',
  `disabled` bit(1) NOT NULL DEFAULT b'0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位';



-- 导出  表 exam.sys_post_perm 结构
CREATE TABLE IF NOT EXISTS `sys_post_perm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `post_id` bigint NOT NULL,
  `perm` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=283 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='岗位权限';



-- 导出  表 exam.sys_project_dict 结构
CREATE TABLE IF NOT EXISTS `sys_project_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目字典名称',
  `py` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `english_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目字典英文名称',
  `print_name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目字典打印名称',
  `code` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目字典第三方接口编码',
  `value_type` varchar(2) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '值类型 C：字符 N：数值',
  `value_unit` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '值单位',
  `project_type_id` bigint NOT NULL COMMENT '项目类型ID',
  `remark` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='检查项目字典';



-- 导出  表 exam.sys_sample_type 结构
CREATE TABLE IF NOT EXISTS `sys_sample_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，检验样本类型ID',
  `name` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '样本类型名称',
  `short_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '样本类型缩写',
  `py` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '样本类型代码',
  `dept_id` bigint DEFAULT NULL COMMENT '样本采集科室id',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样本类型';



-- 导出  表 exam.sys_setting 结构
CREATE TABLE IF NOT EXISTS `sys_setting` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(15) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设置表';



-- 导出  表 exam.sys_sign_dict 结构
CREATE TABLE IF NOT EXISTS `sys_sign_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，体征字典ID',
  `name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '体征名称',
  `py` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音',
  `info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '体征描述',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='体征词字典';



-- 导出  表 exam.sys_sms_config 结构
CREATE TABLE IF NOT EXISTS `sys_sms_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `message_id` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息id',
  `content_template` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容模板',
  `supplier_template_code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对应短信提供商的模板编号',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否禁用',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信配置';



-- 导出  表 exam.sys_upfile 结构
CREATE TABLE IF NOT EXISTS `sys_upfile` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `object_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_path` varchar(2047) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_ext` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_size` bigint NOT NULL,
  `info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deleted` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- 导出  表 exam.sys_user 结构
CREATE TABLE IF NOT EXISTS `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_name` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `login_name` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录账号',
  `his_code` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '员工编号（his）',
  `py` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼音码',
  `password` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `id_no` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `sex` smallint NOT NULL DEFAULT '1' COMMENT '性别 1：男 2：女',
  `title_dict_id` bigint DEFAULT NULL COMMENT '职称字典ID',
  `certificate_no` varchar(31) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书编号',
  `title_dict_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职称字典名称',
  `entry_date` datetime DEFAULT NULL COMMENT '入职日期',
  `category_dict_id` bigint DEFAULT NULL COMMENT '人员类别字典ID',
  `category_dict_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '人员类别字典名称',
  `image` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '医生图像',
  `intro` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '医生简介',
  `admin` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为超级管理员',
  `need_change_password` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否需要变更密码',
  `status` smallint NOT NULL DEFAULT '0' COMMENT '状态 0：正常 1：禁用',
  `last_login` datetime DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户';



-- 导出  表 exam.sys_user_dept 结构
CREATE TABLE IF NOT EXISTS `sys_user_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `dept_id` bigint NOT NULL,
  `post_id` bigint DEFAULT NULL,
  `main` bit(1) NOT NULL DEFAULT b'0',
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- 导出  表 exam.sys_user_perm 结构
CREATE TABLE IF NOT EXISTS `sys_user_perm` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `type` smallint NOT NULL DEFAULT '0',
  `perm` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- 导出  表 exam.sys_user_sign 结构
CREATE TABLE IF NOT EXISTS `sys_user_sign` (
  `id` bigint NOT NULL,
  `sign` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `update_time` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户电子签名';



-- 导出  表 exam.sys_fee_project 结构
CREATE TABLE IF NOT EXISTS `sys_fee_project` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键，收费项目ID',
  `fee_no` varchar(31) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '编号',
  `country_no` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家编码',
  `project_name` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目名称',
  `py` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拼音',
  `standard_amount` decimal(12,2) DEFAULT NULL COMMENT '标准金额',
  `fee_amount` decimal(12,2) DEFAULT NULL COMMENT '收费金额',
  `mz_invoice_project` decimal(5,0) DEFAULT NULL COMMENT '门诊发票项目',
  `approval_number` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批准文号',
  `unit` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `spec` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '规格',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值',
  `disabled` bit(1) NOT NULL DEFAULT b'0' COMMENT '禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `create_by` bigint DEFAULT NULL,
  `create_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_by` bigint DEFAULT NULL,
  `update_by_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收费项目';



-- 导出  表 exam.sys_valid_code 结构
CREATE TABLE IF NOT EXISTS `sys_valid_code` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `code` varchar(6) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '验证码',
  `extra` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展数据',
  `expiry_time` datetime NOT NULL COMMENT '过期时间',
  `used` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否已使用',
  `try_count` int NOT NULL DEFAULT '0' COMMENT '尝试验证次数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证码';


