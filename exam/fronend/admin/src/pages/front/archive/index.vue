<script setup lang="ts">
definePage({
  meta: {
    title: '档案管理',
    menu: true,
    order: 2,
    icon: 'iconoir:archive',
  },
})
</script>

<template>
  <p>1. 个检客户档案完善：补充客户基础信息（身份证号、紧急联系人），关联历次体检记录，支持档案信息编辑与更新；</p>
  <p>2. 团检客户档案管理：批量导入 / 完善团体人员信息，关联团体单位信息，支持按团体名称筛选查询；</p>
  <p>3. 历史检查项目组合查询：按客户姓名、体检时间等维度，查询历史体检项目组合及结果，生成历史项目对比表；</p>
  <p>4. 档案状态跟踪（待完善 / 已完善），确保客户档案完整性与时效性</p>

  检前阶段 “开单登记与导入（开单信息关联客户档案）”；健康管理阶段 “客户档案维护（整合历次体检数据，动态更新）”“历史检查回溯（查询历史记录）”
</template>
