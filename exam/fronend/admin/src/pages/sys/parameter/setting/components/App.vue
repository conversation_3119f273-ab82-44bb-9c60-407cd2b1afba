<script setup lang="ts">
import * as api from '~/api/sys/setting'
import { useFormReset, useLoading } from '~/composables'
import { tips } from '~/utils'
import { loadConfig } from '~/utils/config'

const formRef = useTemplateRef('formRef')
const { loading, withLoading } = useLoading()
const { saveData, resetData } = useFormReset(formRef)

const form = reactive({
  appName: '',
  watermark: false,
})
const rules = reactive({
  appName: [
    { required: true, message: '请输入应用名称' },
  ],
})

// 处理提交
async function handleSubmit() {
  await formRef.value.validate()
  await withLoading(async () => {
    await api.updateApp(form)
    // 重新加载配置
    await loadConfig()
    tips.success()
  })
}

// 处理重置
function handleReset() {
  resetData(form)
}

onMounted(async () => {
  const res = await api.getApp()
  saveData(res.data)
  Object.assign(form, res.data)
})
</script>

<template>
  <el-config-provider>
    <el-form ref="formRef" :model="form" label-width="auto" :rules="rules" class="pl-2 pt-4" @submit.prevent="handleSubmit">
      <el-form-item prop="appName" label="应用名称" class="w-100">
        <el-input v-model="form.appName" />
      </el-form-item>
      <el-form-item prop="watermark" label="启用水印">
        <el-switch v-model="form.watermark" />
      </el-form-item>
      <el-form-item label=" " class="pt-4">
        <el-button type="primary" :loading="loading" native-type="submit" size="default">
          <template #icon>
            <icon icon="mdi:check" />
          </template>
          确定
        </el-button>
        <el-button size="default" @click="handleReset">
          <template #icon>
            <icon icon="radix-icons:reset" />
          </template>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </el-config-provider>
</template>
