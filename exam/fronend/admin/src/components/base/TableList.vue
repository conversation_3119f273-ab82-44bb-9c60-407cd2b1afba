<script setup lang="ts">
import { useLoading } from '~/composables'
import { getConfig, scrollTop } from '~/utils'

defineOptions({
  inheritAttrs: false,
})

// 属性
const props = withDefaults(defineProps<{
  // 用于获取数据的Api
  fetchApi: (params: any) => Promise<any>
  // 导出Api，如果指定，则显示导出按钮
  exportApi?: (data: any) => Promise<any>
  // 查询参数
  queryParams?: object
  // 是否分页，默认为 true
  pager?: boolean
  // 表格行的 key，默认为 'id'
  rowKey?: string
  // 是否显示刷新按钮，默认为 true
  refresh?: boolean
}>(), {
  pager: true,
  refresh: true,
  rowKey: 'id',
})

// 事件
const emits = defineEmits<{
  dataLoaded: [data: any]
}>()

const attrs = useAttrs()

// 基础配置
const config = getConfig()
const { loading, withLoading } = useLoading()
const { loading: reportLoading, withLoading: withReportLoading } = useLoading()

const selected = ref()
const hasSelected = computed(() => selected.value && selected.value.length > 0)

const slots = useSlots()
const hasSearch = computed(() => slots.search)

// 数据状态
const listData = ref()
const pageParams = reactive({
  page: 1,
  size: config.pageSize[0],
})
const tableRef = useTemplateRef<any>('tableRef')

// 获取数据
async function fetchData() {
  if (!props.fetchApi) {
    console.warn('请指定 fetchApi 属性')
    return
  }
  await withLoading(async () => {
    try {
      const params = {
        ...props.queryParams,
        ...(props.pager ? pageParams : undefined),
      }
      const result = await props.fetchApi(params)
      listData.value = result.data
      emits('dataLoaded', result.data)
    }
    catch (error) {
      return Promise.reject(error)
    }
  })
}

// 导出数据
async function exportData() {
  if (!props.exportApi) {
    console.warn('请指定 exportApi 属性')
    return
  }

  await withReportLoading(async () => {
    try {
      await props.exportApi!(props.queryParams)
    }
    catch (error) {
      console.error('导出失败:', error)
    }
  })
}

// 处理刷新
async function handleRefresh() {
  await fetchData()
}

// 处理导出
async function handleExport() {
  await exportData()
}

// 处理搜索
async function search() {
  pageParams.page = 1
  await fetchData()
}

// 选中
function handleSelectionChange(val: any) {
  selected.value = val.map((item: any) => item.id);
  (attrs.onSelectionChange as ((value: any) => void) | undefined)?.(val)
}

// 获取数据并滚动到顶部
async function fetchDataAndScroll() {
  await fetchData()
  await nextTick()
  scrollTop()
}

// 监听分页和大小变化
watch(() => pageParams.page, fetchDataAndScroll)
watch(() => pageParams.size, async () => {
  pageParams.page = 1
  await fetchDataAndScroll()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  // 获取数据
  fetchData,
  // 导出数据
  exportData,
  // 搜索(页码重置为1)
  search,
  // loading状态
  loading,
  // 管理loading
  withLoading,
  // 表格引用
  tableRef,
  // 选中的数据
  selected,
  // 是否有选中数据
  hasSelected,
})
</script>

<template>
  <div>
    <div class="mt-2 flex flex-col">
      <!-- 查询表单 -->
      <div v-if="hasSearch" class="mb-1 items-center">
        <slot name="search" />
      </div>
      <div class="flex flex-col justify-between sm:flex-row space-y-3 sm:space-y-0">
        <!-- 左侧操作区 -->
        <div class="flex items-center space-x-2">
          <slot name="left-actions" />
        </div>
        <!-- 右侧操作区 -->
        <div class="flex items-center justify-end space-x-3">
          <el-pagination
            v-if="props.pager && listData?.total" v-model:current-page="pageParams.page"
            v-model:page-size="pageParams.size" :total="listData.total || 0" :page-sizes="config.pageSize"
            class="h-34px" layout="prev, next"
          />
          <el-button-group class="flex">
            <slot name="right-actions" />
            <el-button v-if="props.exportApi" round :loading="reportLoading" @click="handleExport">
              <template #icon>
                <icon icon="material-symbols:upload-rounded" />
              </template>
              导出
            </el-button>
            <el-button v-if="props.refresh" round :loading="loading" @click="handleRefresh">
              <template #icon>
                <icon icon="mdi:refresh" />
              </template>
              刷新
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>
    <!-- 表格区域 -->
    <div v-loading="loading" class="mt-2">
      <el-table
        ref="tableRef" class="table-list" :data="pager ? listData?.items : listData" :row-key="props.rowKey"
        size="large" v-bind="attrs" @selection-change="handleSelectionChange"
      >
        <slot />
        <template #empty>
          <div class="select-none p-10">
            <icon icon="carbon:rule-data-quality" width="52" height="52" class="opacity-30" />
            <div>暂无数据</div>
          </div>
        </template>
      </el-table>
      <div v-if="props.pager" class="mt-5 flex justify-center sm:justify-end">
        <el-scrollbar>
          <el-pagination
            v-model:current-page="pageParams.page" v-model:page-size="pageParams.size"
            :total="listData?.total || 0" :page-sizes="config.pageSize" background
            layout="total, sizes, prev, pager, next, jumper"
          />
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
