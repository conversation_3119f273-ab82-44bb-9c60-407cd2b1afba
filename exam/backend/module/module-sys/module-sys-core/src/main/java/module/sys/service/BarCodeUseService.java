package module.sys.service;

import infra.core.exception.BizException;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.BarCodeConfig;
import module.sys.entity.BarCodeUse;
import module.sys.mapper.BarCodeUseMapper;
import module.sys.model.IdRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 条形码使用服务
 */
@Slf4j
@Service
public class BarCodeUseService extends ServiceBase<BarCodeUseMapper, BarCodeUse> {
    @Autowired
    private BarCodeConfigService barCodeConfigService;

    /**
     * 使用申请
     */
    @Transactional
    public IdRecord useApply(String prefix, Long customerId, String customerName) {
        var configOpt = barCodeConfigService.getByPrefix(prefix);
        if (configOpt.isEmpty()) {
            throw new BizException("条形码配置不存在或已禁用");
        }

        var config = configOpt.get();
        var apply = useApply(config, customerId, customerName);
        return new IdRecord(apply.getNo(), prefix + String.format("%0" + Math.max(config.getLen(), 0) + "d", apply.getNo()));
    }

    /**
     * 使用申请
     */
    private BarCodeUse useApply(BarCodeConfig config, Long customerId, String customerName) {
        int maxRetries = 5;
        int retryCount = 0;

        while (true) {
            if (retryCount > 0) {
                log.info("并发冲突，第{}次申请条码，条码配置: {}", retryCount, config.getPrefix());
                config = barCodeConfigService.getById(config.getId()).orElseThrow();
            }

            Long nextNo = config.getCurrentNo() + 1;
            config.setCurrentNo(nextNo);
            boolean updated = barCodeConfigService.update(config);

            if (!updated) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.warn("条形码申请失败，条码配置：{}", config.getPrefix());
                    throw new BizException("条形码申请失败，系统繁忙请稍后再试");
                }
                continue;
            }

            // 更新成功，创建使用记录
            BarCodeUse barCodeUse = BarCodeUse.builder()
                    .configId(config.getId())
                    .prefix(config.getPrefix())
                    .no(nextNo)
                    .customerId(customerId)
                    .customerName(customerName)
                    .createTime(LocalDateTime.now())
                    .build();

            add(barCodeUse);
            return barCodeUse;
        }
    }
}
