package infra.integration.config;

import com.zaxxer.hikari.HikariConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * 集成配置属性
 * 配置示例：
 * app:
 *   integration:
 *     api1:
 *       datasource:
 *         # HikariCP配置，使用jdbcUrl而不是url
 *         jdbc-url: ********************************
 *         username: root
 *         password: pwd
 *         driver-class-name: com.mysql.cj.jdbc.Driver
 *         # HikariCP连接池配置
 *         maximum-pool-size: 10
 *         minimum-idle: 2
 *       config:
 *         # 其他API配置
 *         test: aa
 *         timeout: 30000
 *         retries: 3
 *         endpoint: api1.example.com
 */
@Data
@Slf4j
@ConfigurationProperties(prefix = "app")
public class IntegrationConfigProperties {

    /**
     * API配置映射，key为集成名称，value为API配置
     */
    private Map<String, ApiConfig> integration = new HashMap<>();

    /**
     * 单个API的配置
     */
    @Data
    public static class ApiConfig {

        /**
         * 数据源配置，基于HikariConfig（可选）
         */
        @NestedConfigurationProperty
        private HikariConfig datasource;

        /**
         * 其他配置项，支持任意类型的值
         */
        private Map<String, Object> config = new HashMap<>();

        /**
         * 获取配置值
         *
         * @param key 配置键
         * @return 配置值
         */
        public Object getConfigValue(String key) {
            return config.get(key);
        }

        /**
         * 获取配置值并转换为指定类型
         *
         * @param key  配置键
         * @param type 目标类型
         * @param <T>  类型参数
         * @return 转换后的配置值
         */
        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key, Class<T> type) {
            Object value = config.get(key);
            if (value == null) {
                return null;
            }
            if (type.isInstance(value)) {
                return (T) value;
            }
            throw new IllegalArgumentException(
                    String.format("转换配置类型失败 '%s' 类型 %s - %s",
                            key, value.getClass().getSimpleName(), type.getSimpleName())
            );
        }

        /**
         * 检查是否有数据源配置
         *
         * @return true如果配置了数据源
         */
        public boolean hasDatasource() {
            return datasource != null && datasource.getJdbcUrl() != null;
        }

        /**
         * 获取所有配置键
         *
         * @return 配置键集合
         */
        public java.util.Set<String> getConfigKeys() {
            return config.keySet();
        }
    }
}