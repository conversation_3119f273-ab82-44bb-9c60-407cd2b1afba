package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 收费项目
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_fee_project")
public class FeeProject extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 编号
    private String feeNo;

    // 国家编码
    private String countryNo;

    // 项目名称
    private String projectName;

    // 拼音
    private String py;

    // 标准金额
    private BigDecimal standardAmount;

    // 收费金额
    private BigDecimal feeAmount;

    // 发票金额
    private BigDecimal invoiceAmount;

    // 批准文号
    private String approvalNumber;

    // 单位
    private String unit;

    // 规格
    private String spec;

    // 排序值
    private Integer sort;

    // 禁用
    private Boolean disabled;
}