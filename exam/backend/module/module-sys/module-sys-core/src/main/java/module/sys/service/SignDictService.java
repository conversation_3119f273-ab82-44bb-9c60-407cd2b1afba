package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Chinese;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.SignDict;
import module.sys.mapper.SignDictMapper;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 体征词字典服务
 */
@Slf4j
@Service
public class SignDictService extends ServiceBase<SignDictMapper, SignDict> {
    @Override
    protected Function<QueryWrapper<SignDict>, QueryWrapper<SignDict>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<SignDict>, QueryWrapper<SignDict>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(SignDict entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);
        setPinyin(entity);
    }

    /**
     * 设置拼音
     */
    private void setPinyin(SignDict value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
