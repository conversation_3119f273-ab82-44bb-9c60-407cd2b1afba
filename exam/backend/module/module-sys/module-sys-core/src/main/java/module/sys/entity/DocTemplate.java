package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 文书模板
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_doc_template")
public class DocTemplate extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 模板名称
    private String name;

    // 编号
    private String code;

    // 模板内容
    private String template;

    // 是否为系统内置
    private Boolean isSys;

    // 排序值
    private Integer sort;

    // 禁用
    private Boolean disabled;
}