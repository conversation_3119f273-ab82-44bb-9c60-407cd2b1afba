<script setup lang="ts">
definePage({
  meta: {
    title: '标本管理',
    menu: true,
    order: 2,
    icon: 'cuida:sample-container-outline',
  },
})
</script>

<template>
<p>1. 标本标识：生成唯一标本编号（关联客户信息、检查项目），采集时扫码记录采集人、时间，打印标签贴附；</p>
<p>2. 流转监控：标本在采集点与检验科室、外送机构间交接时，需扫码确认，系统实时更新状态（待采集→已采集→待检验→已完成）；</p>
<p>3. 外送管理：外送标本记录外送机构、接收人，外送结果上传后自动回填至客户体检记录，与院内结果整合；</p>
<p>4. 异常定位：出现标本丢失、错配时，可通过系统追溯各环节操作记录，快速定位责任环节</p>

检中阶段 “检验标本采验（全流程管理、实时记录流转信息）”“标本外送回填（外送结果自动回填）”；检查科室 “标本全流程追溯（扫码确认、实时追踪状态）”
</template>
