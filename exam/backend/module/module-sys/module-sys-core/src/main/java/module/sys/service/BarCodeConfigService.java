package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.BarCodeConfig;
import module.sys.mapper.BarCodeConfigMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.function.Function;

/**
 * 检验条码设置服务
 */
@Slf4j
@Service
public class BarCodeConfigService extends ServiceBase<BarCodeConfigMapper, BarCodeConfig> {
    @Override
    protected Function<QueryWrapper<BarCodeConfig>, QueryWrapper<BarCodeConfig>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<BarCodeConfig>, QueryWrapper<BarCodeConfig>> defaultOrderBy() {
        return w -> w.orderByAsc("id");
    }

    @Override
    protected void beforeSave(BarCodeConfig entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        // 为提高取号性能，更新时不做检查，在数据库层面做联合唯一约束，前端也在编辑时不允许修改
        if (!isUpdate && existsByPrefix(entity.getPrefix(), null)) {
            throw new BizException("条形码前缀已存在");
        }

        if (entity.getCurrentNo() == null || entity.getCurrentNo() + 1 < entity.getStartNo()) {
            entity.setCurrentNo(Long.valueOf(entity.getStartNo()) - 1);
        }
        setPinyin(entity);
    }

    /**
     * 根据前缀查询
     */
    public Optional<BarCodeConfig> getByPrefix(String prefix) {
        if (Str.isEmpty(prefix))
            return Optional.empty();

        return getFirst(w -> w.eq("prefix", prefix));
    }

    /**
     * 检查前缀是否已存在
     */
    public boolean existsByPrefix(String prefix, Long excludeId) {
        if (Str.isEmpty(prefix)) {
            return false;
        }

        return exists(w -> {
            w.eq("prefix", prefix);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 设置拼音
     */
    private void setPinyin(BarCodeConfig value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}