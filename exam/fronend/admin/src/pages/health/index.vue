<script setup lang="ts">
import { ref } from 'vue'

definePage({
  meta: {
    title: '健康服务',
    menu: true,
    order: -3,
    icon: 'tabler:health-recognition',
  },
})

// 统计数据
const healthStats = ref({
  archives: {
    total: 8642,
    newToday: 23,
    updated: 156,
    highRisk: 89
  },
  follow: {
    pending: 45,
    completed: 234,
    overdue: 12,
    effectiveness: 87.5
  },
  cases: {
    diabetes: 156,
    hypertension: 203,
    coronary: 78,
    newCases: 8
  },
  mining: {
    reports: 12,
    insights: 34,
    trends: 5,
    accuracy: 94.2
  }
})

// 功能模块数据
const modules = ref([
  { name: '健康档案', desc: '客户档案维护和历史回溯', icon: 'mingcute:user-follow-2-line', path: 'record', color: 'amber' },
  { name: '随访干预', desc: '对接随访系统，跟踪完成率与效果', icon: 'fluent:slide-record-28-regular', path: 'follow', color: 'emerald' },
  { name: '病例管理', desc: '特种病例干预，自动识别标记', icon: 'lucide:briefcase-medical', path: 'case', color: 'red' },
  { name: '数据挖掘', desc: '健康数据深度挖掘，群体趋势分析', icon: 'eos-icons:data-mining', path: 'mining', color: 'blue' }
])

// 健康风险趋势数据（最近7天）
const riskTrend = ref([
  { date: '08-14', high: 12, medium: 28, low: 156, total: 196 },
  { date: '08-15', high: 15, medium: 31, low: 162, total: 208 },
  { date: '08-16', high: 11, medium: 26, low: 158, total: 195 },
  { date: '08-17', high: 18, medium: 33, low: 164, total: 215 },
  { date: '08-18', high: 14, medium: 29, low: 160, total: 203 },
  { date: '08-19', high: 16, medium: 35, low: 168, total: 219 },
  { date: '08-20', high: 13, medium: 31, low: 165, total: 209 },
])

// 随访任务提醒
const followTasks = ref([
  { id: 1, patient: '张三', type: '糖尿病随访', dueDate: '2024-08-23', priority: 'high', status: 'pending' },
  { id: 2, patient: '李四', type: '高血压随访', dueDate: '2024-08-23', priority: 'medium', status: 'pending' },
  { id: 3, patient: '王五', type: '冠心病随访', dueDate: '2024-08-24', priority: 'high', status: 'pending' },
  { id: 4, patient: '赵六', type: '健康评估', dueDate: '2024-08-24', priority: 'low', status: 'pending' },
  { id: 5, patient: '钱七', type: '复查提醒', dueDate: '2024-08-25', priority: 'medium', status: 'pending' }
])

// 疾病分布数据
const diseaseDistribution = ref([
  { name: '高血压', count: 203, percentage: 35.2, trend: 'up' },
  { name: '糖尿病', count: 156, percentage: 27.1, trend: 'stable' },
  { name: '冠心病', count: 78, percentage: 13.5, trend: 'down' },
  { name: '高血脂', count: 89, percentage: 15.4, trend: 'up' },
  { name: '其他', count: 51, percentage: 8.8, trend: 'stable' }
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-amber-50 to-orange-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 健康档案总数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-amber-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-amber-600 dark:text-amber-400">
              {{ healthStats.archives.total }}
            </div>
            <div class="text-sm text-amber-700 dark:text-amber-300 mt-1 font-medium">
              健康档案总数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="mingcute:user-follow-2-line" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 待随访人数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ healthStats.follow.pending }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              待随访人数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="fluent:slide-record-28-regular" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 特种病例数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-red-600 dark:text-red-400">
              {{ healthStats.cases.diabetes + healthStats.cases.hypertension + healthStats.cases.coronary }}
            </div>
            <div class="text-sm text-red-700 dark:text-red-300 mt-1 font-medium">
              特种病例数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-red-400 to-red-600 dark:from-red-500 dark:to-red-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="lucide:briefcase-medical" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 数据分析准确率 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {{ healthStats.mining.accuracy }}%
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              数据分析准确率
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="eos-icons:data-mining" class="text-white text-xl" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：核心功能模块和健康趋势 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 健康风险趋势 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-800 dark:text-gray-200">健康风险趋势</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">最近7天</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="trend in riskTrend" :key="trend.date" class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div class="flex items-center space-x-4">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">{{ trend.date }}</span>
                <div class="flex items-center space-x-2">
                  <div class="flex items-center space-x-1">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">高风险 {{ trend.high }}</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">中风险 {{ trend.medium }}</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-xs text-gray-600 dark:text-gray-400">低风险 {{ trend.low }}</span>
                  </div>
                </div>
              </div>
              <span class="text-sm font-semibold text-gray-800 dark:text-gray-200">总计 {{ trend.total }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：随访任务和疾病分布 -->
      <div class="space-y-6">
        <!-- 随访任务提醒 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:alarm-clock" class="text-primary" />
              <span class="font-medium text-gray-800 dark:text-gray-200">随访任务提醒</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="task in followTasks.slice(0, 5)" :key="task.id" class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ task.patient }}</span>
                  <el-tag
                    :type="task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'info'"
                    size="small"
                  >
                    {{ task.priority === 'high' ? '紧急' : task.priority === 'medium' ? '普通' : '一般' }}
                  </el-tag>
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ task.type }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">{{ task.dueDate }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 疾病分布统计 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:data-analysis" class="text-success" />
              <span class="font-medium text-gray-800 dark:text-gray-200">疾病分布统计</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="disease in diseaseDistribution" :key="disease.name" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ disease.name }}</span>
                <div class="flex items-center space-x-1">
                  <icon
                    :icon="disease.trend === 'up' ? 'ep:arrow-up' : disease.trend === 'down' ? 'ep:arrow-down' : 'ep:minus'"
                    :class="[
                      disease.trend === 'up' ? 'text-red-500' :
                      disease.trend === 'down' ? 'text-green-500' :
                      'text-gray-400'
                    ]"
                    class="text-xs"
                  />
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ disease.count }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-500">{{ disease.percentage }}%</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 健康管理概览 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:trend-charts" class="text-warning" />
              <span class="font-medium text-gray-800 dark:text-gray-200">健康管理概览</span>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">随访完成率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="healthStats.follow.effectiveness"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ healthStats.follow.effectiveness }}%</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">档案更新率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((healthStats.archives.updated / healthStats.archives.total) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round((healthStats.archives.updated / healthStats.archives.total) * 100) }}%
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">高风险占比</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((healthStats.archives.highRisk / healthStats.archives.total) * 100)"
                  :show-text="false"
                  class="w-20"
                  color="#f56565"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round((healthStats.archives.highRisk / healthStats.archives.total) * 100) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
