package infra.auth.config;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 授权配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.auth")
public class AuthConfigProperties {
    // 密钥
    @NotBlank
    private String secretKey;
    // 算法(默认HS256)
    private String algorithm = "HS256";
    // 访问key过期时间(默认7200L秒/2小时)
    private int accessTokenExpireSeconds = 7200;
    // 刷新key过期时间(默认2592000L秒/30天)
    private int refreshTokenExpireSeconds = 2592000;
    // token从请求头中获取的key(默认Authorization)
    private String tokenFromKey = "Authorization";
    // 部门从请求头中获取的key(默认X-Dept)
    private String deptFromKey = "X-Dept";
    // redis中token的key前缀(如果配置此值，则token会存储到redis中)
    private String redisTokenPrefix;
    // 用户数据缓存前缀(默认为auth:)
    private String userCachePrefix = "auth:";
    // 用户数据缓存过期时间(默认10分钟)
    private int userCacheExpireSeconds = 60 * 10;
}
