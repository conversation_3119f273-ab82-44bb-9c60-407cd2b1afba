<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>infra</groupId>
        <artifactId>infra</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>infra-audit</artifactId>
    <description>审计模块</description>

    <dependencies>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <!-- 与web相关的类引入 -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 基础依赖 -->
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-auth</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

</project>