package infra.domain.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * 领域相关配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.domain")
public class DomainConfigProperties {
    // 字段加密配置
    private FieldEncryptConfig fieldEncrypt = new FieldEncryptConfig();

    /**
     * 加密配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldEncryptConfig {
        private boolean enabled = false;
        private String provider;
        private AesConfig aes = new AesConfig();
        private Sm4Config sm4 = new Sm4Config();
    }

    /**
     * Aes加密配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AesConfig {
        private String secretKey;
    }

    /**
     * Sm4加密配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Sm4Config {
        private String secretKey;
    }
}
