<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>module</groupId>
        <artifactId>module-health</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>module-health-core</artifactId>
    <description>体检模块-core</description>

    <dependencies>
        <!-- 基础依赖 -->
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-domain</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-cache</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-audit</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

</project>