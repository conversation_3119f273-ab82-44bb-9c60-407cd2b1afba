package infra.core.exception;

/**
 * 序列化异常
 */
public class SerializationException extends RuntimeException {
    /**
     * 使用异常消息创建配置异常
     *
     * @param message 异常消息
     */
    public SerializationException(String message) {
        super(message);
    }

    /**
     * 使用异常消息和原因创建配置异常
     *
     * @param message 异常消息
     * @param cause   原始异常
     */
    public SerializationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 使用原因创建配置异常
     *
     * @param cause 原始异常
     */
    public SerializationException(Throwable cause) {
        super(cause);
    }
}
