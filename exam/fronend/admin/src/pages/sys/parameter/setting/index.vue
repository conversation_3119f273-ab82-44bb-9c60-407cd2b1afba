<script setup lang="ts">
import { useAuth } from '~/composables'
import App from './components/App.vue'
import HospitalInfo from './components/HospitalInfo.vue'
import Safe from './components/Safe.vue'

definePage({
  meta: {
    title: '系统参数',
    perm: 'sys:setting',
    menu: true,
    order: 1,
  },
})

const { hasPerm } = useAuth()
const activeTab = ref<string | undefined>(undefined)

onMounted(() => {
  const tabs = []
  if (hasPerm('sys:setting:app')) {
    tabs.push('app')
  }
  if (hasPerm('sys:setting:hospitalInfo')) {
    tabs.push('hospitalInfo')
  }
  if (hasPerm('sys:setting:safe')) {
    tabs.push('safe')
  }
  activeTab.value = tabs.length ? tabs[0] : undefined
})
</script>

<template>
  <div class="p-4">
    <el-tabs v-model="activeTab">
      <el-tab-pane v-if="$hasPerm('sys:setting:app')" name="app" label="基础设置">
        <App />
      </el-tab-pane>
      <el-tab-pane v-if="$hasPerm('sys:setting:hospitalInfo')" name="hospitalInfo" label="医院信息" lazy>
        <HospitalInfo />
      </el-tab-pane>
      <el-tab-pane v-if="$hasPerm('sys:setting:safe')" name="safe" label="安全设置" lazy>
        <Safe />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
