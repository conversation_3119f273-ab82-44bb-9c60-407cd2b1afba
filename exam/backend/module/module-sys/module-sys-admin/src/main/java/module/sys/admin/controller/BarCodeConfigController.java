package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.BarCodeConfigDto;
import module.sys.entity.BarCodeConfig;
import module.sys.service.BarCodeConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "条形码配置")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/barCodeConfig")
public class BarCodeConfigController {
    private final BarCodeConfigService barCodeConfigService;

    @Operation(summary = "条码配置列表")
    @GetMapping("/list")
    @Perm("sys:barCodeConfig")
    public Result<PageResult<BarCodeConfig>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = barCodeConfigService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("name".equals(type)) {
                    w.and(w1 -> w1.like("name", keyTrim)
                            .or().like("py", keyTrim));
                }
                w.like("prefix".equals(type), "prefix", keyTrim);
            }
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增条码配置")
    @PostMapping("/add")
    @AuditLog(code = "sys:barCodeConfig:add", value = "条码配置-新增")
    @Perm("sys:barCodeConfig:add")
    public Result<Long> add(@RequestBody @Valid BarCodeConfigDto.EditDto dto) {
        BarCodeConfig entity = BarCodeConfig.builder()
                .prefix(dto.getPrefix())
                .name(dto.getName())
                .len(dto.getLen())
                .startNo(dto.getStartNo())
                .remark(dto.getRemark())
                .sampleVolume(dto.getSampleVolume())
                .sampleColor(dto.getSampleColor())
                .sampleRemark(dto.getSampleRemark())
                .disabled(dto.getDisabled())
                .build();

        return barCodeConfigService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑条码配置")
    @GetMapping("/edit")
    @Perm("sys:barCodeConfig:edit")
    public Result<BarCodeConfigDto.EditDto> getEdit(Long id) {
        var entityOpt = barCodeConfigService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new BarCodeConfigDto.EditDto())
        );
    }

    @Operation(summary = "编辑条码配置")
    @PostMapping("/edit")
    @AuditLog(code = "sys:barCodeConfig:edit", value = "条码配置-编辑")
    @Perm("sys:barCodeConfig:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid BarCodeConfigDto.EditDto dto) {
        var entityOpt = barCodeConfigService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("条码配置不存在");
        }

        BarCodeConfig entity = entityOpt.get();
        entity.setPrefix(dto.getPrefix());
        entity.setName(dto.getName());
        entity.setLen(dto.getLen());
        entity.setStartNo(dto.getStartNo());
        entity.setRemark(dto.getRemark());
        entity.setSampleVolume(dto.getSampleVolume());
        entity.setSampleColor(dto.getSampleColor());
        entity.setSampleRemark(dto.getSampleRemark());
        entity.setDisabled(dto.getDisabled());

        return barCodeConfigService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除条码配置")
    @PostMapping("/delete")
    @AuditLog(code = "sys:barCodeConfig:delete", value = "条码配置-删除")
    @Perm("sys:barCodeConfig:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            barCodeConfigService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "sys:barCodeConfig:enable", value = "条码配置-启用")
    @Perm("sys:barCodeConfig:enable")
    public Result<Void> enableBarCode(@RequestBody List<Long> ids) {
        barCodeConfigService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "sys:barCodeConfig:disable", value = "条码配置-禁用")
    @Perm("sys:barCodeConfig:disable")
    public Result<Void> disableBarCode(@RequestBody List<Long> ids) {
        barCodeConfigService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "获取配置条码字典")
    @GetMapping("/dict")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<String>>> getDict() {
        return Result.okData(barCodeConfigService.getList().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getName(), w.getName()))
                .toList());
    }
}