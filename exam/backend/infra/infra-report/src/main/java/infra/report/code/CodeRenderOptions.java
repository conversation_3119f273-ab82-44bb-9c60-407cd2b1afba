package infra.report.code;

import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.Serial;
import java.io.Serializable;

/**
 * 二维码/条码渲染选项
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class CodeRenderOptions implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 宽度(默认300)
     */
    @Builder.Default
    private int width = 300;
    /**
     * 高度(默认300)
     */
    @Builder.Default
    private int height = 300;
    /**
     * 边距(默认1)
     */
    @Builder.Default
    private int margin = 1;
    /**
     * 前景色(默认默色)
     */
    @Builder.Default
    private Color foregroundColor = Color.BLACK;
    /**
     * 背景色(默认白色)
     */
    @Builder.Default
    private Color backgroundColor = Color.WHITE;
    /**
     * 容错等级(默认高)
     */
    @Builder.Default
    private ErrorCorrectionLevel errorCorrectionLevel = ErrorCorrectionLevel.H;
    /**
     * 输出格式(默认PNG)
     */
    @Builder.Default
    private String format = "PNG";

    /**
     * Logo配置(可选)
     */
    private LogoOptions logo;

    /**
     * 获取默认二维码配置
     */
    public static CodeRenderOptions defaultQRCode() {
        return CodeRenderOptions.builder()
                .width(300)
                .height(300)
                .margin(1)
                .foregroundColor(Color.BLACK)
                .backgroundColor(Color.WHITE)
                .errorCorrectionLevel(ErrorCorrectionLevel.H)
                .format("PNG")
                .build();
    }

    /**
     * 获取默认条码配置
     */
    public static CodeRenderOptions defaultBarcode() {
        return CodeRenderOptions.builder()
                .width(200)
                .height(80)
                .margin(1)
                .foregroundColor(Color.BLACK)
                .backgroundColor(Color.WHITE)
                .format("PNG")
                .build();
    }

    /**
     * 自动配置Logo（使用默认设置）
     *
     * @param logoImage Logo图片
     */
    public void autoConfigLogo(BufferedImage logoImage) {
        autoConfigLogo(logoImage, null, null, null);
    }

    /**
     * 自动配置Logo（允许部分自定义）
     *
     * @param logoImage      Logo图片
     * @param customLogoSize 自定义Logo大小，null则自动计算
     * @param roundCorner    是否圆角，null则使用默认值true
     * @param opacity        透明度，null则使用默认值1.0f
     */
    public void autoConfigLogo(BufferedImage logoImage, Integer customLogoSize, Boolean roundCorner, Float opacity) {
        if (logoImage == null) {
            throw new IllegalArgumentException("Logo图像不能为空");
        }

        int qrSize = Math.min(getWidth(), getHeight());
        int logoSize = customLogoSize != null ? customLogoSize : qrSize / 5;

        var logoConfig = LogoOptions.builder()
                .logoImage(logoImage)
                .logoSize(logoSize)
                .borderWidth(qrSize > 300 ? 3 : 2)
                .roundCorner(roundCorner != null ? roundCorner : true)
                .opacity(opacity != null ? opacity : 1.0f)
                .build();

        setLogo(logoConfig);
    }

    /**
     * 验证参数
     */
    public void validate() {
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("宽度和高度必须大于0");
        }
        if (width > 2000 || height > 2000) {
            throw new IllegalArgumentException("图片尺寸不能超过2000x2000像素");
        }
        if (margin < 0) {
            throw new IllegalArgumentException("边距不能为负数");
        }
        if (foregroundColor == null || backgroundColor == null) {
            throw new IllegalArgumentException("前景色和背景色不能为空");
        }
        if (format == null || format.trim().isEmpty()) {
            throw new IllegalArgumentException("输出格式不能为空");
        }
        if (!format.toUpperCase().matches("^(PNG|JPG|JPEG|GIF|BMP)$")) {
            throw new IllegalArgumentException("不支持的图片格式");
        }

        if (getLogo() != null)
            getLogo().validate();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class LogoOptions implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * Logo图形
         */
        private BufferedImage logoImage;

        /**
         * Logo大小(像素)
         */
        @Builder.Default
        private int logoSize = 60;

        /**
         * Logo边框宽度
         */
        @Builder.Default
        private int borderWidth = 2;

        /**
         * 是否圆角
         */
        @Builder.Default
        private boolean roundCorner = true;

        /**
         * 透明度 0.0-1.0
         */
        @Builder.Default
        private float opacity = 1.0f;

        /**
         * 验证参数
         */
        public void validate() {
            if (logoImage == null) {
                throw new IllegalArgumentException("Logo图片不能为空");
            }
            if (logoSize <= 0) {
                throw new IllegalArgumentException("Logo大小必须大于0");
            }
            if (borderWidth < 0) {
                throw new IllegalArgumentException("边框不能为负数");
            }
            if (opacity > 1 || opacity < 0) {
                throw new IllegalArgumentException("透明度在0.0-1.0之间");
            }
        }
    }
}
