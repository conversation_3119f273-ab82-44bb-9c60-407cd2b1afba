<configuration scan="true" scanPeriod="60 seconds">
    <springProperty name="LOG_PATH" source="logging.path" defaultValue="./logs" />
    <springProperty name="SERVER_PORT" source="server.port" scope="context" defaultValue="8080" />
    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 输出到控制台 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="CONSOLE_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="CONSOLE"/>
    </appender>

    <springProfile name="!dev">
        <!-- 输出到文件 -->
        <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/log_${SERVER_PORT}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/log_${SERVER_PORT}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>50MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>${PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <appender name="FILE_ALL_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
            <includeCallerData>true</includeCallerData>
            <appender-ref ref="FILE_ALL"/>
        </appender>

        <appender name="FILE_WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/warn_${SERVER_PORT}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/warn_${SERVER_PORT}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>50MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>${PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>WARN</level>
            </filter>
        </appender>
        <appender name="FILE_WARN_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
            <includeCallerData>true</includeCallerData>
            <appender-ref ref="FILE_WARN"/>
        </appender>

        <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_PATH}/error_${SERVER_PORT}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>${LOG_PATH}/error_${SERVER_PORT}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>50MB</maxFileSize>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>${PATTERN}</pattern>
                <charset>UTF-8</charset>
            </encoder>
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>ERROR</level>
            </filter>
        </appender>
        <appender name="FILE_ERROR_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
            <includeCallerData>true</includeCallerData>
            <appender-ref ref="FILE_ERROR"/>
        </appender>
    </springProfile>

    <!-- 根据环境配置 -->
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE_ASYNC"/>
        </root>
        <logger name="module" level="DEBUG"/>
        <logger name="infra" level="DEBUG"/>
    </springProfile>

    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="CONSOLE_ASYNC"/>
            <appender-ref ref="FILE_ALL_ASYNC"/>
            <appender-ref ref="FILE_WARN_ASYNC"/>
            <appender-ref ref="FILE_ERROR_ASYNC"/>
        </root>
        <logger name="module" level="DEBUG"/>
        <logger name="infra" level="DEBUG"/>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE_ASYNC"/>
            <appender-ref ref="FILE_ALL_ASYNC"/>
            <appender-ref ref="FILE_WARN_ASYNC"/>
            <appender-ref ref="FILE_ERROR_ASYNC"/>
        </root>
    </springProfile>
</configuration>