package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.SexLimit;

import java.io.Serial;

/**
 * 病史类别
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_medical_type")
public class MedicalType extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 病史类别名称
    private String name;

    // 拼音
    private String py;

    // 性别 0：不限 1：男 2：女
    @Builder.Default
    private SexLimit sex = SexLimit.NORMAL;

    // 用于成人或儿童 0：不限 1：成人 2：儿童
    @Builder.Default
    private AgeLimit age = AgeLimit.NORMAL;

    // 排序值
    @Builder.Default
    private Integer sort = 0;

    // 禁用
    private Boolean disabled;
}