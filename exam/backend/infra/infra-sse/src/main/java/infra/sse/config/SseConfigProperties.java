package infra.sse.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * SSE配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.sse")
public class SseConfigProperties {
    /**
     * 是否启用SSE
     */
    private boolean enabled = false;

    /**
     * 心跳间隔(秒)
     */
    private int heartbeatInterval = 60;

    /**
     * 连接超时时间(分钟)(默认10分钟)
     */
    private long timeoutMin = 10L;
}
