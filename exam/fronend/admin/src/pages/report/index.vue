<script setup lang="ts">
definePage({
  meta: {
    title: '报告处理',
    menu: true,
    order: -4,
    icon: 'fluent-mdl2:report-library',
  },
})

// 示例数据
const reportStats = ref({
  // 总审管理统计
  inspection: {
    pending: 45,
    completed: 128,
    rejected: 8,
    totalToday: 181,
    avgProcessTime: 12.5, // 分钟
    rejectionRate: 4.4, // 百分比
  },
  // 质量抽检统计
  quality: {
    samplingRate: 10, // 抽检比例
    totalSampled: 32,
    qualified: 29,
    unqualified: 3,
    qualificationRate: 90.6,
    commonIssues: [
      { type: '报告规范性', count: 2 },
      { type: '结论准确性', count: 1 },
    ],
  },
  // 报告签发统计
  issue: {
    pendingSign: 23,
    signed: 156,
    rejected: 2,
    totalToday: 181,
    avgSignTime: 8.3, // 分钟
    signRate: 86.2, // 签发率
  },
  // 送达管理统计
  delivery: {
    pending: 18,
    delivered: 163,
    totalToday: 181,
    deliveryMethods: {
      online: 98, // 线上推送
      mail: 45, // 邮寄
      pickup: 20, // 现场领取
    },
    deliveryRate: 90.1,
  },
})

// 今日处理进度
const todayProgress = computed(() => {
  const total = reportStats.value.inspection.totalToday
  const completed = reportStats.value.delivery.delivered
  return Math.round((completed / total) * 100)
})

// 各环节处理效率
const processEfficiency = computed(() => [
  {
    stage: '总审',
    pending: reportStats.value.inspection.pending,
    completed: reportStats.value.inspection.completed,
    avgTime: reportStats.value.inspection.avgProcessTime,
    efficiency: Math.round((reportStats.value.inspection.completed / reportStats.value.inspection.totalToday) * 100),
  },
  {
    stage: '签发',
    pending: reportStats.value.issue.pendingSign,
    completed: reportStats.value.issue.signed,
    avgTime: reportStats.value.issue.avgSignTime,
    efficiency: Math.round((reportStats.value.issue.signed / reportStats.value.issue.totalToday) * 100),
  },
  {
    stage: '送达',
    pending: reportStats.value.delivery.pending,
    completed: reportStats.value.delivery.delivered,
    avgTime: 15.2,
    efficiency: Math.round((reportStats.value.delivery.delivered / reportStats.value.delivery.totalToday) * 100),
  },
])

// 功能模块数据
const modules = ref([
  { name: '总审管理', desc: '总审人员对总检评价进行审核', icon: 'ic:outline-rate-review', path: 'review', color: 'violet' },
  { name: '质量抽检', desc: '系统按规则抽取报告检查', icon: 'ic:outline-high-quality', path: 'inspection', color: 'emerald' },
  { name: '报告签发', desc: '总审通过后生成正式报告', icon: 'fluent:document-signature-20-regular', path: 'issue', color: 'orange' },
  { name: '送达管理', desc: '支持多种送达方式管理', icon: 'icon-park-outline:mail-review', path: 'delivery', color: 'teal' }
])

// 质量控制趋势数据（最近7天）
const qualityTrend = ref([
  { date: '08-14', qualified: 28, unqualified: 2, rate: 93.3 },
  { date: '08-15', qualified: 31, unqualified: 1, rate: 96.9 },
  { date: '08-16', qualified: 26, unqualified: 4, rate: 86.7 },
  { date: '08-17', qualified: 33, unqualified: 2, rate: 94.3 },
  { date: '08-18', qualified: 29, unqualified: 3, rate: 90.6 },
  { date: '08-19', qualified: 35, unqualified: 1, rate: 97.2 },
  { date: '08-20', qualified: 29, unqualified: 3, rate: 90.6 },
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 待总审 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-violet-50 to-violet-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-violet-600 dark:text-violet-400">
              {{ reportStats.inspection.pending }}
            </div>
            <div class="text-sm text-violet-700 dark:text-violet-300 mt-1 font-medium">
              待总审
            </div>
          </div>
          <div class="w-16 h-16 bg-gradient-to-br from-violet-400 to-violet-600 dark:from-violet-500 dark:to-violet-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ic:outline-rate-review" class="text-white text-2xl" />
          </div>
        </div>
      </el-card>

      <!-- 待签发 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">
              {{ reportStats.issue.pendingSign }}
            </div>
            <div class="text-sm text-orange-700 dark:text-orange-300 mt-1 font-medium">
              待签发
            </div>
          </div>
          <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 dark:from-orange-500 dark:to-orange-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="fluent:document-signature-20-regular" class="text-white text-2xl" />
          </div>
        </div>
      </el-card>

      <!-- 待送达 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-teal-50 to-teal-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-teal-600 dark:text-teal-400">
              {{ reportStats.delivery.pending }}
            </div>
            <div class="text-sm text-teal-700 dark:text-teal-300 mt-1 font-medium">
              待送达
            </div>
          </div>
          <div class="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 dark:from-teal-500 dark:to-teal-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="icon-park-outline:mail-review" class="text-white text-2xl" />
          </div>
        </div>
      </el-card>

      <!-- 质量合格率 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ reportStats.quality.qualificationRate }}%
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              质量合格率
            </div>
          </div>
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="ic:outline-high-quality" class="text-white text-2xl" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 左侧：功能模块和处理流程状态 -->
      <div class="lg:col-span-2 space-y-4">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 各环节处理效率 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-800 dark:text-gray-200">各环节处理效率</span>
              <el-tag type="info" size="small">
                实时更新
              </el-tag>
            </div>
          </template>

          <div class="space-y-4">
            <div v-for="item in processEfficiency" :key="item.stage">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <span class="font-medium">{{ item.stage }}</span>
                  <el-tag
                    :type="item.efficiency >= 80 ? 'success' : item.efficiency >= 60 ? 'warning' : 'danger'"
                    size="small"
                  >
                    {{ item.efficiency }}%
                  </el-tag>
                </div>
                <span class="text-sm text-gray-500">平均 {{ item.avgTime }} 分钟</span>
              </div>

              <div class="flex items-center space-x-4 text-sm mb-2">
                <span class="text-success">已完成: {{ item.completed }}</span>
                <span class="text-warning">待处理: {{ item.pending }}</span>
              </div>

              <el-progress
                :percentage="item.efficiency"
                :status="item.efficiency >= 80 ? 'success' : item.efficiency >= 60 ? 'warning' : 'exception'"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>

        <!-- 质量控制趋势 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-800 dark:text-gray-200">质量控制趋势</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">最近7天</span>
            </div>
          </template>

          <div>
            <div class="grid grid-cols-7 gap-2 mb-4">
              <div v-for="day in qualityTrend" :key="day.date" class="text-center">
                <div class="text-xs text-gray-500 mb-1">
                  {{ day.date }}
                </div>
                <div class="h-20 bg-gray-100 rounded flex flex-col justify-end p-1">
                  <div
                    class="bg-success rounded-sm mb-1"
                    :style="{ height: `${(day.qualified / 40) * 100}%` }"
                    :title="`合格: ${day.qualified}`"
                  />
                  <div
                    class="bg-danger rounded-sm"
                    :style="{ height: `${(day.unqualified / 40) * 100}%` }"
                    :title="`不合格: ${day.unqualified}`"
                  />
                </div>
                <div
                  class="text-xs font-medium mt-1"
                  :class="day.rate >= 95 ? 'text-success' : day.rate >= 90 ? 'text-warning' : 'text-danger'"
                >
                  {{ day.rate }}%
                </div>
              </div>
            </div>

            <div class="flex items-center justify-center space-x-6 text-sm">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-success rounded" />
                <span>合格</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-danger rounded" />
                <span>不合格</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：流程状态和数据概览 -->
      <div class="space-y-4">
        <!-- 今日处理进度 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:trend-charts" class="text-primary" />
              <span class="font-medium text-gray-800 dark:text-gray-200">今日处理进度</span>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">总审完成</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((reportStats.inspection.completed / reportStats.inspection.totalToday) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ reportStats.inspection.completed }}/{{ reportStats.inspection.totalToday }}
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">签发完成</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((reportStats.issue.signed / reportStats.issue.totalToday) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ reportStats.issue.signed }}/{{ reportStats.issue.totalToday }}
                </span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">送达完成</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((reportStats.delivery.delivered / reportStats.delivery.totalToday) * 100)"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ reportStats.delivery.delivered }}/{{ reportStats.delivery.totalToday }}
                </span>
              </div>
            </div>
          </div>
        </el-card>



        <!-- 报告状态概览 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:data-analysis" class="text-primary" />
              <span class="font-medium text-gray-800 dark:text-gray-200">报告状态概览</span>
            </div>
          </template>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">总审通过率</span>
              <div class="text-sm">
                <span class="text-success font-medium">{{ Math.round((reportStats.inspection.completed / reportStats.inspection.totalToday) * 100) }}%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">签发及时率</span>
              <div class="text-sm">
                <span class="text-primary font-medium">{{ Math.round((reportStats.issue.signed / reportStats.issue.totalToday) * 100) }}%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">送达完成率</span>
              <div class="text-sm">
                <span class="text-info font-medium">{{ reportStats.delivery.deliveryRate }}%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">质量合格率</span>
              <div class="text-sm">
                <span class="text-warning font-medium">{{ reportStats.quality.qualificationRate }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
