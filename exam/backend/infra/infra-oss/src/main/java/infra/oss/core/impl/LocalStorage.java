package infra.oss.core.impl;

import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.oss.config.OssConfigProperties;
import infra.oss.core.IStorage;
import infra.oss.exception.StorageException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;

/**
 * 本地文件系统存储实现
 */
@Slf4j
public class LocalStorage implements IStorage {
    private static final String TEMP_SUFFIX = ".tmp";

    private final Path rootPath;
    private final String baseUrl;

    public LocalStorage(OssConfigProperties.LocalConfig config) {
        this.rootPath = validateAndInitializePath(config.getPath());
        this.baseUrl = validateBaseUrl(config.getBaseUrl());
    }

    /**
     * 检查对象是否存在
     *
     * @param objectKey 对象键
     * @return true表示存在
     */

    @Override
    public boolean exists(String objectKey) {
        try {
            var path = resolvePath(objectKey);
            return Files.exists(path) && Files.isRegularFile(path);
        } catch (Exception e) {
            throw new StorageException("检查objectKey存在失败：" + objectKey, e);
        }
    }

    /**
     * 上传对象
     *
     * @param source    输入流
     * @param objectKey 对象键
     */
    @Override
    public void upload(InputStream source, String objectKey) {
        var targetPath = resolvePath(objectKey);
        var tempPath = targetPath.resolveSibling(targetPath.getFileName() + TEMP_SUFFIX);

        try {
            Files.createDirectories(targetPath.getParent());

            try (var output = Files.newOutputStream(tempPath, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
                source.transferTo(output);
            }

            Files.move(tempPath, targetPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            try {
                Files.deleteIfExists(tempPath);
            } catch (IOException cleanupException) {
                e.addSuppressed(cleanupException);
            }
            throw new StorageException("上传对象失败：" + objectKey, e);
        }
    }

    /**
     * 下载对象到输出流
     *
     * @param objectKey   对象键
     * @param destination 目标输出流
     */
    @Override
    public void download(String objectKey, OutputStream destination) {
        var sourcePath = resolvePath(objectKey);

        if (!Files.exists(sourcePath)) {
            throw new StorageException("文件不存在：" + objectKey);
        }

        try (var input = Files.newInputStream(sourcePath)) {
            input.transferTo(destination);
        } catch (IOException e) {
            throw new StorageException("下载对象失败：" + objectKey, e);
        }
    }

    /**
     * 获取对象输入流
     *
     * @param objectKey 对象键
     * @return 文件输入流
     */
    @Override
    public InputStream getInputStream(String objectKey) {
        var sourcePath = resolvePath(objectKey);

        if (!Files.exists(sourcePath)) {
            throw new StorageException("文件不存在：" + objectKey);
        }

        try {
            return Files.newInputStream(sourcePath);
        } catch (IOException e) {
            throw new StorageException("获取文件流失败：" + objectKey, e);
        }
    }


    /**
     * 删除对象
     *
     * @param objectKey 对象键
     */
    @Override
    public void delete(String objectKey) {
        try {
            var targetPath = resolvePath(objectKey);
            if (Files.exists(targetPath)) {
                Files.delete(targetPath);
            }
        } catch (Exception e) {
            throw new StorageException("删除对象失败：" + objectKey, e);
        }
    }

    /**
     * 获取对象访问URL
     *
     * @param objectKey 对象键
     * @return 访问URL
     */
    @Override
    public String getUrl(String objectKey) {
        return baseUrl + "/" + objectKey;
    }

    /**
     * 获取对象大小
     *
     * @param objectKey 对象键
     * @return 大小（字节）
     */
    @Override
    public long getSize(String objectKey) {
        try {
            var path = resolvePath(objectKey);
            if (!Files.exists(path)) {
                throw new StorageException("文件不存在：" + objectKey);
            }
            return Files.size(path);
        } catch (Exception e) {
            throw new StorageException("获取对象大小失败：" + objectKey, e);
        }
    }

    /**
     * 验证并初始化存储路径
     */
    private Path validateAndInitializePath(String path) {
        if (Str.isEmpty(path)) {
            throw new ConfigException("配置存储路径path不能为空");
        }

        var sysPath = Path.of(path).toAbsolutePath().normalize();
        try {
            Files.createDirectories(sysPath);
        } catch (IOException e) {
            throw new ConfigException("创建存储目录失败:" + path, e);
        }
        return sysPath;
    }

    /**
     * 验证并初始化baseUrl
     */
    private String validateBaseUrl(String baseUrl) {
        if (Str.isEmpty(baseUrl)) {
            throw new ConfigException("配置baseUrl不能为空");
        }
        return baseUrl.replaceAll("/$", "");
    }

    /**
     * 解析对象存储路径
     */
    private Path resolvePath(String objectKey) {
        if (Str.isEmpty(objectKey)) {
            throw new IllegalArgumentException("objectKey不能为空");
        }

        // 标准化路径，防止路径穿越攻击
        var normalized = Path.of(objectKey).normalize();
        var resolved = rootPath.resolve(normalized).normalize();

        if (!resolved.startsWith(rootPath)) {
            log.warn("非法路径尝试：{}", objectKey);
            throw new StorageException("非法路径：" + objectKey);
        }

        return resolved;
    }
}
