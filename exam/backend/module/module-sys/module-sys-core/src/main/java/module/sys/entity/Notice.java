package module.sys.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 消息通知
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_notice")
public class Notice extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 用户ID
    private Long userId;

    // 标题
    private String title;

    // 内容
    private String content;

    // 消息类型(业务自定)
    private Integer type;

    // 已读
    private Boolean isRead;

    // 跳转链接
    private String link;

    // 发送者ID
    private Long senderId;

    // 发送者名称
    private String senderName;

    // 额外数据(JSON格式存储)
    private String data;

    // 已删除
    @TableLogic
    private Boolean deleted;

    // 创建时间
    @Builder.Default
    private LocalDateTime createTime = LocalDateTime.now();

    // 修改时间
    private LocalDateTime updateTime;
}
