package module.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.io.Serial;

/**
 * ID Builder
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_id_builder")

public class IdBuilder extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // code
    private String code;

    // 长度
    private Integer len;

    // 起始编号
    private Integer startNo;

    // 当前编号
    private Long currentNo;

    // 说明
    private String about;

    @Version
    @Builder.Default
    private Long version = 0L;
}
