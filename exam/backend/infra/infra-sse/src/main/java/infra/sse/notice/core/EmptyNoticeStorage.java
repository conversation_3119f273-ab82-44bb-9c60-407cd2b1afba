package infra.sse.notice.core;

import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.sse.notice.model.NoticeMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 空的存储实现，即不做任何处理
 */
@Slf4j
public class EmptyNoticeStorage implements INoticeStorage {
    @Override
    public Long saveMessage(Long userId, NoticeMessage message) {
        return 0L;
    }

    @Override
    public List<Long> saveMessage(List<Long> userIds, NoticeMessage message) {
        return userIds;
    }

    @Override
    public void markAsRead(Long userId, List<Long> ids) {
    }

    @Override
    public void markAllAsRead(Long userId) {
    }

    @Override
    public int getUnreadCount(Long userId) {
        return 0;
    }

    @Override
    public void delete(Long userId, List<Long> ids) {
    }

    @Override
    public void deleteAll(Long userId) {
    }

    @Override
    public List<NoticeMessage> getList(Long userId, int limit) {
        return Collections.emptyList();
    }

    @Override
    public PageResult<NoticeMessage> getPage(Long userId, NoticeMessage query, PageParam pageParam) {
        return new PageResult<>(Collections.emptyList(), 0L);
    }
}