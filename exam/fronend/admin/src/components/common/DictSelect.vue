<script setup lang="ts">
import { useDict } from '~/composables'

interface StatusSelectProps {
  /** 获取状态数据的key */
  dictKey: string
  nameField?: string
}

const { dictKey, nameField = 'name' } = defineProps<StatusSelectProps>()
const attrs = useAttrs()
const { getDict } = useDict()

const filterText = ref('')
const data = ref<any[]>([])
const modelValue = defineModel<number | undefined>()
const modelName = defineModel<string>('name')

// 过滤后的数据
const filterData = computed(() => {
  return data.value.filter((item) => {
    return item.name?.includes(filterText.value)
      || item.py?.includes(filterText.value)
  })
})

// 处理name
function handleChange(value: any) {
  const node = data.value?.find(o => o.id === value)
  modelName.value = node?.[nameField] || '';
  (attrs.onChange as ((value: any) => void) | undefined)?.(value)
}

// 过滤
function handleFilter(query: string) {
  filterText.value = query
}

onMounted(async () => {
  data.value = await getDict(dictKey)
})
</script>

<template>
  <el-select v-model="modelValue" placeholder="请选择" v-bind="attrs" filterable :filter-method="handleFilter" @change="handleChange">
    <el-option v-for="item in filterData" :key="item.id" :label="item[nameField]" :value="item.id" />
  </el-select>
</template>
