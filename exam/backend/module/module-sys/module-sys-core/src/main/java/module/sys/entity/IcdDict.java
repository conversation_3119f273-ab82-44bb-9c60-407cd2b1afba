package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import module.sys.model.SexLimit;

import java.io.Serial;

/**
 * ICD编码
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_icd_dict")
public class IcdDict extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // ICD编码
    private String icdNo;

    // 疾病名称
    private String name;

    // 拼音
    private String py;

    // 是否传染病 0：否 1：是
    private Boolean isContagion;

    // 是否慢性病 0：否 1：是
    private Boolean isChronic;

    // 性别
    private SexLimit sex;

    // 排序值
    @Builder.Default
    private Integer sort = 0;

    // 禁用
    private Boolean disabled;
}