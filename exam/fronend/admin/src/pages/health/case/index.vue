<script setup lang="ts">
definePage({
  meta: {
    title: '病例管理',
    menu: true,
    order: 3,
    icon: 'lucide:briefcase-medical',
  },
})
</script>

<template>
<p>1. 病例识别：自动标记患有特种疾病（糖尿病、冠心病）或高风险客户，便于快速定位；</p>
<p>2. 方案库支持：内置特种疾病干预建议（如糖尿病饮食指导），健管师可调整方案；</p>
<p>3. 执行跟踪：跟踪干预方案执行情况（如客户是否按方案运动），通过随访验证效果；</p>
<p>4. 协同会诊：发起多科室会诊，共享健康档案，获取意见优化干预方案</p>

健康管理阶段 “特种病例干预（自动识别标记，制定个性化方案，跟踪效果）”；检中阶段 “科室间协调会诊（共享数据，获取意见）”
</template>
