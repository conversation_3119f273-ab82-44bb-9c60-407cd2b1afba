<script setup lang="ts">
import type { SliderVerifyData, TrackPoint } from '~/types/components/SliderVerify'

const props = withDefaults(defineProps<Props>(), {
  text: '请向右滑动滑块完成验证',
  successText: '验证成功',
})

const emit = defineEmits(['change'])

// 是否验证成功
const isVerified = defineModel<boolean>()

interface Props {
  /** 滑块文本 */
  text?: string
  /** 验证成功文本 */
  successText?: string
}

// 滑块相关引用和状态
const sliderTrackRef = ref<HTMLElement | null>(null) // 滑块dom容器
const sliderValue = ref(0) // 滑块当前位置
const { width: sliderWidth } = useElementSize(sliderTrackRef) // 滑块宽度
const isDragging = ref(false) // 是否正在拖动
let startX = 0 // 鼠标或触摸的起始位置
let handleWidth = 0 // 滑块宽度
const handlePadding = 16 // 滑块左右边距

// 行为数据
const track: TrackPoint[] = []
let startTime = 0
let position = 0
let duration = 0

// 鼠标按下事件
function handleMouseDown(e: MouseEvent) {
  if (isVerified.value)
    return

  if (e.target instanceof HTMLElement) {
    handleWidth = e.target.clientWidth
    isDragging.value = true
    startX = e.clientX

    // 开始记录
    startTime = Date.now()
    track.length = 0
    track.push({
      x: 0,
      y: e.clientY / window.innerHeight,
      t: 0,
    })
  }

  e.preventDefault()
}

// 鼠标移动事件
function handleMouseMove(e: MouseEvent) {
  if (!isDragging.value)
    return

  const offsetX = e.clientX - startX
  sliderValue.value = Math.max(0, Math.min(sliderWidth.value - handleWidth - handlePadding, offsetX))

  // 记录轨迹点
  if (sliderWidth.value > 0) {
    const relativeX = (sliderValue.value + handleWidth / 2) / sliderWidth.value
    const relativeY = e.clientY / window.innerHeight

    track.push({
      x: relativeX,
      y: relativeY,
      t: Date.now() - startTime,
    })

    checkVerification()
  }
}

// 鼠标释放事件
function handleMouseUp() {
  if (!isDragging.value)
    return

  finishDrag()
}

// 触摸开始事件
function handleTouchStart(e: TouchEvent) {
  if (isVerified.value)
    return

  if (e.target instanceof HTMLElement) {
    handleWidth = e.target.clientWidth
    isDragging.value = true
    startX = e.touches[0].clientX

    // 开始记录
    startTime = Date.now()
    track.length = 0
    track.push({
      x: 0,
      y: e.touches[0].clientY / window.innerHeight,
      t: 0,
    })
  }

  e.preventDefault()
}

// 触摸移动事件
function handleTouchMove(e: TouchEvent) {
  if (!isDragging.value)
    return

  const offsetX = e.touches[0].clientX - startX
  sliderValue.value = Math.max(0, Math.min(sliderWidth.value - handleWidth - handlePadding, offsetX))

  // 记录轨迹点
  if (sliderWidth.value > 0) {
    // 计算相对位置(0-1之间)
    const relativeX = (sliderValue.value + handleWidth / 2) / sliderWidth.value
    const relativeY = e.touches[0].clientY / window.innerHeight

    track.push({
      x: relativeX,
      y: relativeY,
      t: Date.now() - startTime,
    })
    checkVerification()
  }

  e.preventDefault()
}

// 触摸结束事件
function handleTouchEnd() {
  if (!isDragging.value)
    return

  finishDrag()
}

// 完成拖动
function finishDrag() {
  isDragging.value = false

  if (!isVerified.value) {
    // 如果未验证成功，回到起始位置
    sliderValue.value = 0
    return
  }

  // 计算滑块位置百分比
  position = sliderWidth.value !== 0
    ? (sliderValue.value + handleWidth / 2) / sliderWidth.value
    : 0
  // 计算滑动总时长
  duration = Date.now() - startTime
}

// 检查是否验证结果
function checkVerification() {
  if (!isVerified.value && sliderWidth.value !== 0 && ((sliderValue.value + handleWidth + handlePadding) / sliderWidth.value) >= 0.99) {
    isVerified.value = true
    emit('change', true)
  }
}

// 获取行为数据
function getData(): SliderVerifyData | null {
  if (!isVerified.value)
    return null

  return {
    position,
    track,
    duration,
  }
}

// 事件监听
useEventListener(document, 'mousemove', handleMouseMove)
useEventListener(document, 'mouseup', handleMouseUp)
useEventListener(document, 'touchmove', handleTouchMove, { passive: false })
useEventListener(document, 'touchend', handleTouchEnd)
useEventListener(document, 'touchcancel', handleTouchEnd)

// 重置验证
function reset() {
  isVerified.value = false
  sliderValue.value = 0
}

// 对外暴露方法
defineExpose({
  getData,
  reset,
})
</script>

<template>
  <div
    ref="sliderTrackRef"
    class="relative h-10 w-full select-none overflow-hidden border border-gray-300 rounded-md bg-gray-100 transition-colors dark:border-gray-600 dark:bg-gray-900"
    :style="{
      backgroundColor: isVerified ? 'var(--el-color-success-light-7)' : '',
    }"
  >
    <!-- 提示文本 -->
    <div
      class="absolute left-0 top-0 h-full w-full flex items-center justify-center text-sm text-gray-500 dark:text-gray-400"
    >
      {{ isVerified ? props.successText : props.text }}
    </div>

    <!-- 滑块 -->
    <Transition leave-active-class="transition-opacity duration-300" leave-to-class="opacity-0">
      <div
        v-show="!isVerified" class="absolute left-8px top-0 h-full flex items-center" :style="{
          transform: `translateX(${sliderValue}px)`,
        }" :class="{
          'transition-transform duration-300': !isDragging && !isVerified,
        }"
      >
        <div
          class="h-6 w-6 cursor-pointer rounded-full shadow-md transition-transform hover:scale-110" :style="{
            backgroundColor: isVerified ? 'var(--el-color-success)' : 'var(--el-color-primary)',
          }" @mousedown="handleMouseDown" @touchstart="handleTouchStart"
        />
      </div>
    </Transition>
  </div>
</template>
