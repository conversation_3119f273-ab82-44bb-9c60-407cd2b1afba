import type { SliderVerifyData } from '~/types/components/SliderVerify'
import type { Result } from '~/utils/request'
import { get, post } from '~/utils/request'

// 用户信息
export interface UserInfo {
  id: number
  loginName: string
  userName: string
  needChangePassword: boolean
  deptPosts: Array<any> | null
}

// 登录参数
export interface LoginReq {
  loginName: string
  password: string
  sliderData: SliderVerifyData
}

// Token响应
export interface TokenRes {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// 修改密码
export interface ChangePasswordReq {
  oldPassword: string
  newPassword: string
}

// 刷新Token参数
export interface RefreshTokenReq {
  refreshToken: string
}

/**
 * 用户登录
 */
export function login(data: LoginReq): Promise<Result<TokenRes>> {
  return post<TokenRes>('/sys/auth/login', data)
}

/**
 * 刷新Token
 */
export function refreshToken(data: RefreshTokenReq): Promise<Result<TokenRes>> {
  return post<TokenRes>('/sys/auth/refreshToken', data, undefined, { disableAutoHandleError: true })
}

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<Result<UserInfo>> {
  return post<UserInfo>('/sys/auth/userinfo')
}

/**
 * 获取权限数据
 */
export function getPerms(): Promise<Result<string[]>> {
  return post('/sys/auth/perms')
}

/**
 * 修改密码
 */
export function changePassword(data: ChangePasswordReq): Promise<Result> {
  return post('/sys/auth/changePassword', data)
}

/**
 * 退出登录
 */
export function logout(): Promise<Result> {
  return post('/sys/auth/logout')
}

/**
 * 获取用户基本信息
 */
export function getBaseInfo(): Promise<any> {
  return get('/sys/auth/baseinfo')
}

/**
 * 修改用户基本信息
 */
export function postBaseInfo(data: any): Promise<any> {
  return post('/sys/auth/baseinfo', data)
}

/**
 * 获取用户签名
 */
export function getSign(): Promise<any> {
  return get('/sys/auth/getSign')
}

/**
 * 获取用户签名Base64图片
 */
export async function getSignImage() {
  const res = await getSign()
  if (res && res.data) {
    return JSON.parse(res.data)
  }
  return ''
}

/**
 * 修改用户签名
 */
export function setSign(data: any): Promise<any> {
  return post('/sys/auth/setSign', data)
}

/**
 * 获取用户日志
 */
export function log(): Promise<any> {
  return get('/sys/auth/log')
}
