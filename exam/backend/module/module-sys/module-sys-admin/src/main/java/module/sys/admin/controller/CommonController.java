package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.DataPerm;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.image.ImageProcessor;
import infra.core.security.Aes;
import infra.core.security.Sm4;
import infra.core.text.Str;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.CommonDto;
import module.sys.admin.dto.DataDictDto;
import module.sys.entity.Job;
import module.sys.entity.User;
import module.sys.model.*;
import module.sys.service.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Tag(name = "公共数据获取")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/common")
public class CommonController {

    private final SettingService settingService;
    private final UserService userService;
    private final DataDictService dataDictService;
    private final UpfileService upfileService;

    @Operation(summary = "生成一个key数据")
    @GetMapping("/key")
    public String key(String type) {
        if ("sm4".equals(type)) {
            return Sm4.generateKey();
        } else {
            return Aes.generateKey();
        }
    }

    @Operation(summary = "获取系统设置")
    @GetMapping("/setting")
    public Result<CommonDto.AppInfoDto> getSetting() {
        var settings = settingService.getAppSetting();
        return Result.okData(new CommonDto.AppInfoDto(settings.getAppName(), settings.isWatermark()));
    }

    @Operation(summary = "获取状态字典")
    @GetMapping("/status")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<Integer>>> getStatus(@RequestParam String key) {
        return switch (key) {
            case "job_status" -> Result.okData(IDictEnum.getItems(Job.JobStatus.class));
            case "data_perm" -> Result.okData(IDictEnum.getItems(DataPerm.class));
            case "user_status" -> Result.okData(IDictEnum.getItems(User.UserStatus.class));
            case "sex" -> Result.okData(IDictEnum.getItems(Sex.class));
            case "age_limit" -> Result.okData(IDictEnum.getItems(AgeLimit.class));
            case "sex_limit" -> Result.okData(IDictEnum.getItems(SexLimit.class));
            case "customer_limit" -> Result.okData(IDictEnum.getItems(CustomerLimit.class));
            case "urine" -> Result.okData(IDictEnum.getItems(Urine.class));
            case "dining" -> Result.okData(IDictEnum.getItems(Dining.class));
            default -> Result.fail("不支持的类型");
        };
    }

    @Operation(summary = "获取字典数据")
    @GetMapping("/dict")
    @Perm
    public Result<List<DataDictDto.ValueItem>> getDict(@RequestParam String code) {
        if (Str.isEmpty(code))
            return Result.fail("code参数不能为空");

        return Result.okData(dataDictService.getValuesByCode(code)
                .stream().map(w -> ObjectUtil.copyTo(w, new DataDictDto.ValueItem()))
                .toList());
    }

    @Operation(summary = "上传文件")
    @PostMapping("/uploadFile")
    @AuditLog(code = "sys:common:uploadFile", value = "上传文件")
    public Result<String> uploadFile(@RequestParam("files") MultipartFile file, String info, Long size, String ext) {
        long fileSize = file.getSize();
        if (fileSize == 0) {
            return Result.fail("上传为一个空文件");
        }

        if (!Str.isEmpty(ext)) {
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return Result.fail("文件名不能为空");
            }
            
            // 获取文件扩展名
            String actualExt = "";
            int lastDotIndex = originalFilename.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFilename.length() - 1) {
                actualExt = originalFilename.substring(lastDotIndex + 1).toLowerCase();
            }
            
            // 验证扩展名
            String[] allowedExts = ext.toLowerCase().split(",");
            boolean isValidExt = false;
            for (String allowedExt : allowedExts) {
                if (actualExt.equals(allowedExt.trim())) {
                    isValidExt = true;
                    break;
                }
            }
            
            if (!isValidExt) {
                return Result.fail(String.format("文件类型不支持，仅支持 %s 格式", ext));
            }
        }

        if (size != null && size > 0L && fileSize > size * 1024) {
            return Result.fail(String.format("文件大小超过上传限制，上传为%s，限制%s", formatBytesSize(fileSize), formatBytesSize(size * 1024)));
        }

        var upfile = upfileService.uploadFile(file, info);
        return Result.okData(upfile.getFullPath());
    }

    @Operation(summary = "上传图片")
    @PostMapping("/uploadImage")
    @AuditLog(code = "sys:common:uploadImage", value = "上传图片")
    public Result<String> uploadImage(@RequestParam("files") MultipartFile file, String info, Integer width, Integer height, Long size) throws IOException {
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return Result.fail("仅支持图片文件上传");
        }

        long fileSize = file.getSize();
        if (fileSize == 0) {
            return Result.fail("上传为一个空文件");
        }

        if (size != null && size > 0L && fileSize > size * 1024) {
            return Result.fail(String.format("图片大小超过上传限制，上传为%s，限制%s", formatBytesSize(fileSize), formatBytesSize(size * 1024)));
        }

        MultipartFile uploadFile = file;
        // 缩略图
        if ((width != null && width > 0) || (height != null && height > 0)) {
            try (var imageProcessor = ImageProcessor.from(file)) {
                uploadFile = imageProcessor.thumbnail(width, height)
                        .toMultipartFile(file.getOriginalFilename());
            }
        }

        var upfile = upfileService.uploadFile(uploadFile, info);
        return Result.okData(upfile.getFullPath());
    }

    private String formatBytesSize(Long size) {
        if (size <= 0) {
            return "0B";
        }

        final long k = 1024;
        final String[] sizes = {"B", "KB", "MB", "GB", "TB"};

        int i = (int) Math.floor(Math.log(size) / Math.log(k));
        i = Math.min(i, sizes.length - 1);

        // 计算转换后的值并保留两位小数
        double value = (double) size / Math.pow(k, i);
        BigDecimal roundedValue = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP);
        return roundedValue + sizes[i];
    }
}
