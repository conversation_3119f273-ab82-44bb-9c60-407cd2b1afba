package infra.report.code;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.google.zxing.oned.Code39Writer;
import com.google.zxing.oned.EAN13Writer;
import infra.report.exception.CodeException;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 条码生成器
 */
@Slf4j
public class BarcodeGenerator implements ICodeGenerator {

    private final BarcodeFormat format;

    public BarcodeGenerator(BarcodeFormat format) {
        this.format = format;
    }

    public static BarcodeGenerator code128() {
        return new BarcodeGenerator(BarcodeFormat.CODE_128);
    }

    public static BarcodeGenerator code39() {
        return new BarcodeGenerator(BarcodeFormat.CODE_39);
    }

    public static BarcodeGenerator ean13() {
        return new BarcodeGenerator(BarcodeFormat.EAN_13);
    }

    @Override
    public BufferedImage generateImage(String data, CodeRenderOptions config) {
        validateInput(data, config);

        try {
            BitMatrix bitMatrix = createBitMatrix(data, config);
            return createBufferedImage(bitMatrix, config);
        } catch (WriterException e) {
            log.error("生成条码失败: {}", e.getMessage(), e);
            throw new CodeException("条码生成失败", e);
        }
    }

    @Override
    public void generateToStream(String data, CodeRenderOptions config, OutputStream outputStream) {
        if (outputStream == null) {
            throw new IllegalArgumentException("输出流不能为空");
        }

        try {
            BufferedImage image = generateImage(data, config);
            ImageIO.write(image, config.getFormat(), outputStream);
        } catch (IOException e) {
            log.error("输出条码到流失败: {}", e.getMessage(), e);
            throw new CodeException("输出条码失败", e);
        }
    }

    @Override
    public byte[] generateBytes(String data, CodeRenderOptions config) {
        try (ByteArrayOutputStream byteStream = new ByteArrayOutputStream()) {
            generateToStream(data, config, byteStream);
            return byteStream.toByteArray();
        } catch (IOException e) {
            log.error("生成条码字节数组失败: {}", e.getMessage(), e);
            throw new CodeException("生成条码字节数组失败", e);
        }
    }

    private void validateInput(String data, CodeRenderOptions config) {
        if (data == null || data.trim().isEmpty()) {
            throw new IllegalArgumentException("数据不能为空");
        }
        validateDataForFormat(data);
        if (config == null) {
            throw new IllegalArgumentException("配置不能为空");
        }
        config.validate();
    }

    private void validateDataForFormat(String data) {
        switch (format) {
            case CODE_128 -> {
                if (data.length() > 80) {
                    throw new IllegalArgumentException("Code128条码数据长度不能超过80字符");
                }
                // 检查字符是否为ASCII范围内
                if (!data.chars().allMatch(c -> c >= 0 && c <= 127)) {
                    throw new IllegalArgumentException("Code128只支持ASCII字符");
                }
            }
            case CODE_39 -> {
                if (data.length() > 43) {
                    throw new IllegalArgumentException("Code39条码数据长度不能超过43字符");
                }
                if (!data.matches("^[A-Z0-9\\-. $/+%]*$")) {
                    throw new IllegalArgumentException("Code39只支持大写字母、数字和特殊字符(-. $/+%)");
                }
            }
            case EAN_13 -> {
                if (!data.matches("^\\d{12,13}$")) {
                    throw new IllegalArgumentException("EAN13必须是12或13位数字");
                }
            }
        }
    }

    private BitMatrix createBitMatrix(String data, CodeRenderOptions options) throws WriterException {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.MARGIN, options.getMargin());
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        return switch (format) {
            case CODE_128 -> new Code128Writer().encode(data, format, options.getWidth(), options.getHeight(), hints);
            case CODE_39 -> new Code39Writer().encode(data, format, options.getWidth(), options.getHeight(), hints);
            case EAN_13 -> new EAN13Writer().encode(data, format, options.getWidth(), options.getHeight(), hints);
            default -> throw new IllegalArgumentException("不支持的条码格式: " + format);
        };
    }

    private BufferedImage createBufferedImage(BitMatrix bitMatrix, CodeRenderOptions options) {
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();

        try {
            // 设置抗锯齿
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 设置背景色
            graphics.setColor(options.getBackgroundColor());
            graphics.fillRect(0, 0, width, height);

            // 设置前景色并绘制条码
            graphics.setColor(options.getForegroundColor());
            for (int y = 0; y < height; y++) {
                int startX = -1;
                for (int x = 0; x < width; x++) {
                    if (bitMatrix.get(x, y)) {
                        if (startX == -1) {
                            startX = x;
                        }
                    } else {
                        if (startX != -1) {
                            graphics.fillRect(startX, y, x - startX, 1);
                            startX = -1;
                        }
                    }
                }
                // 处理行末的连续点
                if (startX != -1) {
                    graphics.fillRect(startX, y, width - startX, 1);
                }
            }
        } finally {
            graphics.dispose();
        }
        return image;
    }

}
