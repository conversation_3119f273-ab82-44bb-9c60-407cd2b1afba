import { get, post } from '~/utils/request'

const baseUrl = '/sys/setting'

/**
 * 获取应用设置
 */
export function getApp(): Promise<any> {
  return get<any>(`${baseUrl}/app`)
}

/**
 * 更新应用设置
 */
export function updateApp(data: any): Promise<any> {
  return post<any>(`${baseUrl}/app`, data)
}

/**
 * 获取安全设置
 */
export function getSafe(): Promise<any> {
  return get<any>(`${baseUrl}/safe`)
}

/**
 * 更新安全设置
 */
export function updateSafe(data: any): Promise<any> {
  return post<any>(`${baseUrl}/safe`, data)
}

/**
 * 获取医院信息
 */
export function getHospitalInfo(): Promise<any> {
  return get<any>(`${baseUrl}/hospitalInfo`)
}

/**
 * 更新医院信息
 */
export function updateHospitalInfo(data: any): Promise<any> {
  return post<any>(`${baseUrl}/hospitalInfo`, data)
}
