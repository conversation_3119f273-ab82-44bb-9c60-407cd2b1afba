package module.sys.entity;

import infra.core.common.IDictEnum;
import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import module.sys.model.Sex;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_user")
public class User extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 登录名称
    private String loginName;
    // 用户名称
    private String userName;
    // HIS编号
    private String hisCode;
    // 真实姓名简拼
    private String py;
    // 性别
    private Sex sex;
    // 密码
    private String password;
    // 手机号码
    private String phone;
    // 邮箱
    private String email;
    // 身份证号
    private String idNo;
    // 职称字典ID
    private Long titleDictId;
    // 职称字典名称
    private String titleDictName;
    // 证书编号
    private String certificateNo;
    // 入职日期
    private LocalDate entryDate;
    // 人员类别字典ID
    private Long categoryDictId;
    // 人员类别字典名称
    private String categoryDictName;
    // 人员图像
    private String image;
    // 人员简介
    private String intro;
    // 是否管理员
    @Builder.Default
    private Boolean admin = false;
    // 需要修改密码
    @Builder.Default
    private Boolean needChangePassword = false;
    // 用户状态
    @Builder.Default
    private UserStatus status = UserStatus.NORMAL;
    // 最后登录时间
    private LocalDateTime lastLogin;

    /**
     * 用户状态枚举
     */
    @Getter
    @AllArgsConstructor
    public enum UserStatus implements IDictEnum {
        NORMAL(0, "正常"),
        DISABLED(1, "禁用");

        @EnumValue
        private final int value;
        private final String name;
    }
}
