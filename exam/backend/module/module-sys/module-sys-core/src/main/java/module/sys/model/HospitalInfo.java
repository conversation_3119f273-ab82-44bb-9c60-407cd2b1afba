package module.sys.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HospitalInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 医院名称
      */
    private String name;

    /**
     * 医院检称
     */
    private String shortName;

    /**
     * 医院代码
     */
    private String code;

    /**
     * Logo
     */
    private String logo;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 地址
     */
    private String address;
}
