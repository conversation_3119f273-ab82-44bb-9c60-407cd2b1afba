package infra.report.pdf;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.slf4j.Slf4jLogger;
import com.openhtmltopdf.util.XRLog;
import infra.core.text.Str;
import infra.core.web.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.*;
import java.util.stream.Stream;

/**
 * html to pdf 生成器
 */
@Slf4j
public class HtmlToPdfGenerator {

    // 字体缓存
    private static volatile Map<String, String> FONT_CACHE = null;
    private static final Object SYNC_OBJ = new Object();

    /**
     * 生成PDF
     */
    public static byte[] generatePdf(String html, PdfRenderOptions options) {
        if (Str.isEmpty(html)) {
            throw new IllegalArgumentException("HTML内容不能为空");
        }

        try {
            // 配置日志
            setupLogging();
            // 预处理HTML
            String processedHtml = preprocessHtml(html, options);
            // 初始化字体
            initializeFonts(options);
            // 生成基础PDF
            byte[] basePdf = generateBasePdf(processedHtml, options);
            // 添加页眉页脚和水印
            return addHeaderFooterAndWatermark(basePdf, options);
        } catch (Exception e) {
            log.error("PDF生成失败", e);
            throw new RuntimeException("PDF生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 配置日志
     */
    private static void setupLogging() {
        XRLog.setLoggerImpl(new Slf4jLogger());
        XRLog.setLoggingEnabled(true);
    }

    /**
     * 预处理HTML
     */
    private static String preprocessHtml(String html, PdfRenderOptions options) {
        // 注入页面设置CSS
        String css = buildCss(options);
        html = html.replaceFirst("(?i)</head>", "<style>" + css + "</style>$0");
        return html;
    }

    /**
     * 构建CSS
     */
    private static String buildCss(PdfRenderOptions options) {
        StringBuilder css = new StringBuilder();

        // 页面设置
        css.append("@page { ");
        css.append("margin-top: ").append(options.getMarginTop()).append("pt; ");
        css.append("margin-bottom: ").append(options.getMarginBottom()).append("pt; ");
        css.append("margin-left: ").append(options.getMarginLeft()).append("pt; ");
        css.append("margin-right: ").append(options.getMarginRight()).append("pt; ");

        // 页面大小
        if (options.getPageSize() != null) {
            css.append("size: ").append(getPageSizeCss(options.getPageSize())).append("; ");
        }
        css.append("} ");

        // 字体设置
        css.append("body { ");
        css.append("font-family: '").append(options.getDefaultFontFamily()).append("', ");
        css.append("'SimHei', 'Microsoft YaHei', sans-serif; ");
        css.append("font-size: ").append(options.getDefaultFontSize()).append("pt; ");
        css.append("} ");

        return css.toString();
    }

    /**
     * 获取页面大小CSS
     */
    private static String getPageSizeCss(PdfRenderOptions.PageSize pageSize) {
        return switch (pageSize) {
            case A4 -> "A4";
            case A3 -> "A3";
            case A5 -> "A5";
            case LETTER -> "letter";
            case LEGAL -> "legal";
        };
    }

    /**
     * 初始化字体
     */
    private static void initializeFonts(PdfRenderOptions options) {
        if (FONT_CACHE == null) {
            synchronized (SYNC_OBJ) {
                if (FONT_CACHE == null) {
                    FONT_CACHE = new HashMap<>();

                    try {
                        // 扫描资源字体
                        scanResourceFonts();

                        // 扫描外部字体
                        var externalPaths = determineFontPath(options);
                        externalPaths.forEach(HtmlToPdfGenerator::scanExternalFonts);

                        log.info("字体初始化完成，发现 {} 个字体", FONT_CACHE.size());
                    } catch (Exception e) {
                        log.warn("字体初始化失败", e);
                    }
                }
            }
        }
    }

    /**
     * 扫描资源字体
     */
    private static void scanResourceFonts() {
        try {
            // 已明确在资源中的字体
            String[] resourceFonts = {"simhei.ttf", "simkai.ttf", "simfang.ttf", "simsun.ttc"};
            var classLoader = HtmlToPdfGenerator.class.getClassLoader();

            for (String font : resourceFonts) {
                String fontPath = "fonts/" + font;
                if (classLoader.getResource(fontPath) != null) {
                    String fontFamily = extractFontFamily(font);
                    FONT_CACHE.put(fontFamily, "classpath:" + fontPath);
                    log.debug("发现资源字体: {}", fontFamily);
                }
            }
        } catch (Exception e) {
            log.warn("扫描资源字体失败", e);
        }
    }

    /**
     * 扫描外部字体
     */
    private static void scanExternalFonts(String fontDir) {
        try {
            // 增加路径遍历攻击的防御
            if (fontDir.contains("..")) {
                log.warn("字体路径 '{}' 包含 '..', 可能存在安全风险，已跳过.", fontDir);
                return;
            }
            Path fontPath = Paths.get(fontDir);

            if (!Files.isDirectory(fontPath)) {
                log.warn("字体路径 '{}' 无效或不是一个目录.", fontDir);
                return;
            }

            try (Stream<Path> walk = Files.walk(fontPath)) {
                walk.filter(path -> {
                    String lowerCasePath = path.toString().toLowerCase();
                    return lowerCasePath.endsWith(".ttf") || lowerCasePath.endsWith(".otf");
                }).forEach(path -> {
                    try {
                        String fontFamily = extractFontFamily(path.getFileName().toString());
                        FONT_CACHE.put(fontFamily, path.toAbsolutePath().toString());
                        log.debug("发现外部字体: {} -> {}", fontFamily, path);
                    } catch (Exception e) {
                        log.warn("处理字体文件失败: {}", path, e);
                    }
                });
            }
        } catch (Exception e) {
            log.warn("扫描外部字体失败: {}", fontDir, e);
        }
    }


    /**
     * 获取外部字体路径
     */
    private static List<String> determineFontPath(PdfRenderOptions options) {
        List<String> paths = new ArrayList<>();

        if (options == null)
            return paths;

        if (!Str.isEmpty(options.getFontPath())) {
            paths.add(options.getFontPath());
        }

        // 常用系统字体
        if (options.isLoadSystemFont()) {
            String[] commonPaths = {"/usr/share/fonts", "C:\\Windows\\Fonts", "/System/Library/Fonts"};
            for (String path : commonPaths) {
                if (Files.exists(Paths.get(path))) {
                    paths.add(path);
                }
            }
        }

        return paths;
    }

    /**
     * 提取字体族名称
     */
    private static String extractFontFamily(String fileName) {
        // 默认使用文件名（去掉扩展名）
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;
    }

    /**
     * 生成基础PDF
     */
    private static byte[] generateBasePdf(String html, PdfRenderOptions options) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            PdfRendererBuilder builder = new PdfRendererBuilder().withHtmlContent(html, getBaseUri()).toStream(outputStream);

            // 设置字体
            setupFonts(builder);
            // 设置页面配置
            setupPageConfiguration(builder, options);
            // 运行渲染
            builder.run();

            return outputStream.toByteArray();
        }
    }

    /**
     * 获取基础URI
     */
    private static String getBaseUri() {
        var request = ServletUtil.getRequest();
        if (request == null) {
            return null;
        }

        StringBuilder url = new StringBuilder();
        url.append(request.getScheme()).append("://");
        url.append(request.getServerName());

        int port = request.getServerPort();
        String scheme = request.getScheme();
        boolean isDefaultPort = ("http".equals(scheme) && port == 80)
                || ("https".equals(scheme) && port == 443);
        if (!isDefaultPort) {
            url.append(":").append(port);
        }

        url.append(request.getContextPath());
        return url.toString();
    }

    /**
     * 设置字体
     */
    private static void setupFonts(PdfRendererBuilder builder) {
        try {
            int successCount = 0;
            var resourceLoader = HtmlToPdfGenerator.class.getClassLoader();

            for (var entry : FONT_CACHE.entrySet()) {
                String fontFamily = entry.getKey();
                String fontPath = entry.getValue();

                try {
                    if (fontPath.startsWith("classpath:")) {
                        String resourcePath = fontPath.substring("classpath:".length());

                        // 使用 supplier 确保每次都创建新的流
                        builder.useFont(() -> {
                            try {
                                return Optional.ofNullable(resourceLoader.getResourceAsStream(resourcePath))
                                        .map(BufferedInputStream::new)
                                        .orElse(null);
                            } catch (Exception e) {
                                log.debug("获取资源字体流失败: {}", resourcePath, e);
                                return null;
                            }
                        }, fontFamily);
                    } else {
                        File fontFile = new File(fontPath);
                        if (fontFile.exists() && fontFile.canRead()) {
                            builder.useFont(fontFile, fontFamily);
                        }
                    }

                    successCount++;
                } catch (Exception e) {
                    log.warn("字体注册失败: {} -> {}", fontFamily, fontPath, e);
                }
            }

            if (successCount == 0) {
                log.warn("没有成功注册任何字体，PDF字体显示可能受到影响");
            } else {
                log.debug("成功注册 {} 个字体", successCount);
            }

        } catch (Exception e) {
            log.error("设置字体失败，PDF字体显示可能受到影响", e);
        }
    }


    /**
     * 设置页面配置
     */
    private static void setupPageConfiguration(PdfRendererBuilder builder, PdfRenderOptions options) {
        try {
            switch (options.getPageSize()) {
                case A3 -> builder.useDefaultPageSize(297, 420, PdfRendererBuilder.PageSizeUnits.MM);
                case A5 -> builder.useDefaultPageSize(148, 210, PdfRendererBuilder.PageSizeUnits.MM);
                case LETTER -> builder.useDefaultPageSize(8.5f, 11f, PdfRendererBuilder.PageSizeUnits.INCHES);
                case LEGAL -> builder.useDefaultPageSize(8.5f, 14f, PdfRendererBuilder.PageSizeUnits.INCHES);
                default -> builder.useDefaultPageSize(210, 297, PdfRendererBuilder.PageSizeUnits.MM);
            }

            // 启用快速模式
            builder.useFastMode();
        } catch (Exception e) {
            log.warn("设置页面配置失败", e);
        }
    }

    /**
     * 添加页眉页脚和水印
     */
    private static byte[] addHeaderFooterAndWatermark(byte[] pdfBytes, PdfRenderOptions options) {
        if (options.getHeaderFooterOptions() == null && options.getWatermarkOptions() == null) {
            return pdfBytes;
        }

        try (PDDocument document = Loader.loadPDF(pdfBytes); ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            PDFont font = getDefaultFont(document, options);
            int totalPages = document.getNumberOfPages();

            for (int i = 0; i < totalPages; i++) {
                PDPage page = document.getPage(i);

                try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {

                    // 添加页眉页脚
                    if (options.getHeaderFooterOptions() != null) {
                        addHeaderFooter(contentStream, page, font, options.getHeaderFooterOptions(), i + 1, totalPages);
                    }

                    // 添加水印
                    if (options.getWatermarkOptions() != null) {
                        addWatermark(contentStream, page, font, options.getWatermarkOptions(), document);
                    }
                }
            }

            document.save(outputStream);
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.warn("添加页眉页脚和水印失败，返回原始PDF", e);
            return pdfBytes;
        }
    }

    /**
     * 获取默认字体
     */
    private static PDFont getDefaultFont(PDDocument document, PdfRenderOptions options) {
        return Stream.of(options.getDefaultFontFamily(), "simhei", "simsun")
                .filter(Objects::nonNull)
                .map(fontFamily -> loadFontFromCache(document, fontFamily))
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(new PDType1Font(Standard14Fonts.FontName.HELVETICA));
    }

    /**
     * 从缓存加载字体
     */
    private static PDFont loadFontFromCache(PDDocument document, String fontFamily) {
        if (Str.isEmpty(fontFamily)) {
            return null;
        }

        String fontPath = FONT_CACHE.get(fontFamily);
        if (fontPath == null) {
            return null;
        }

        try {
            if (fontPath.startsWith("classpath:")) {
                String resourcePath = fontPath.substring("classpath:".length());
                try (InputStream fontStream = HtmlToPdfGenerator.class.getClassLoader().getResourceAsStream(resourcePath)) {
                    if (fontStream != null) {
                        return PDType0Font.load(document, fontStream);
                    }
                }
            } else {
                File fontFile = new File(fontPath);
                if (fontFile.exists() && fontFile.canRead()) {
                    return PDType0Font.load(document, fontFile);
                }
            }
        } catch (Exception e) {
            log.warn("加载默认字体失败: {} - {}", fontFamily, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 添加页眉页脚
     */
    private static void addHeaderFooter(PDPageContentStream contentStream, PDPage page, PDFont font,
                                        PdfRenderOptions.HeaderFooterOptions options, int pageNumber, int totalPages) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();
            float fontSize = options.getFontSize();

            // 计算动态边距
            float horizontalMargin = Math.max(20, Math.min(50, pageWidth * 0.05f));
            float verticalMargin = Math.max(20, Math.min(40, pageHeight * 0.02f));

            // 页眉位置
            float headerY = pageHeight - verticalMargin;

            // 添加页眉
            addTextWithAlignment(contentStream, options.getHeaderLeft(), options.getHeaderLeftColor(),
                    horizontalMargin, headerY, font, fontSize, TextAlignment.LEFT);
            addTextWithAlignment(contentStream, options.getHeaderCenter(), options.getHeaderCenterColor(),
                    pageWidth / 2, headerY, font, fontSize, TextAlignment.CENTER);
            addTextWithAlignment(contentStream, options.getHeaderRight(), options.getHeaderRightColor(),
                    pageWidth - horizontalMargin, headerY, font, fontSize, TextAlignment.RIGHT);

            // 添加页脚
            addTextWithAlignment(contentStream, options.getFooterLeft(), options.getFooterLeftColor(),
                    horizontalMargin, verticalMargin, font, fontSize, TextAlignment.LEFT);
            addTextWithAlignment(contentStream, options.getFooterCenter(), options.getFooterCenterColor(),
                    pageWidth / 2, verticalMargin, font, fontSize, TextAlignment.CENTER);

            String footerRight = replacePlaceholders(options.getFooterRight(), pageNumber, totalPages);
            addTextWithAlignment(contentStream, footerRight, options.getFooterRightColor(),
                    pageWidth - horizontalMargin, verticalMargin, font, fontSize, TextAlignment.RIGHT);

        } catch (Exception e) {
            log.warn("添加页眉页脚失败", e);
        }
    }


    /**
     * 添加页眉页脚文本
     */
    private static void addTextWithAlignment(PDPageContentStream contentStream, String text, String color,
                                             float x, float y, PDFont font, float fontSize, TextAlignment alignment) {
        if (Str.isEmpty(text)) return;

        try {
            contentStream.setFont(font, fontSize);
            contentStream.setNonStrokingColor(parseColor(color));

            float textWidth = font.getStringWidth(text) / 1000 * fontSize;
            float startX;

            switch (alignment) {
                case CENTER -> startX = x - textWidth / 2;
                case RIGHT -> startX = x - textWidth;
                default -> startX = x; // LEFT
            }

            contentStream.beginText();
            contentStream.newLineAtOffset(startX, y);
            contentStream.showText(text);
            contentStream.endText();

        } catch (Exception e) {
            log.warn("添加页眉页脚文本失败: '{}'", text, e);
        }
    }


    /**
     * 添加水印
     */
    private static void addWatermark(PDPageContentStream contentStream, PDPage page, PDFont font, PdfRenderOptions.WatermarkOptions options, PDDocument document) {
        try {
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();

            // 设置透明度
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(options.getOpacity());
            contentStream.setGraphicsStateParameters(graphicsState);

            // 优先尝试图片水印
            boolean imageWatermarkAdded = false;
            if (!Str.isEmpty(options.getImagePath())) {
                imageWatermarkAdded = addImageWatermark(contentStream, page, options, pageWidth, pageHeight, document);
            }

            // 如果图片水印失败，使用文本水印
            if (!imageWatermarkAdded && !Str.isEmpty(options.getText())) {
                addTextWatermark(contentStream, page, font, options, pageWidth, pageHeight);
            }

        } catch (Exception e) {
            log.warn("添加水印失败", e);
        }
    }

    /**
     * 添加文本水印
     */
    private static void addTextWatermark(PDPageContentStream contentStream, PDPage page, PDFont font, PdfRenderOptions.WatermarkOptions options, float pageWidth, float pageHeight) {
        try {
            contentStream.setFont(font, options.getFontSize());
            contentStream.setNonStrokingColor(parseColor(options.getColor()));

            String text = options.getText();
            float textWidth = font.getStringWidth(text) / 1000 * options.getFontSize();

            if (options.isRepeatWatermark()) {
                // 循环水印
                float horizontalSpacing = options.getHorizontalSpacing();
                float verticalSpacing = options.getVerticalSpacing();
                float density = options.getDensity();

                horizontalSpacing = horizontalSpacing / density;
                verticalSpacing = verticalSpacing / density;

                for (float y = 0; y < pageHeight; y += verticalSpacing) {
                    for (float x = 0; x < pageWidth; x += horizontalSpacing) {
                        addSingleTextWatermark(contentStream, text, x, y, options.getRotation());
                    }
                }
            } else {
                // 单个水印
                float x = (pageWidth - textWidth) / 2;
                float y = pageHeight / 2;
                addSingleTextWatermark(contentStream, text, x, y, options.getRotation());
            }

        } catch (Exception e) {
            log.warn("添加文本水印失败", e);
        }
    }

    /**
     * 添加单个文本水印
     */
    private static void addSingleTextWatermark(PDPageContentStream contentStream, String text, float x, float y, float rotation) {
        try {
            contentStream.saveGraphicsState();
            contentStream.transform(Matrix.getRotateInstance(Math.toRadians(rotation), x, y));
            contentStream.beginText();
            contentStream.newLineAtOffset(x, y);
            contentStream.showText(text);
            contentStream.endText();
            contentStream.restoreGraphicsState();
        } catch (Exception e) {
            log.debug("添加单个文本水印失败", e);
        }
    }

    /**
     * 添加图片水印
     */
    private static boolean addImageWatermark(PDPageContentStream contentStream, PDPage page, PdfRenderOptions.WatermarkOptions options, float pageWidth, float pageHeight, PDDocument document) {
        try {
            BufferedImage image = loadWatermarkImage(options.getImagePath());
            if (image == null) {
                return false;
            }

            // 创建PDImageXObject
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            ImageIO.write(image, "PNG", byteStream);

            org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject pdImage = org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject.createFromByteArray(document, byteStream.toByteArray(), "watermark");

            float imageWidth = image.getWidth();
            float imageHeight = image.getHeight();

            if (options.isRepeatWatermark()) {
                // 循环图片水印
                float horizontalSpacing = options.getHorizontalSpacing() / options.getDensity();
                float verticalSpacing = options.getVerticalSpacing() / options.getDensity();

                for (float y = 0; y < pageHeight; y += verticalSpacing) {
                    for (float x = 0; x < pageWidth; x += horizontalSpacing) {
                        contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
                    }
                }
            } else {
                // 单个图片水印
                float x = (pageWidth - imageWidth) / 2;
                float y = (pageHeight - imageHeight) / 2;
                contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
            }

            return true;

        } catch (Exception e) {
            log.warn("添加图片水印失败", e);
            return false;
        }
    }

    /**
     * 加载水印图片
     */
    private static BufferedImage loadWatermarkImage(String imagePath) {
        try {
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return ImageIO.read(URI.create(imagePath).toURL());
            } else if (imagePath.startsWith("classpath:")) {
                String resourcePath = imagePath.substring("classpath:".length());
                try (InputStream imageStream = HtmlToPdfGenerator.class.getClassLoader().getResourceAsStream(resourcePath)) {
                    if (imageStream == null) {
                        log.warn("找不到类路径下的水印图片: {}", imagePath);
                        return null;
                    }
                    return ImageIO.read(imageStream);
                }
            } else {
                File imageFile = new File(imagePath);
                if (!imageFile.exists() || !imageFile.canRead()) {
                    log.warn("水印图片文件不存在或不可读: {}", imagePath);
                    return null;
                }
                return ImageIO.read(imageFile);
            }
        } catch (Exception e) {
            log.warn("加载水印图片失败: {}", imagePath, e);
            return null;
        }
    }


    /**
     * 替换占位符
     */
    private static String replacePlaceholders(String text, int pageNumber, int totalPages) {
        if (text == null) return "";
        return text.replace("{page}", String.valueOf(pageNumber)).replace("{total}", String.valueOf(totalPages));
    }

    /**
     * 解析颜色
     */
    private static Color parseColor(String colorStr) {
        if (Str.isEmpty(colorStr)) return Color.BLACK;

        try {
            if (colorStr.startsWith("#")) {
                return Color.decode(colorStr);
            } else {
                return switch (colorStr.toLowerCase()) {
                    case "white" -> Color.WHITE;
                    case "red" -> Color.RED;
                    case "green" -> Color.GREEN;
                    case "blue" -> Color.BLUE;
                    case "gray", "grey" -> Color.GRAY;
                    default -> Color.BLACK;
                };
            }
        } catch (Exception e) {
            return Color.BLACK;
        }
    }

    /**
     * 文本对齐枚举
     */
    private enum TextAlignment {
        LEFT, CENTER, RIGHT
    }
}
