<script setup lang="ts">
definePage({
  meta: {
    title: '检验配置',
    menu: true,
    order: 5,
    icon: 'hugeicons:configuration-01',
  },
})
</script>

<template>
<p>1. 标本管理配置：设置标本采集、交接、检验各环节的扫码确认规则，配置标本唯一标识生成逻辑，关联检中标本追溯模块；</p>
<p>2. 检验结果校验：配置检验结果录入的格式校验标准（如数值范围、图像完整性）、异常值标红规则，同步至检查科室结果录入模块；</p>
<p>3. 外送标本设置：配置外送标本的信息记录项（外送机构、接收人）、结果回填触发条件，确保外送数据与院内数据整合</p>

检中阶段 “检验标本采验（全流程管理，实时记录流转信息）”🔶21-29；检中阶段 “标本外送回填（外送结果自动整合）”🔶21-31；检查科室 “标本全流程追溯（扫码确认，实时追踪状态）”🔶21-82
</template>
