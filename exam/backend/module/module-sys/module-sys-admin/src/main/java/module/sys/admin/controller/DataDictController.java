package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.DataDictDto;
import module.sys.entity.DataDict;
import module.sys.entity.DataDictValue;
import module.sys.service.DataDictService;
import module.sys.service.DataDictValueService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "数据字典")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/dict")
public class DataDictController {

    private final DataDictService dataDictService;
    private final DataDictValueService dataDictValueService;

    @Operation(summary = "数据字典列表")
    @GetMapping("/list")
    @Perm("sys:dict")
    public Result<PageResult<DataDict>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = dataDictService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                w.like("name".equals(type), "name", keyTrim);
                w.like("code".equals(type), "code", keyTrim);
                w.like("info".equals(type), "info", keyTrim);
            }
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增字典")
    @PostMapping("/add")
    @AuditLog(code = "sys:dict:add", value = "数据字典-新增")
    @Perm("sys:dict:add")
    public Result<Long> add(@RequestBody @Valid DataDictDto.EditDto dto) {
        DataDict entity = DataDict.builder()
                .name(dto.getName())
                .code(dto.getCode())
                .info(dto.getInfo())
                .sort(dto.getSort())
                .build();

        return dataDictService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑字典")
    @GetMapping("/edit")
    @Perm("sys:dict:edit")
    public Result<DataDictDto.EditDto> getEdit(Long id) {
        var dictOpt = dataDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(dictOpt.orElse(null), new DataDictDto.EditDto())
        );
    }

    @Operation(summary = "编辑字典")
    @PostMapping("/edit")
    @AuditLog(code = "sys:dict:edit", value = "数据字典-编辑")
    @Perm("sys:dict:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid DataDictDto.EditDto dto) {
        var dictOpt = dataDictService.getById(id);
        if (dictOpt.isEmpty()) {
            return Result.fail("数据字典不存在");
        }

        DataDict dict = dictOpt.get();
        dict.setName(dto.getName());
        dict.setCode(dto.getCode());
        dict.setInfo(dto.getInfo());
        dict.setSort(dto.getSort());

        return dataDictService.update(dict) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除字典")
    @PostMapping("/delete")
    @AuditLog(code = "sys:dict:delete", value = "数据字典-删除")
    @Perm("sys:dict:delete")
    @Transactional
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            dataDictService.deleteByIds(ids);
            dataDictValueService.deleteBatch(w -> w.in("dict_id", ids));
        }
        return Result.OK;
    }

    @Operation(summary = "数据字典值列表")
    @GetMapping("/valueList")
    @Perm("sys:dict")
    public Result<List<DataDictValue>> valueList(Long id) {
        List<DataDictValue> entities = dataDictValueService.getListByDict(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(entities);
    }

    @Operation(summary = "新增字典值")
    @PostMapping("/valueAdd")
    @AuditLog(code = "sys:dict:add", value = "数据字典-新增值")
    @Perm("sys:dict:add")
    public Result<Long> valueAdd(@RequestBody @Valid DataDictDto.ValueEditDto dto) {
        DataDictValue entity = DataDictValue.builder()
                .dictId(dto.getDictId())
                .name(dto.getName())
                .code(dto.getCode())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return dataDictValueService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑字典值")
    @GetMapping("/valueEdit")
    @Perm("sys:dict:edit")
    public Result<DataDictDto.ValueEditDto> valueGetEdit(Long id) {
        var valueOpt = dataDictValueService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(valueOpt.orElse(null), new DataDictDto.ValueEditDto())
        );
    }

    @Operation(summary = "编辑字典值")
    @PostMapping("/valueEdit")
    @AuditLog(code = "sys:dict:edit", value = "数据字典-编辑值")
    @Perm("sys:dict:edit")
    public Result<Void> valueEdit(@RequestParam Long id, @RequestBody @Valid DataDictDto.ValueEditDto dto) {
        var entity = dataDictValueService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("数据字典值不存在");
        }

        DataDictValue value = entity.get();
        value.setName(dto.getName());
        value.setCode(dto.getCode());
        value.setSort(dto.getSort());
        value.setDisabled(dto.getDisabled());

        return dataDictValueService.update(value) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除字典值")
    @PostMapping("/valueDelete")
    @AuditLog(code = "sys:dict:delete", value = "数据字典-删除值")
    @Perm("sys:dict:delete")
    public Result<Void> valueDelete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            dataDictValueService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用字典值")
    @PostMapping("/valueEnable")
    @AuditLog(code = "sys:dict:valueEnable", value = "字典值-启用")
    @Perm("sys:dict:valueEnable")
    public Result<Void> valueEnable(@RequestBody List<Long> ids) {
        dataDictValueService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用字典值")
    @PostMapping("/valueDisable")
    @AuditLog(code = "sys:dict:valueDisable", value = "字典值-禁用")
    @Perm("sys:dict:valueDisable")
    public Result<Void> valueDisable(@RequestBody List<Long> ids) {
        dataDictValueService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "导出字典值")
    @PostMapping("/valueExport")
    @AuditLog(code = "sys:dict:export", value = "数据字典-导出值")
    @Perm("sys:dict:export")
    public void valueExport(Long dictId, HttpServletResponse response) throws IOException {
        var dictOpt = dataDictService.getById(dictId);
        if (dictOpt.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return;
        }

        DataDict dict = dictOpt.get();
        List<DataDictValue> values = dataDictValueService.getListByDict(dictId, FilterOptions.DISABLE_ALL_FILTER);

        ExcelExporter.<DataDictValue>create()
                .sheet(dict.getName() + "字典值")
                .columns(cols ->
                        cols.add("名称", DataDictValue::getName)
                                .add("编码", DataDictValue::getCode)
                                .add("排序", DataDictValue::getSort)
                                .add("状态", value -> value.getDisabled() ? "禁用" : "启用")
                )
                .data(values)
                .export(response, dict.getName());
    }

    @Operation(summary = "导入字典值")
    @PostMapping("/valueImport")
    @AuditLog(code = "sys:dict:import", value = "数据字典-导入值")
    @Perm("sys:dict:import")
    @Transactional
    public Result<Void> valueImport(
            @RequestParam Long dictId,
            @RequestParam("files") MultipartFile file) {

        var dictOpt = dataDictService.getById(dictId);
        if (dictOpt.isEmpty()) {
            return Result.fail("数据字典不存在");
        }

        try {
            ImportResult<DataDictValue> parseResult = ExcelImporter.create(() -> DataDictValue.builder().dictId(dictId).build())
                    .columns(cols -> {
                        cols.required("名称", ExcelParser.string(), DataDictValue::setName);
                        cols.add("编码", ExcelParser.string(), DataDictValue::setCode);
                        cols.withDefault("排序", ExcelParser.integer(), DataDictValue::setSort, 0);
                        cols.withDefault("状态", "禁用"::equals, DataDictValue::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            dataDictValueService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }
}
