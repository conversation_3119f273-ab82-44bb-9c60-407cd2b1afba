package infra.sms.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import infra.core.common.Result;
import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.sms.config.SmsConfigProperties;
import infra.sms.core.ISmsSender;
import lombok.extern.slf4j.Slf4j;

/**
 * 腾讯云短信服务实现
 */
@Slf4j
public class TencentSmsSender implements ISmsSender {
    private final SmsConfigProperties.TencentConfig config;
    private final SmsClient client;

    public TencentSmsSender(SmsConfigProperties.TencentConfig config) {
        this.config = validateConfig(config);
        this.client = createClient();
    }

    /**
     * 发送短信
     *
     * @param number   接收号码
     * @param templateCode 模板
     * @param params   短信参数
     * @return 发送结果
     */
    @Override
    public Result<String> send(String number, String templateCode, String... params) {
        if (Str.isEmpty(number)) {
            return Result.fail("接收号码不能为空");
        }
        if (Str.isEmpty(templateCode)) {
            return Result.fail("发送模板不能为空");
        }

        try {
            SendSmsRequest request = new SendSmsRequest();

            request.setSmsSdkAppId(config.getSdkAppId());
            request.setSignName(config.getSignName());
            request.setTemplateId(templateCode);

            // 设置接收短信的手机号码（需要加国际区号，中国大陆为+86）
            String[] phoneNumbers = {formatPhoneNumber(number)};
            request.setPhoneNumberSet(phoneNumbers);

            // 设置模板参数
            if (params != null && params.length > 0) {
                request.setTemplateParamSet(params);
            }

            SendSmsResponse response = client.SendSms(request);
            return handleResponse(number, templateCode, response);
        } catch (Exception e) {
            log.error("[腾讯云短信] 发送失败: {}", e.getMessage(), e);
            return Result.fail("短信发送失败: " + e.getMessage());
        }
    }

    /**
     * 处理发送响应
     */
    private Result<String> handleResponse(String number, String template, SendSmsResponse response) {
        SendStatus[] statusSet = response.getSendStatusSet();

        if (statusSet != null && statusSet.length > 0) {
            SendStatus status = statusSet[0];

            if ("Ok".equals(status.getCode())) {
                return Result.OK_STRING;
            } else {
                log.error("[腾讯云短信] 发送失败 - 手机号: {}, 错误码: {}, 错误信息: {}", number, status.getCode(), status.getMessage());
                return Result.fail("短信发送失败: " + status.getMessage());
            }
        } else {
            log.error("[腾讯云短信] 响应异常 - 手机号: {}, 无状态信息", number);
            return Result.fail("短信发送失败: 响应状态异常");
        }
    }

    /**
     * 格式化手机号码，添加国际区号
     */
    private String formatPhoneNumber(String number) {
        if (number.startsWith("+")) {
            return number;
        }
        if (number.startsWith("86")) {
            return "+" + number;
        }
        return "+86" + number;
    }

    /**
     * 创建腾讯云SMS客户端
     */
    private SmsClient createClient() {
        try {
            Credential credential = new Credential(config.getSecretId(), config.getSecretKey());

            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(config.getEndpoint());

            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            return new SmsClient(credential, config.getRegion(), clientProfile);
        } catch (Exception e) {
            log.error("[腾讯云短信] 客户端初始化失败: {}", e.getMessage(), e);
            throw new ConfigException("腾讯云短信客户端初始化失败", e);
        }
    }

    /**
     * 验证配置参数
     */
    private SmsConfigProperties.TencentConfig validateConfig(SmsConfigProperties.TencentConfig config) {
        if (Str.isEmpty(config.getSecretId()) || Str.isEmpty(config.getSecretKey()) || Str.isEmpty(config.getSignName()) || Str.isEmpty(config.getSdkAppId()) || Str.isEmpty(config.getEndpoint())) {
            throw new ConfigException("腾讯云短信配置不完整");
        }

        return config;
    }
}
