package infra.core.security;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HexFormat;

/**
 * md5 hash
 */
public final class Md5 {
    private static final HexFormat HEX_FORMAT = HexFormat.of().withLowerCase();

    /**
     * 计算字节数组的 MD5 哈希值（直接返回字节数组，避免编码开销）
     */
    public static byte[] hashBytes(byte[] input) {
        final MessageDigest md = getMd5Digest();
        return md.digest(input);
    }

    /**
     * 计算字符串的 MD5 哈希值（UTF-8 编码）
     */
    public static String hashString(String input) {
        final MessageDigest md = getMd5Digest();
        byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
        return HEX_FORMAT.formatHex(bytes);
    }

    /**
     * 针对 NIO ByteBuffer 的高效哈希计算（适用于文件或网络流）
     */
    public static String hashByteBuffer(ByteBuffer buffer) {
        final MessageDigest md = getMd5Digest();
        md.update(buffer);
        byte[] bytes = md.digest();
        buffer.rewind(); // 重置缓冲区位置
        return HEX_FORMAT.formatHex(bytes);
    }

    /**
     * 获取MD5摘要算法实例
     */
    private static MessageDigest getMd5Digest() {
        try {
            return MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("MD5 algorithm not available", e);
        }
    }
}
