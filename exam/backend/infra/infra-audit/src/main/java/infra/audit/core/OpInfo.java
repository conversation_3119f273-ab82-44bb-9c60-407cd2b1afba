package infra.audit.core;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审计操作信息
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OpInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    // 模块
    private String module;
    // 操作编码(可以考虑同权限编号)
    private String code;
    // 操作详情
    private String detail;
    // 用户ID
    private Long userId;
    // 登录账号
    private String loginName;
    // 用户名称
    private String userName;
    // URL
    private String url;
    // 操作IP
    private String ip;
    // 附属数据
    private Object data;
    // 是否成功
    private Boolean success;
    // 错误信息(记录错误且抛错时)
    private String error;
    // 请求时间
    private LocalDateTime startTime;
    // 处理耗时(毫秒)
    private Long useTime;
}