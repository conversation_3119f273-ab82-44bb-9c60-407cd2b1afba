package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.JSON;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.pdf.PdfUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.DocTemplateDto;
import module.sys.doc.IDocTemplate;
import module.sys.entity.DocTemplate;
import module.sys.service.DocTemplateService;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Tag(name = "文书模板")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/docTemplate")
public class DocTemplateController {
    private final DocTemplateService docTemplateService;
    private final ApplicationContext applicationContext;
    private final ObjectMapper objectMapper;

    @Operation(summary = "文书模板列表")
    @GetMapping("/list")
    @Perm("health:docTemplate")
    public Result<PageResult<DocTemplate>> list(
            String type,
            String key,
            Boolean isSys,
            PageParam pageParam) {

        var result = docTemplateService.getPage(w -> applyQueryCondition(w, type, key, isSys), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增文书模板")
    @PostMapping("/add")
    @AuditLog(code = "health:docTemplate:add", value = "文书模板-新增")
    @Perm("health:docTemplate:add")
    public Result<Long> add(@RequestBody @Valid DocTemplateDto.EditDto dto) {
        DocTemplate entity = DocTemplate.builder()
                .name(dto.getName())
                .code(dto.getCode())
                .template(dto.getTemplate())
                .isSys(false)
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return docTemplateService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑文书模板")
    @GetMapping("/edit")
    @Perm("health:docTemplate:edit")
    public Result<DocTemplateDto.EditDto> getEdit(Long id) {
        var entityOpt = docTemplateService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new DocTemplateDto.EditDto())
        );
    }

    @Operation(summary = "编辑文书模板")
    @PostMapping("/edit")
    @AuditLog(code = "health:docTemplate:edit", value = "文书模板-编辑")
    @Perm("health:docTemplate:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid DocTemplateDto.EditDto dto) {
        var entityOpt = docTemplateService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("文书模板不存在");
        }

        DocTemplate entity = entityOpt.get();
        entity.setSort(dto.getSort());
        if (!entity.getIsSys()) {
            // 非系统模板才可以更改以下内容
            entity.setName(dto.getName());
            entity.setCode(dto.getCode());
            entity.setTemplate(dto.getTemplate());
            entity.setDisabled(dto.getDisabled());
        }
        return docTemplateService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除文书模板")
    @PostMapping("/delete")
    @AuditLog(code = "health:docTemplate:delete", value = "文书模板-删除")
    @Perm("health:docTemplate:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            docTemplateService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:docTemplate:enable", value = "文书模板-启用")
    @Perm("health:docTemplate:enable")
    public Result<Void> enable(@RequestBody List<Long> ids) {
        docTemplateService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .eq("is_sys", false)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:docTemplate:disable", value = "文书模板-禁用")
    @Perm("health:docTemplate:disable")
    public Result<Void> disable(@RequestBody List<Long> ids) {
        docTemplateService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .eq("is_sys", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "获取指定分类的文书模板")
    @GetMapping("/listByCode")
    @Perm
    public Result<List<DocTemplate>> listByCode(String code) {
        return Result.okData(docTemplateService.getListByCode(code));
    }

    @Operation(summary = "获取指定模板的模型说明")
    @GetMapping("/schema")
    @Perm("health:docTemplate")
    public Result<Object> schema(String code) {
        if (Str.isEmpty(code)) {
            return Result.fail("文书模板编号不能为空");
        }

        var docBeans = applicationContext.getBeansOfType(IDocTemplate.class);
        var templateOpt = docBeans.values().stream().filter(w -> w.getCode().equals(code)).findFirst();
        if (templateOpt.isEmpty()) {
            return Result.fail("不存在指定编号的文书模板：" + code);
        }

        Class<?> modelClass = templateOpt.get().getModel();
        var schema = extractSchema(modelClass);
        return Result.okData(schema);
    }

    @Operation(summary = "获取可用code")
    @GetMapping("/codes")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<String>>> getCodes() {
        var docBeans = applicationContext.getBeansOfType(IDocTemplate.class);
        return Result.okData(docBeans.values().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getCode(), w.getCode()))
                .toList());
    }

    @Operation(summary = "获取可用模板")
    @GetMapping("/dict")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<Long>>> getDict() {
        return Result.okData(docTemplateService.getList().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getId(), w.getName() + "(" + (w.getIsSys() ? "系统默认" : "用户定义") + ")"))
                .toList());
    }

    @Operation(summary = "通过模板生成PDF")
    @PostMapping("/pdfByTemplate")
    @Perm
    public ResponseEntity<?> pdfByTemplate(@RequestParam Long id, @RequestBody(required = false) Map<String, Object> data) {
        var entityOpt = docTemplateService.getById(id);
        if (entityOpt.isEmpty()) {
            return new ResponseEntity<>(Result.fail("文书模板不存在"), HttpStatus.NOT_FOUND);
        }

        DocTemplate template = entityOpt.get();
        var docBeans = applicationContext.getBeansOfType(IDocTemplate.class);
        var templateSpecOpt = docBeans.values().stream()
                .filter(w -> w.getCode().equals(template.getCode()))
                .findFirst();
        if (templateSpecOpt.isEmpty()) {
            return new ResponseEntity<>(Result.fail("未找到模板编号 '" + template.getCode() + "' 的定义"), HttpStatus.BAD_REQUEST);
        }
        Class<?> modelClass = templateSpecOpt.get().getModel();

        // 转换数据
        Object modelData;
        try {
            modelData = (data == null) ? null : objectMapper.convertValue(data, modelClass);
        } catch (Exception e) {
            log.error("数据格式与模板模型不匹配", e);
            return new ResponseEntity<>(Result.fail("数据格式与模板模型不匹配: " + e.getMessage()), HttpStatus.BAD_REQUEST);
        }

        byte[] pdfBytes = PdfUtil.htmlToPdf(template.getTemplate(), modelData);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", Str.urlEncode(template.getName()) + ".pdf");

        return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
    }

    @Operation(summary = "通过HTML生成PDF")
    @PostMapping("/pdfByHtml")
    @Perm("health:docTemplate")
    public ResponseEntity<?> pdfByHtml(@RequestBody String html) {
        if (Str.isEmpty(html)) {
            return new ResponseEntity<>(Result.fail("HTML内容不能为空"), HttpStatus.BAD_REQUEST);
        }

        String parseHtml = JSON.fromJson(html, String.class);
        byte[] pdfBytes = PdfUtil.htmlToPdf(parseHtml);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", "preview.pdf");

        return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
    }

    /**
     * 条件处理
     */
    private QueryWrapper<DocTemplate> applyQueryCondition(QueryWrapper<DocTemplate> wrapper, String type, String key, Boolean isSys) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            wrapper.like("name".equals(type), "name", keyTrim);
            wrapper.like("code".equals(type), "code", keyTrim);
        }
        wrapper.eq(isSys != null, "is_sys", isSys);
        return wrapper;
    }

    /**
     * 获取文本模板DTO的Schema描述，返回el-tree格式
     */
    private List<Map<String, Object>> extractSchema(Class<?> clazz) {
        if (clazz == null) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> fields = new ArrayList<>();
        for (Field field : clazz.getDeclaredFields()) {
            Schema fieldSchema = field.getAnnotation(Schema.class);
            if (fieldSchema != null) {
                Map<String, Object> fieldInfo = new LinkedHashMap<>();
                fieldInfo.put("name", field.getName());
                fieldInfo.put("type", getFieldTypeString(field));
                fieldInfo.put("description", fieldSchema.description());
                fieldInfo.put("label", field.getName() + " (" + getFieldTypeString(field) + "): " + fieldSchema.description());

                Class<?> fieldType = field.getType();
                if (Collection.class.isAssignableFrom(fieldType)) {
                    Type genericType = field.getGenericType();
                    if (genericType instanceof ParameterizedType pType) {
                        Type[] typeArguments = pType.getActualTypeArguments();
                        if (typeArguments.length > 0 && typeArguments[0] instanceof Class<?> itemType) {
                            if (isComplexType(itemType)) {
                                fieldInfo.put("children", extractSchema(itemType));
                            }
                        }
                    }
                } else if (isComplexType(fieldType)) {
                    fieldInfo.put("children", extractSchema(fieldType));
                }
                
                fields.add(fieldInfo);
            }
        }
        return fields;
    }

    private String getFieldTypeString(Field field) {
        Class<?> fieldType = field.getType();
        if (Collection.class.isAssignableFrom(fieldType)) {
            return "List";
        }
        return fieldType.getSimpleName();
    }

    private boolean isComplexType(Class<?> clazz) {
        return !clazz.isPrimitive() && clazz.getPackage() != null && !clazz.getPackage().getName().startsWith("java.");
    }
}