package module.sys.service;

import infra.core.common.Result;
import infra.core.text.Str;
import infra.sms.core.ISmsMessage;
import infra.sms.core.ISmsSender;
import infra.sms.core.ISmsService;
import infra.sms.impl.LogSmsSender;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.SmsConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 短信发送服务
 */
@Service
@Slf4j
@AllArgsConstructor
public class SmsService implements ISmsService {
    private final SmsConfigService smsConfigService;
    private final ISmsSender smsSender;

    @Transactional
    @Override
    public void register(List<ISmsMessage> messages) {
        if (messages == null || messages.isEmpty())
            return;

        int add = 0, update = 0;

        for (ISmsMessage message : messages) {
            var messageOpt = smsConfigService.getByMessageId(message.getMessageId());

            if (messageOpt.isPresent()) {
                var entity = messageOpt.get();
                // 检查是否有变化
                if (!message.getContentTemplate().equals(entity.getContentTemplate())) {
                    entity.setContentTemplate(message.getContentTemplate());
                    smsConfigService.update(entity);
                    update++;
                }
            } else {
                var entity = new SmsConfig();
                entity.setMessageId(message.getMessageId());
                entity.setContentTemplate(message.getContentTemplate());
                entity.setSort(message.getSort());
                smsConfigService.add(entity);
                add++;
            }
        }

        log.info("注册短信模板，新增{}条，更新{}条", add, update);
    }

    @Override
    public Result<String> send(String number, String messageId, String... params) {
        var messageOpt = smsConfigService.getByMessageId(messageId);
        if (messageOpt.isEmpty()) {
            return Result.fail("短信消息id数据不存在：" + messageId);
        }

        var message = messageOpt.get();
        if (message.getDisabled()) {
            return Result.fail("短信消息已禁用，暂不支持发送");
        }
        if (Str.isEmpty(message.getSupplierTemplateCode()) && !(smsSender instanceof LogSmsSender)) {
            return Result.fail("模板尚未配置平台方模板编号");
        }

        return smsSender.send(number, message.getSupplierTemplateCode(), params);
    }
}
