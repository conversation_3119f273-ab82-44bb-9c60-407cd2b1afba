<script setup lang="ts">
definePage({
  meta: {
    title: '检查管理',
    menu: true,
    order: -5,
    icon: 'ic:baseline-biotech',
  },
})

// 检查管理统计数据
const examStats = ref({
  totalExams: 186,
  pendingEntry: 45,
  inProgress: 89,
  completed: 52,
  criticalAlerts: 3,
  samplePending: 28
})

// 科室工作负荷数据
const deptWorkload = ref([
  { dept: '检验科', pending: 18, inProgress: 25, completed: 32, efficiency: 85 },
  { dept: '放射科', pending: 12, inProgress: 15, completed: 28, efficiency: 92 },
  { dept: '内科', pending: 8, inProgress: 22, completed: 35, efficiency: 88 },
  { dept: '超声科', pending: 7, inProgress: 18, completed: 25, efficiency: 90 },
  { dept: '外科', pending: 5, inProgress: 12, completed: 18, efficiency: 86 }
])

// 风险预警列表示例数据
const riskAlertList = ref([
  { alertId: 'AL202408001', examNo: 'EX202408001', name: '张三', type: '危急值预警', content: '血糖2.5mmol/L（参考范围3.9-6.1）', triggerTime: '2024-08-20 10:15', status: '未处理', level: 'high' },
  { alertId: 'AL202408002', examNo: 'EX202408002', name: '李四', type: '重大阳性预警', content: '肿瘤标志物CA199异常升高（80U/ml，参考<37）', triggerTime: '2024-08-20 09:45', status: '处理中', level: 'high' },
  { alertId: 'AL202408003', examNo: 'EX202408004', name: '赵六', type: '操作违规预警', content: '检验科未确认标本信息直接送检', triggerTime: '2024-08-20 09:30', status: '已处理', level: 'medium' }
])

// 功能模块数据
const modules = ref([
  { name: '检查录入', desc: '录入检查结果和数据', icon: 'ep:edit', path: 'entry', color: 'indigo' },
  { name: '标本管理', desc: '标本采集和跟踪', icon: 'cuida:sample-container-outline', path: 'specimen', color: 'purple' },
  { name: '危急值预警', desc: '危急值和异常预警', icon: 'ep:warning-filled', path: 'critical', color: 'red' },
  { name: '总检评价', desc: '总检医师评价审核', icon: 'ep:document-checked', path: 'evaluation', color: 'emerald' },
  { name: '医师排班', desc: '医师排班和调度', icon: 'ep:calendar', path: 'roster', color: 'blue' }
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
      <!-- 检查总数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
              {{ examStats.totalExams }}
            </div>
            <div class="text-sm text-indigo-700 dark:text-indigo-300 mt-1 font-medium">
              检查总数
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-600 dark:from-indigo-500 dark:to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:monitor" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 待录入 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-amber-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {{ examStats.pendingEntry }}
            </div>
            <div class="text-sm text-amber-700 dark:text-amber-300 mt-1 font-medium">
              待录入
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:edit" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 进行中 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ examStats.inProgress }}
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              进行中
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:loading" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 已完成 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ examStats.completed }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              已完成
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:check" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 危急预警 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-red-600 dark:text-red-400">
              {{ examStats.criticalAlerts }}
            </div>
            <div class="text-sm text-red-700 dark:text-red-300 mt-1 font-medium">
              危急预警
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 dark:from-red-500 dark:to-red-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:warning-filled" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 待处理标本 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {{ examStats.samplePending }}
            </div>
            <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
              待处理标本
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="cuida:sample-container-outline" class="text-white text-lg" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 左侧：功能模块和工作负荷 -->
      <div class="lg:col-span-2 space-y-4">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 科室工作负荷 -->
        <el-card>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium">科室工作负荷</span>
              <el-tag type="info" size="small">实时更新</el-tag>
            </div>
          </template>
          
          <div class="space-y-4">
            <div v-for="dept in deptWorkload" :key="dept.dept">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <span class="font-medium">{{ dept.dept }}</span>
                  <el-tag 
                    :type="dept.efficiency >= 90 ? 'success' : dept.efficiency >= 80 ? 'warning' : 'danger'" 
                    size="small"
                  >
                    {{ dept.efficiency }}%
                  </el-tag>
                </div>
                <span class="text-sm text-gray-500">效率</span>
              </div>
              
              <div class="flex items-center space-x-4 text-sm mb-2">
                <span class="text-warning">待处理: {{ dept.pending }}</span>
                <span class="text-info">进行中: {{ dept.inProgress }}</span>
                <span class="text-success">已完成: {{ dept.completed }}</span>
              </div>
              
              <el-progress 
                :percentage="dept.efficiency" 
                :status="dept.efficiency >= 90 ? 'success' : dept.efficiency >= 80 ? 'warning' : 'exception'"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：预警和排班概览 -->
      <div class="space-y-4">
        <!-- 危急值预警 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:warning-filled" class="text-danger" />
              <span class="font-medium">危急值预警</span>
              <el-tag type="danger" size="small">{{ riskAlertList.filter(r => r.status === '未处理').length }}</el-tag>
            </div>
          </template>
          
          <div class="space-y-3">
            <div v-for="alert in riskAlertList.slice(0, 3)" :key="alert.alertId" class="p-3 rounded-lg border border-gray-200">
              <div class="flex items-start justify-between mb-2">
                <div class="flex-1">
                  <p class="font-medium text-sm">{{ alert.name }} - {{ alert.type }}</p>
                  <p class="text-xs text-gray-500 mt-1">{{ alert.content }}</p>
                  <p class="text-xs text-gray-400 mt-1">{{ alert.triggerTime }}</p>
                </div>
                <el-tag 
                  :type="alert.level === 'high' ? 'danger' : 'warning'" 
                  size="small"
                >
                  {{ alert.level === 'high' ? '高危' : '中危' }}
                </el-tag>
              </div>
              <el-tag 
                :type="alert.status === '未处理' ? 'danger' : alert.status === '处理中' ? 'warning' : 'success'" 
                size="small"
              >
                {{ alert.status }}
              </el-tag>
            </div>
          </div>
        </el-card>



        <!-- 今日排班概览 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:calendar" class="text-primary" />
              <span class="font-medium">今日排班概览</span>
            </div>
          </template>
          
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span>检验科</span>
              <div class="text-sm">
                <span class="text-success">3人在岗</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">负荷85%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>放射科</span>
              <div class="text-sm">
                <span class="text-success">2人在岗</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">负荷92%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>内科</span>
              <div class="text-sm">
                <span class="text-success">4人在岗</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">负荷88%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>超声科</span>
              <div class="text-sm">
                <span class="text-success">2人在岗</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">负荷90%</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
