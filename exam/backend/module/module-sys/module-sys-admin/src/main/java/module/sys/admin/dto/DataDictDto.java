package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class DataDictDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        @NotBlank(message = "字典名称不能为空")
        private String name;
        @NotBlank(message = "字典编码不能为空")
        private String code;
        private String info;
        private Integer sort;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ValueEditDto {
        private Long dictId;
        @NotBlank(message = "字典值名称不能为空")
        private String name;
        private String code;
        private Integer sort;
        private Boolean disabled;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ValueItem {
        private Long id;
        private String name;
        private String code;
        private String py;
        private String info;
    }
} 