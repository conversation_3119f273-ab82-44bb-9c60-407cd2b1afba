package infra.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

import java.io.Serial;
import java.util.List;

/**
 * 树结构实体
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public abstract class TreeEntity extends EntityBase implements ITreeEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 父级ID
    @TableField
    private Long parentId;

    // 树路径
    @TableField
    private String path;

    // 排序值
    @TableField
    private Integer sort = 0;

    // 子级列表，用于树型结构，不保存数据库
    @TableField(exist = false)
    private List<? extends TreeEntity> children;
}
