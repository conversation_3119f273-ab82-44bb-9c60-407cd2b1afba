<script setup lang="ts">
definePage({
  meta: {
    title: '前台服务',
    menu: true,
    order: -6,
    icon: 'fluent:desk-16-regular',
  },
})

// 前台服务统计数据
const frontStats = ref({
  todayReception: 156,
  waitingCount: 28,
  todayBilling: 89,
  todaySettlement: 52,
  archiveCount: 1248,
  guidanceCount: 186
})

// 客户列表数据
const clientList = ref([
  { checkNo: '20240520001', name: '张三', type: '个检', status: 'checking', dept: '检验科', stayTime: '15分钟' },
  { checkNo: '20240520002', name: '李四', type: '团检(XX公司)', status: 'wait', dept: '-', stayTime: '-' },
  { checkNo: '20240520003', name: '王五', type: 'VIP个检', status: 'warning', dept: '内科', stayTime: '35分钟' },
  { checkNo: '20240520004', name: '赵六', type: '团检(XX学校)', status: 'done', dept: '-', stayTime: '-' }
])

// 功能模块数据
const modules = ref([
  { name: '客户建档', desc: '客户信息录入和管理', icon: 'ep:user-filled', path: 'archive', color: 'cyan' },
  { name: '现场开单', desc: '现场体检开单服务', icon: 'ep:document', path: 'billing', color: 'blue' },
  { name: '报到确认', desc: '客户报到和确认', icon: 'ep:check', path: 'reception', color: 'emerald' },
  { name: '预约排期', desc: '体检预约和排期', icon: 'ep:calendar', path: 'schedule', color: 'purple' },
  { name: '指引服务', desc: '体检指引和导诊', icon: 'ep:guide', path: 'guidance', color: 'orange' },
  { name: '费用结算', desc: '个检和团检结算', icon: 'ep:money', path: 'settlement', color: 'pink' }
])

// 排队状况数据
const queueStatus = ref([
  { dept: '内科', waiting: 8, avgWaitTime: 12 },
  { dept: '检验科', waiting: 15, avgWaitTime: 8 },
  { dept: '放射科', waiting: 5, avgWaitTime: 15 },
  { dept: '超声科', waiting: 12, avgWaitTime: 10 },
  { dept: '心电图', waiting: 3, avgWaitTime: 5 }
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
      <!-- 今日接待 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
              {{ frontStats.todayReception }}
            </div>
            <div class="text-sm text-cyan-700 dark:text-cyan-300 mt-1 font-medium">
              今日接待
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-cyan-400 to-cyan-600 dark:from-cyan-500 dark:to-cyan-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:user-filled" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 等候人数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-amber-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-amber-600 dark:text-amber-400">
              {{ frontStats.waitingCount }}
            </div>
            <div class="text-sm text-amber-700 dark:text-amber-300 mt-1 font-medium">
              等候人数
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:clock" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 今日开单 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {{ frontStats.todayBilling }}
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              今日开单
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:document" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 今日结算 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ frontStats.todaySettlement }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              今日结算
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:money" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 档案总数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {{ frontStats.archiveCount }}
            </div>
            <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
              档案总数
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:folder" class="text-white text-lg" />
          </div>
        </div>
      </el-card>

      <!-- 指引服务 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
              {{ frontStats.guidanceCount }}
            </div>
            <div class="text-sm text-orange-700 dark:text-orange-300 mt-1 font-medium">
              指引服务
            </div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 dark:from-orange-500 dark:to-orange-700 rounded-xl flex items-center justify-center shadow-lg">
            <icon icon="ep:guide" class="text-white text-lg" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- 左侧：功能模块和客户状态 -->
      <div class="lg:col-span-2 space-y-4">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 当前客户状态 -->
        <el-card>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium">当前客户状态</span>
              <el-tag type="info" size="small">实时更新</el-tag>
            </div>
          </template>
          
          <div class="space-y-3">
            <div v-for="client in clientList" :key="client.checkNo" class="flex items-center justify-between p-3 rounded-lg border border-gray-200">
              <div class="flex items-center space-x-3">
                <div>
                  <p class="font-medium">{{ client.name }}</p>
                  <p class="text-sm text-gray-500">{{ client.checkNo }} - {{ client.type }}</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">{{ client.dept }}</span>
                <el-tag 
                  :type="client.status === 'checking' ? 'primary' : client.status === 'wait' ? 'warning' : client.status === 'warning' ? 'danger' : 'success'" 
                  size="small"
                >
                  {{ client.status === 'checking' ? '检查中' : client.status === 'wait' ? '等候' : client.status === 'warning' ? '超时' : '完成' }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：排队状况和工作概览 -->
      <div class="space-y-4">
        <!-- 各科室排队状况 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:timer" class="text-warning" />
              <span class="font-medium">各科室排队状况</span>
            </div>
          </template>
          
          <div class="space-y-3">
            <div v-for="queue in queueStatus" :key="queue.dept" class="flex items-center justify-between">
              <div>
                <p class="font-medium">{{ queue.dept }}</p>
                <p class="text-sm text-gray-500">平均等待 {{ queue.avgWaitTime }} 分钟</p>
              </div>
              <div class="text-right">
                <el-tag 
                  :type="queue.waiting <= 5 ? 'success' : queue.waiting <= 10 ? 'warning' : 'danger'" 
                  size="small"
                >
                  {{ queue.waiting }}人
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>



        <!-- 今日工作概览 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:data-analysis" class="text-primary" />
              <span class="font-medium">今日工作概览</span>
            </div>
          </template>
          
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span>个检接待</span>
              <div class="text-sm">
                <span class="text-primary font-medium">128人</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">82%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>团检接待</span>
              <div class="text-sm">
                <span class="text-success font-medium">28人</span>
                <span class="text-gray-400 mx-1">/</span>
                <span class="text-gray-600">18%</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>费用收取</span>
              <div class="text-sm">
                <span class="text-warning font-medium">¥45,680</span>
              </div>
            </div>
            <div class="flex justify-between items-center">
              <span>平均等待</span>
              <div class="text-sm">
                <span class="text-info font-medium">9.5分钟</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 服务质量评价 -->
        <el-card>
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:star-filled" class="text-warning" />
              <span class="font-medium">服务质量评价</span>
            </div>
          </template>
          
          <div class="text-center">
            <div class="text-3xl font-bold text-warning mb-2">4.8</div>
            <div class="text-sm text-gray-500 mb-3">今日平均评分</div>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span>非常满意</span>
                <span class="text-success">85%</span>
              </div>
              <div class="flex justify-between">
                <span>满意</span>
                <span class="text-primary">12%</span>
              </div>
              <div class="flex justify-between">
                <span>一般</span>
                <span class="text-warning">3%</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
