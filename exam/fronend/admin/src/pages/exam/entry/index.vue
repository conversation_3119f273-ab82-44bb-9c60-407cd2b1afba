<script setup lang="ts">
definePage({
  meta: {
    title: '检查录入',
    menu: true,
    order: 1,
    icon: 'guidance:entry',
  },
})
</script>

<template>
<p>1. 多形式结果录入：支持检查科室人员录入文字（如症状描述）、数值（如血压、血糖）、图像（如超声、CT 影像）类检查结果，适配临床与辅助科室操作；</p>
<p>2. 实时校验：录入时自动比对参考范围，异常值标红提醒并强制填写异常说明；设备上传数据（如心电图）后，校验格式完整性（如关键波段是否缺失），不满足则无法提交；</p>
<p>3. 数据整合：按体检号关联同一客户所有检查项目结果，支持医生快速调阅，确保结果录入后可直接用于总检评估；</p>
<p>4. 录入追溯：记录结果录入人、录入时间，允许总检前修正错误数据并留存修改日志，保障数据可追溯</p>

检中阶段 “检查结果录入（支持多形式录入，系统自动格式校验与逻辑判断）”；检查科室 “检查数据实时校验（异常值标红、设备数据格式校验）”
</template>
