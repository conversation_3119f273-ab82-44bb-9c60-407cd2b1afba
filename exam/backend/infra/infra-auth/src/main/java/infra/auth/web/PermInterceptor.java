package infra.auth.web;

import infra.auth.annotation.Perm;
import infra.auth.core.IUser;
import infra.auth.exception.AuthException;
import infra.auth.exception.PermException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;

/**
 * 权限拦截器
 * 基于注解的权限控制
 */
@Slf4j
public class PermInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {

        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }
        Method method = handlerMethod.getMethod();

        // 检查方法级别的注解
        Perm methodAnnotation = AnnotationUtils.findAnnotation(method, Perm.class);

        // 如果方法没有注解，检查类级别的注解
        Perm classAnnotation = null;
        if (methodAnnotation == null) {
            Class<?> beanType = handlerMethod.getBeanType();
            classAnnotation = AnnotationUtils.findAnnotation(beanType, Perm.class);
        }

        Perm annotation = methodAnnotation != null ? methodAnnotation : classAnnotation;

        // 如果没有权限注解，直接通过
        if (annotation == null) {
            return true;
        }

        return checkPerm(annotation);
    }

    /**
     * 检查权限
     */
    private boolean checkPerm(Perm perm) {
        IUser currentUser = UserContext.getCurrentUser();

        if (currentUser == null) {
            throw new AuthException();
        }

        if (!currentUser.hasPerm(perm.value())) {
            throw new PermException();
        }

        return true;
    }
}
