package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 体检项目与项目字典关联
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_exam_project_dict")
public class ExamProjectDict extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 体检项目ID
    private Long examProjectId;

    // 项目字典ID
    private Long projectDictId;

    // 备注
    private String remark;
}