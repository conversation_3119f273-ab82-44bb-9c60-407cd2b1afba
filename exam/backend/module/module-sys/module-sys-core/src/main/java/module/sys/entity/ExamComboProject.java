package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 体检套餐与体检项目关联
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_exam_combo_project")
public class ExamComboProject extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 套餐ID
    private Long examComboId;

    // 体检项目ID
    private Long examProjectId;
}