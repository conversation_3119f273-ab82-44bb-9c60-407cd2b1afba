<script setup lang="ts">
definePage({
  meta: {
    title: '数据挖掘',
    menu: true,
    order: 4,
    icon: 'eos-icons:data-mining',
  },
})
</script>

<template>
<p>1. 群体分析：统计某年龄段高血压患病率、区域血脂异常分布，生成群体健康报告，明确干预方向；</p>
<p>2. 个体画像：比对客户历年体检数据，生成健康变化曲线，标记风险点（如血糖持续升高）；</p>
<p>3. 风险分层：按健康数据将客户划分为低 / 中 / 高风险等级，为随访提供分层依据；</p>
<p>4. 可视化呈现：用图表展示群体疾病分布、个体指标变化，直观呈现分析结果</p>

健康管理阶段 “健康数据深度挖掘（群体趋势分析、个体健康曲线生成）”；决策支持 “运营数据可视化呈现（图表展示数据）”
</template>
