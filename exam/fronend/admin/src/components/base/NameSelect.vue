<script setup lang="ts">
interface NameSelectProps {
  /** id字段 */
  idFild?: string
  /** 填充name的字段 */
  nameField?: string
  /** 选项数据 */
  options?: any[]
}

const props = withDefaults(defineProps<NameSelectProps>(), {
  idFild: 'id',
  nameField: 'name',
})
const attrs = useAttrs()

const modelValue = defineModel<number>()
const modelName = defineModel<string>('name')

// 处理name
function handleChange(value: any) {
  const node = props.options?.find(o => o[props.idFild] === value)
  modelName.value = node?.[props.nameField] || '';
  (attrs.onChange as ((value: any) => void) | undefined)?.(value)
}
</script>

<template>
  <el-select v-model="modelValue" v-bind="attrs" @change="handleChange">
    <el-option v-for="item in props.options" :key="item[props.idFild]" :value="item[props.idFild]" :label="item[props.nameField]" />
  </el-select>
</template>
