package app.admin;

import infra.core.common.Result;
import infra.core.text.JSON;
import infra.protect.annotation.RateLimit;
import infra.report.code.CodeRenderOptions;
import infra.report.code.CodeUtil;
import infra.report.pdf.PdfRenderOptions;
import infra.report.pdf.PdfUtil;
import infra.sse.notice.core.INoticeService;
import infra.sse.notice.model.NoticeMessage;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.doc.DemoDocModelDto;
import module.sys.doc.IDocTemplate;
import module.sys.entity.DocTemplate;
import module.sys.service.BarCodeUseService;
import module.sys.service.DataDictValueService;
import module.sys.service.DocTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestController {
    private final INoticeService noticeService;
    private final DataDictValueService dataDictValueService;
    private final BarCodeUseService barCodeUseService;
    private final DocTemplateService docTemplateService;
    private final ApplicationContext applicationContext;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @GetMapping("/qrcode")
    public void generateQRCode(
            @RequestParam String data,
            HttpServletResponse response
    ) throws IOException {
        var config= CodeRenderOptions.defaultQRCode();
        BufferedImage logoImage = ImageIO.read(URI.create("https://p3.ssl.qhimg.com/t110b9a9301a8065f25c0e17ce2.png").toURL());
        config.autoConfigLogo(logoImage);
        CodeUtil.generateQRCodeToResponse(data, config, response);
    }

    @GetMapping("/api")
    @RateLimit(count = 2, window = 10)
    public String getApi() {
        noticeService.noticeUsers(List.of(1L), NoticeMessage.builder()
                        .title("测试通知")
                        .content("通知，今晚看电影。老板请客。请有需要的同时找HR报名。")
                .build());
        return  "";
    }


    @GetMapping("/pdf")
    public ResponseEntity<?> pdf() {
//        return JSON.toJson(new DemoDocModelDto("test", "time", List.of(
//                new DemoDocModelDto.UserDto("11", 18, "work"))));
        var entityOpt = docTemplateService.getById(2L);

        DocTemplate template = entityOpt.get();
        var docBeans = applicationContext.getBeansOfType(IDocTemplate.class);
        var templateSpecOpt = docBeans.values().stream()
                .filter(w -> w.getCode().equals(template.getCode()))
                .findFirst();
        if (templateSpecOpt.isEmpty()) {
            return new ResponseEntity<>(Result.fail("未找到模板编号 '" + template.getCode() + "' 的定义"), HttpStatus.BAD_REQUEST);
        }
        Class<?> modelClass = templateSpecOpt.get().getModel();

        // 转换数据
        Object modelData;
        try {
            modelData = new DemoDocModelDto("test", "time", List.of(
                    new DemoDocModelDto.UserDto("11", 18, "work")
            ));
        } catch (Exception e) {
            log.error("数据格式与模板模型不匹配", e);
            return new ResponseEntity<>(Result.fail("数据格式与模板模型不匹配: " + e.getMessage()), HttpStatus.BAD_REQUEST);
        }

        byte[] pdfBytes = PdfUtil.htmlToPdf(template.getTemplate(), modelData);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("inline", infra.core.text.Str.urlEncode(template.getName()) + ".pdf");

        return new ResponseEntity<>(pdfBytes, headers, HttpStatus.OK);
    }
}
