package infra.audit.core;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditLog {
    /**
     * 模块
     */
    String module() default "";

    /**
     * 操作编码(可以考虑同权限编号)
     */
    String code() default "";

    /**
     * 进行的操作
     */
    String value();
}
