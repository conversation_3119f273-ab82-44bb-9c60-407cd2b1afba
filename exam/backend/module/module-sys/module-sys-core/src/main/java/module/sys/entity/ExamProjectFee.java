package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 体检项目与收费项目关联
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_exam_project_fee")
public class ExamProjectFee extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 体检项目ID
    private Long examProjectId;

    // 收费项目ID
    private Long feeProjectId;

    // 数量
    private Integer quantity;

    // 备注
    private String remark;
}