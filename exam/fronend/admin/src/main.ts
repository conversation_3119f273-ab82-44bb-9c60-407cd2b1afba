import { ElLoading } from 'element-plus'
import { registerComponents } from '~/components'
import { useAuth } from '~/composables'
import { useDirectives } from '~/directives'
import { initConfig, setupRouter } from '~/utils'
import App from './App.vue'

import '~/styles/index.scss'
import 'uno.css'

const loadding = ElLoading.service({
  lock: true,
})
const { hasPerm } = useAuth()

initConfig().then((config) => {
  const app = createApp(App)

  // 指令及组件
  useDirectives(app)
  registerComponents(app)

  // 全局属性
  app.config.globalProperties.$hasPerm = hasPerm

  // 路由
  const router = setupRouter(config)
  app.use(router)

  loadding.close()

  app.mount('#app')
  document.title = config.appName
})
