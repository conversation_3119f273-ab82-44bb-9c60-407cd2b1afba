package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * 样本类型Dto
 */
public class SampleTypeDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 样本类型名称
        @NotBlank(message = "病样本类型名称不能为空")
        private String name;
        // 样本类型缩写
        private String shortName;
        // 样本类型代码
        private String code;
        // 样本采集科室id
        private Long deptId;
        // 排序值
        private Integer sort;
        // 禁用
        private Boolean disabled;
    }    
}