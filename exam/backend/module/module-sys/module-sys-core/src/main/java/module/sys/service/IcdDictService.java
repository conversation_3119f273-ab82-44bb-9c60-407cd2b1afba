package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.IcdDict;
import module.sys.mapper.IcdMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.function.Function;

/**
 * ICD编码服务
 */
@Slf4j
@Service
public class IcdDictService extends ServiceBase<IcdMapper, IcdDict> {
    @Override
    protected void beforeSave(IcdDict entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByNo(entity.getIcdNo(), isUpdate ? entity.getId() : null)) {
            throw new BizException("ICD编号已存在");
        }

        setPinyin(entity);
    }

    @Override
    protected Function<QueryWrapper<IcdDict>, QueryWrapper<IcdDict>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<IcdDict>, QueryWrapper<IcdDict>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    /**
     * 检查Icd编号是否已存在
     */
    public boolean existsByNo(String code, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(w -> {
            w.eq("icd_no", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 根据Icd编号获取
     */
    public Optional<IcdDict> getByNo(String no) {
        return getFirst(w -> w.eq("icd_no", no));
    }

    /**
     * 设置拼音
     */
    private void setPinyin(IcdDict value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
