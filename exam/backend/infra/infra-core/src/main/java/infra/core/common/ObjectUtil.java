package infra.core.common;

import infra.core.text.JSON;
import org.springframework.beans.BeanUtils;

import java.util.function.Consumer;

/**
 * 对象工具
 */
public final class ObjectUtil {
    /**
     * 复制对象属性值
     *
     * @param source 源对象
     * @param target 目标对象
     * @param <T>    目标对象类型
     * @return 目标对象
     */
    public static <T> T copyTo(Object source, T target) {
        if (source == null || target == null)
            return target;

        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 复制对象属性值
     *
     * @param source           数据源对象，其属性将被复制
     * @param target           目标对象，将复制源对象的属性值到此对象
     * @param ignoreProperties 可变参数，指定在复制过程中忽略的属性名
     * @param <T>              目标对象的类型
     * @return 返回填充了源对象属性值的目标对象
     */
    public static <T> T copyTo(Object source, T target, String... ignoreProperties) {
        if (source == null || target == null)
            return target;

        BeanUtils.copyProperties(source, target, ignoreProperties);
        return target;
    }

    /**
     * 复制对象属性值
     *
     * @param source   源对象
     * @param target   目标对象
     * @param consumer 复制后的操作
     * @param <T>      目标对象类型
     * @return 目标对象
     */
    public static <T> T copyTo(Object source, T target, Consumer<T> consumer) {
        if (source == null || target == null)
            return target;

        BeanUtils.copyProperties(source, target);
        consumer.accept(target);
        return target;
    }


    /**
     * 克隆对象
     *
     * @param source 源对象
     * @param clazz  目标对象类型
     * @param <T>    目标对象类型
     * @return 目标对象
     */
    public static <T> T clone(T source, Class<T> clazz) {
        if (source == null) {
            return null;
        }

        byte[] jsonBytes = JSON.toBytes(source);
        return JSON.fromBytes(jsonBytes, clazz);
    }

    /**
     * 创建对象实例
     *
     * @param clazz 对象类型
     * @param <T>   对象类型
     * @return 对象实例
     */
    public static <T> T newInstance(Class<T> clazz) {
        return BeanUtils.instantiateClass(clazz);
        // return clazz.getDeclaredConstructor().newInstance();
    }
}
