package infra.cache.config;

import infra.cache.core.ICache;
import infra.cache.impl.LocalCache;
import infra.cache.impl.NoneCache;
import infra.cache.impl.RedisCache;
import infra.cache.impl.TwoLevelCache;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.UUID;

/**
 * 缓存配置
 */
@AutoConfiguration
@EnableConfigurationProperties(CacheConfigProperties.class)
@EnableCaching
@Slf4j
public class CacheAutoConfiguration {
    /**
     * 配置本地缓存
     */
    @Bean
    @ConditionalOnProperty(name = "app.cache.provider", havingValue = "local")
    @ConditionalOnMissingBean(ICache.class)
    public ICache infraLocalCache(CacheConfigProperties config) {
        log.info("[配置] 已配置Local缓存");
        return new LocalCache(config.getLocal().getMaxSize());
    }

    /**
     * 配置Redis缓存
     */
    @Bean
    @ConditionalOnProperty(name = "app.cache.provider", havingValue = "redis")
    @ConditionalOnMissingBean(ICache.class)
    public ICache infraRedisCache(RedissonClient redissonClient) {
        log.info("[配置] 已配置Redis缓存");
        return new RedisCache(redissonClient);
    }

    /**
     * 配置空缓存
     */
    @Bean
    @ConditionalOnProperty(name = "app.cache.provider", havingValue = "none")
    @ConditionalOnMissingBean(ICache.class)
    public ICache infraNoneCache() {
        log.info("[配置] 已配置空缓存");
        return new NoneCache();
    }

    /**
     * 配置二级缓存
     */
    @Bean
    @ConditionalOnProperty(name = "app.cache.provider", havingValue = "two-level")
    @ConditionalOnMissingBean(ICache.class)
    public ICache infraTwoLevelCache(CacheConfigProperties config, RedissonClient redissonClient) {
        // 创建本地缓存实例
        LocalCache localCache = new LocalCache(config.getTwoLevel().getLocalMaxSize());
        // 创建Redis缓存实例
        RedisCache redisCache = new RedisCache(redissonClient);

        String instanceId = generateInstanceId();
        // 创建二级缓存实例
        TwoLevelCache twoLevelCache = new TwoLevelCache(localCache, redisCache, redissonClient, instanceId, config.getTwoLevel().getInvalidateChannelPrefix());
        log.info("[配置] 已配置二级缓存，本地缓存容量: {}, 实例ID: {}", config.getTwoLevel().getLocalMaxSize(), instanceId);

        return twoLevelCache;
    }

    @Primary
    @Bean
    @ConditionalOnBean(ICache.class)
    public CacheManager infraCacheManager(ICache cache) {
        log.info("[配置] 已配置CacheManager");
        return new infra.cache.core.CacheManager(cache);
    }

    /**
     * 生成缓存实例ID
     *
     * @return 实例ID
     */
    private String generateInstanceId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
}
