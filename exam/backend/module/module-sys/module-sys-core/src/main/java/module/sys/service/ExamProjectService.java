package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Chinese;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ExamProject;
import module.sys.mapper.ExamProjectMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Function;

/**
 * 体检项目服务
 */
@Slf4j
@Service
public class ExamProjectService extends ServiceBase<ExamProjectMapper, ExamProject> {
    @Override
    protected Function<QueryWrapper<ExamProject>, QueryWrapper<ExamProject>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected void beforeSave(ExamProject entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);
        setPinyin(entity);
    }

    /**
     * 根据科室获取体检项目列表
     */
    public List<ExamProject> getListByDept(Long deptId) {
        return getList(w -> w.eq("dept_id", deptId));
    }

    /**
     * 设置拼音
     */
    private void setPinyin(ExamProject examProject) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(examProject.getName());
        examProject.setPy(pinyin.py());
    }
}