package infra.auth.config;

import infra.auth.core.*;
import infra.auth.core.HeaderDeptGetter;
import infra.auth.core.HeaderTokenGetter;
import infra.auth.web.PermInterceptor;
import infra.auth.web.UserContextFilter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.*;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 认证自动配置
 */
@Slf4j
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@EnableConfigurationProperties(AuthConfigProperties.class)
public class AuthAutoConfiguration {
//    @Bean
//    public AuthConfigProperties infraAuthConfigProperties(AuthConfigProperties config) {
//        return config;
//    }

    /**
     * 默认Token获取器
     */
    @Bean
    @ConditionalOnMissingBean(ITokenGetter.class)
    public ITokenGetter infraTokenGetter(AuthConfigProperties config) {
        log.info("[配置] 默认Token获取器 - HeaderTokenGetter");
        return new HeaderTokenGetter(config.getTokenFromKey());
    }

    /**
     * 默认部门获取器
     */
    @Bean
    @ConditionalOnMissingBean(IDeptGetter.class)
    public IDeptGetter infraDeptGetter(AuthConfigProperties config) {
        log.info("[配置] 默认部门获取器 - HeaderDeptGetter");
        return new HeaderDeptGetter(config.getDeptFromKey());
    }

    /**
     * Redis Token管理器
     * 当配置了redisTokenPrefix时自动启用
     */
    @Bean
    @ConditionalOnProperty(name = "app.auth.redis-token-prefix")
    @ConditionalOnMissingBean(ITokenManager.class)
    public ITokenManager infraRedisTokenManager(AuthConfigProperties config, RedissonClient redissonClient) {
        log.info("[配置] Token管理器-RedisTokenManager, prefix: {}", config.getRedisTokenPrefix());
        return new RedisTokenManager(config, redissonClient);
    }

    /**
     * 认证管理器
     */
    @Bean
    @ConditionalOnBean(IUserService.class)
    public AuthManager infraAuthManager(AuthConfigProperties config, IUserService userService, ITokenGetter tokenGetter, IDeptGetter deptGetter, @Nullable ITokenManager tokenManager) {
        log.info("[配置] 认证管理器 - AuthManager");
        return new AuthManager(config, userService, tokenGetter, deptGetter, tokenManager);
    }

    /**
     * 用户上下文过滤器
     * 设置最高优先级，确保在其他过滤器之前执行
     */
    @Order(-100)
    @Bean
    @ConditionalOnBean(AuthManager.class)
    @ConditionalOnMissingBean(name = "userContextFilter")
    public OncePerRequestFilter infraUserContextFilter(AuthManager authManager) {
        log.info("[配置] 用户上下文Filter");
        return new UserContextFilter(authManager);
    }

    /**
     * 权限拦截器
     */
    @Bean
    @ConditionalOnMissingBean(PermInterceptor.class)
    public PermInterceptor infraPermInterceptor() {
        log.info("[配置] 权限拦截器 - PermInterceptor");
        return new PermInterceptor();
    }

    /**
     * 权限拦截器配置器
     * 用于自动注册权限拦截器到WebMvc
     */
    @Bean
    @ConditionalOnBean(PermInterceptor.class)
    public WebMvcConfigurer infraPermInterceptorConfigurer(PermInterceptor permInterceptor) {
        log.info("[配置] 将权限拦截器注册到SpringMvc");
        return new WebMvcConfigurer() {
            public void addInterceptors(@NonNull InterceptorRegistry registry) {
                registry.addInterceptor(permInterceptor).addPathPatterns("/**").excludePathPatterns("/error", "/health", "/actuator/**", "/favicon.ico");
            }
        };
    }

    /**
     * 提供一个默认的密码加密器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
}
