package basic.configure.web;

import infra.auth.exception.AuthException;
import infra.auth.exception.PermException;
import infra.core.common.Result;
import infra.core.exception.BizException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.io.IOException;
import java.sql.SQLIntegrityConstraintViolationException;

/**
 * 全局异常处理
 */
@ResponseBody
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Object> handleBindException(BindException ex) {
        var fieldError = ex.getBindingResult().getFieldError();
        String message;
        if (fieldError != null) {
            message = fieldError.getDefaultMessage();
        } else {
            message = ex.getMessage();
        }
        return createErrorResponse(HttpStatus.BAD_REQUEST, message);
    }

    /**
     * 参数校验异常处理
     * 在业务层抛出异常时，会抛出ConstraintViolationException
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException ex) {
        var first = ex.getConstraintViolations().stream().findFirst();
        String message = first.map(ConstraintViolation::getMessage)
                .orElse(ex.getMessage());
        return createErrorResponse(HttpStatus.BAD_REQUEST, message);
    }

    /**
     * 认证异常处理
     */
    @ExceptionHandler(AuthException.class)
    public ResponseEntity<Object> handleAuthException(AuthException ex) {
        return createErrorResponse(HttpStatus.UNAUTHORIZED, ex.getMessage());
    }

    /**
     * 权限异常处理
     */
    @ExceptionHandler(PermException.class)
    public ResponseEntity<Object> handlePermException(PermException ex) {
        return createErrorResponse(HttpStatus.FORBIDDEN, ex.getMessage());
    }

    /**
     * 业务异常处理
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<Object> handleBizException(BizException ex) {
        return createErrorResponse(HttpStatus.BAD_REQUEST, ex.getMessage());
    }

    /**
     * 404异常处理
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex) {
        return createErrorResponse(HttpStatus.NOT_FOUND, "请求的资源不存在");
    }

    /**
     * 404异常处理
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<Object> handleNoResourceFoundException(NoResourceFoundException ex) {
        return createErrorResponse(HttpStatus.NOT_FOUND, "请求的资源不存在");
    }

    /**
     * 请求方法不支持异常处理
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<Object> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        return createErrorResponse(HttpStatus.METHOD_NOT_ALLOWED, "不支持的请求方法: " + ex.getMethod());
    }

    /**
     * 媒体类型不支持异常处理
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<Object> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException ex) {
        return createErrorResponse(HttpStatus.UNSUPPORTED_MEDIA_TYPE, "不支持的媒体类型");
    }

    /**
     * 数据库约束异常处理
     */
    @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
    public ResponseEntity<Object> handleSQLIntegrityConstraintViolationException(SQLIntegrityConstraintViolationException ex) {
        log.warn("SQL约束冲突：{}", ex.getMessage(), ex);
        return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "存储数据冲突，关键数据已被使用");
    }

    /**
     * 数据访问异常处理，不应该向客户端抛message
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<Object> handleDataAccessException(Exception ex) {
        log.warn("数据处理异常：{}", ex.getMessage(), ex);
        return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "服务器处理数据时发生错误");
    }

    @ExceptionHandler(IOException.class)
    public void handleSseException(IOException e) {
        // 过滤SSE连接断开的常见异常
        if ("断开的管道".equals(e.getMessage()) || "Connection reset by peer".equals(e.getMessage())) {
            return;
        }
        // 其他异常正常打印
        log.warn("发生未处理的IO异常", e);
    }

    /**
     * 其它全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleException(Exception ex) {
        log.warn("请求发生异常：{}", ex.getMessage(), ex);
        return createErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage());
    }

    /**
     * 根据请求类型创建合适的错误响应
     */
    private ResponseEntity<Object> createErrorResponse(HttpStatus status, String message) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String accept = request.getHeader("Accept");

            // 处理纯文本请求
            if (accept != null && (accept.contains("text/plain") || accept.contains("text/html"))) {
                return ResponseEntity.status(status)
                        .contentType(MediaType.TEXT_PLAIN)
                        .body(message);
            }

            // 处理SSE请求
            if (accept != null && accept.contains("text/event-stream")) {
                return ResponseEntity.status(status)
                        .contentType(MediaType.valueOf("text/event-stream"))
                        .body("event: error\ndata: " + message + "\n\n");
            }
        }

        // 默认返回JSON格式
        return ResponseEntity.status(status)
                .contentType(MediaType.APPLICATION_JSON)
                .body(Result.fail(status.value(), message));
    }
}
