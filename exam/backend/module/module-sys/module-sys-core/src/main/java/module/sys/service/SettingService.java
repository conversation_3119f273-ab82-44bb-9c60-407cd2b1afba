package module.sys.service;

import infra.cache.core.ICache;
import infra.core.common.ObjectUtil;
import infra.core.exception.ConfigException;
import infra.core.text.JSON;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Setting;
import module.sys.mapper.SettingMapper;
import module.sys.model.AppSetting;
import module.sys.model.HospitalInfo;
import module.sys.model.SafeSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 配置服务
 */
@Slf4j
@Service
public class SettingService extends ServiceBase<SettingMapper, Setting> {
    @Autowired
    private ICache cache;

    private static final String APP_SETTING_CODE = "app";
    private static final String SAFE_SETTING_CODE = "safe";
    private static final String HOSPITAL_SETTING_CODE = "hospital";
    private static final String CACHE_PREFIX = "sys:setting:";

    /**
     * 根据编码获取设置
     */
    public Optional<Setting> getByCode(String code) {
        return getFirst(w -> w.eq("code", code));
    }

    /**
     * 获取指定类型的设置
     */
    public <T> T getSetting(String code, Class<T> modelClass) {
        return cache.getOrSet(CACHE_PREFIX + code, modelClass, () -> {
            Optional<Setting> settingOpt = getByCode(code);

            try {
                if (settingOpt.isPresent()) {
                    return JSON.fromJson(settingOpt.get().getValue(), modelClass);
                } else {
                    return ObjectUtil.newInstance(modelClass);
                }
            } catch (Exception e) {
                log.error("获取配置错误：{}", code, e);
                throw new ConfigException("获取配置错误：" + code);
            }
        });
    }

    /**
     * 更新设置
     */
    public void updateSetting(String code, String name, Object data) {
        Optional<Setting> settingOpt = getByCode(code);
        Setting setting;

        if (settingOpt.isPresent()) {
            setting = settingOpt.get();
        } else {
            setting = new Setting();
            setting.setCode(code);
        }
        setting.setName(name);
        setting.setValue(JSON.toJson(data));

        if (setting.getId() != null && setting.getId() > 0) {
            update(setting);
        } else {
            add(setting);
        }
        cache.delete(CACHE_PREFIX + code);
    }

    /**
     * 获取应用设置
     */
    public AppSetting getAppSetting() {
        return getSetting(APP_SETTING_CODE, AppSetting.class);
    }

    /**
     * 更新应用设置
     */
    public void updateAppSetting(AppSetting data) {
        updateSetting(APP_SETTING_CODE, "应用设置", data);
    }

    /**
     * 获取安全设置
     */
    public SafeSetting getSafeSetting() {
        return getSetting(SAFE_SETTING_CODE, SafeSetting.class);
    }

    /**
     * 更新安全设置
     */
    public void updateSafeSetting(SafeSetting data) {
        updateSetting(SAFE_SETTING_CODE, "安全设置", data);
    }

    /**
     * 获取医院信息
     */
    public HospitalInfo getHospitalInfo() {
        return getSetting(HOSPITAL_SETTING_CODE, HospitalInfo.class);
    }

    /**
     * 更新医院信息
     */
    public void updateHospitalInfo(HospitalInfo data) {
        updateSetting(HOSPITAL_SETTING_CODE, "医院信息", data);
    }
}
