package module.sys.service;

import infra.core.exception.BizException;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ExamProjectDict;
import module.sys.mapper.ExamProjectDictMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 体检项目与项目字典关联服务
 */
@Slf4j
@Service
public class ExamProjectDictService extends ServiceBase<ExamProjectDictMapper, ExamProjectDict> {
    @Override
    protected void beforeSave(ExamProjectDict entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (exists(w ->
                w.eq("exam_project_id", entity.getExamProjectId())
                        .eq("project_dict_id", entity.getProjectDictId())
                        .ne(isUpdate, "id", entity.getId()))) {
            throw new BizException("该关联数据已存在");
        }
    }

    /**
     * 获取体检项目的关联项目
     */
    public List<ExamProjectDict> getListByProject(Long id) {
        return getListByProject(id, FilterOptions.DEFAULTS);
    }

    /**
     * 获取体检项目的关联项目
     */
    public List<ExamProjectDict> getListByProject(Long id, FilterOptions filterOptions) {
        return getList(w -> w.eq("exam_project_id", id), filterOptions);
    }
}