package module.sys.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SafeSetting implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 密码尝试次数
     */
    private int passwordTryMax = 5;
    /**
     * 密码错误超限锁定时间(分钟)
     */
    private int passwordTryLock = 30;
    /**
     * 验证码有效时长(分钟)
     */
    private int validCodeExpiry = 10;
    /**
     * 验证码最大尝试错误次数
     */
    private int validCodeTryMax = 3;
}