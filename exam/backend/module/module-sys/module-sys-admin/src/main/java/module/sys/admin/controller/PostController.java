package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;

import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.PostDto;
import module.sys.entity.Post;
import module.sys.service.PostService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "岗位管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/post")
public class PostController {

    private final PostService postService;

    @Operation(summary = "新增岗位")
    @GetMapping("/list")
    @Perm({"sys:dept:addPost", "sys:dept:editPost"})
    public Result<List<Post>> list(Long deptId) {
        return Result.okData(postService.getListByDept(deptId, FilterOptions.DISABLE_ALL_FILTER));
    }

    @Operation(summary = "新增岗位")
    @PostMapping("/add")
    @AuditLog(code = "sys:dept:addPost", value = "新增岗位")
    @Perm("sys:dept:addPost")
    public Result<Long> add(@RequestBody @Valid PostDto.EditDto dto) {
        Post entity = Post.builder()
                .deptId(dto.getDeptId())
                .code(dto.getCode())
                .name(dto.getName())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return postService.add(entity) ? Result.okData(entity.getId()) : Result.FAIL_LONG;
    }

    @Operation(summary = "编辑岗位")
    @GetMapping("/edit")
    @Perm("sys:dept:editPost")
    public Result<PostDto.EditDto> getEdit(@RequestParam Long id) {
        var postOpt = postService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(postOpt.orElse(null), new PostDto.EditDto())
        );
    }

    @Operation(summary = "编辑岗位")
    @PostMapping("/edit")
    @AuditLog(code = "sys:dept:editPost", value = "编辑岗位")
    @Perm("sys:dept:editPost")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid PostDto.EditDto dto) {
        var entity = postService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entity.isEmpty()) {
            return Result.fail("岗位不存在");
        }

        Post post = entity.get();
        post.setDeptId(dto.getDeptId());
        post.setCode(dto.getCode());
        post.setName(dto.getName());
        post.setSort(dto.getSort());
        post.setDisabled(dto.getDisabled());

        return postService.update(post) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除岗位")
    @PostMapping("/delete")
    @AuditLog(code = "sys:dept:deletePost", value = "删除岗位")
    @Perm("sys:dept:deletePost")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            postService.deleteByIds(ids);
        }
        return Result.OK;
    }
}
