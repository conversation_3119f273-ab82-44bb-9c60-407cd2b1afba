//package module.sys.job;
//
//import infra.backup.core.database.DatabaseBackupService;
//import infra.task.core.IJob;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 数据库备份
// */
//@Slf4j
//@Component
//@AllArgsConstructor
//public class DatabaseBackupJob implements IJob {
//    @Autowired
//    private final DatabaseBackupService databaseBackupService;
//
//    @Override
//    public String getJobId() {
//        return "sys_database_backup";
//    }
//
//    @Override
//    public String getJobDesc() {
//        return "系统模块-数据库自动备份";
//    }
//
//    @Override
//    public String getCron() {
//        return "0 0 3 * * *";
//    }
//
//    @Override
//    public String getCronDesc() {
//        return "每天3:00执行";
//    }
//
//    @Override
//    public Integer getSort() {
//        return 0;
//    }
//
//    @Override
//    public String execute() {
//        return databaseBackupService.backupDatabase();
//    }
//}
