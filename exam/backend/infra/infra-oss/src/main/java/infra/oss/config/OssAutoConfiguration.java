package infra.oss.config;

import infra.core.exception.ConfigException;
import infra.core.text.Str;
import infra.oss.core.IStorage;
import infra.oss.core.impl.LocalStorage;
import infra.oss.core.impl.S3Storage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * Oss存储配置
 */
@AutoConfiguration
@EnableConfigurationProperties(OssConfigProperties.class)
@ConditionalOnProperty(prefix = "app.oss", name = "provider")
@Slf4j
public class OssAutoConfiguration {
    /**
     * 创建存储服务Bean
     * 根据配置自动选择S3或本地存储实现
     */
    @Bean
    @ConditionalOnMissingBean(IStorage.class)
    public IStorage infraStorage(OssConfigProperties config) {
        log.info("[配置] 配置sso提供程序 - {}", config.getProvider());
        return createStorage(config);
    }

    /**
     * 创建存储服务实例
     */
    private IStorage createStorage(OssConfigProperties config) {
        if (Str.isEmpty(config.getProvider())) {
            throw new ConfigException("未配置app.oss.provider参数");
        }

        return switch (config.getProvider().toLowerCase()) {
            case "s3" -> new S3Storage(config.getS3());
            case "local" -> new LocalStorage(config.getLocal());
            default -> throw new IllegalArgumentException("暂未支持的OSS提供实现: " + config.getProvider());
        };
    }
}
