import type { FormInstance } from 'element-plus'
import type { Ref } from 'vue'

export function useFormReset(formRef: Ref<FormInstance | undefined>) {
  let initData: any = null

  const saveData = (data: any): void => {
    initData = JSON.parse(JSON.stringify(toRaw(data)))
  }

  const resetData = (form: any): void => {
    formRef.value?.resetFields()
    Object.assign(form, initData)
  }

  return {
    saveData,
    resetData,
  }
}
