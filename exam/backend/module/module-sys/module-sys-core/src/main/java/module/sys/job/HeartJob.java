package module.sys.job;

import infra.core.text.DateFormat;
import infra.task.core.IJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 心跳任务
 */
@Slf4j
@Component
public class HeartJob implements IJob {
    @Override
    public String getJobId() {
        return "sys_heart";
    }

    @Override
    public String getJobDesc() {
        return "系统模块-心跳任务";
    }

    @Override
    public String getCron() {
        return "0 0 * * * *";
    }

    @Override
    public String getCronDesc() {
        return "每整点执行";
    }

    @Override
    public Integer getSort() {
        return 0;
    }

    @Override
    public String execute() {
        String msg = "心跳：" + DateFormat.formatDateTime(LocalDateTime.now());
        log.debug(msg);
        return msg;
    }
}
