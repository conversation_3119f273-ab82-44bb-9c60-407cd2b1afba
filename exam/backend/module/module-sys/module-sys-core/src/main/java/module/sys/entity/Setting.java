package module.sys.entity;

import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_setting")
public class Setting extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 设置名称
    private String name;
    // 编号
    private String code;
    // 设置值
    private String value;
}
