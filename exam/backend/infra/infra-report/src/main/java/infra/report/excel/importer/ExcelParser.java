package infra.report.excel.importer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.function.Function;

/**
 * Excel 解析器
 * 提供常用的数据解析功能
 */
public interface ExcelParser<T> extends Function<Object, T> {
    
    /**
     * 原始值解析器（不做任何转换）
     */
    static ExcelParser<Object> raw() {
        return value -> value;
    }
    
    /**
     * 字符串解析器
     */
    static ExcelParser<String> string() {
        return value -> value == null ? "" : value.toString().trim();
    }
    
    /**
     * 整数解析器
     */
    static ExcelParser<Integer> integer() {
        return value -> {
            if (value == null) return null;
            if (value instanceof Number number) {
                return number.intValue();
            }
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            try {
                return Integer.valueOf(str);
            } catch (NumberFormatException e) {
                throw new ExcelParseException("无法解析为整数: " + str);
            }
        };
    }
    
    /**
     * 长整数解析器
     */
    static ExcelParser<Long> longValue() {
        return value -> {
            if (value == null) return null;
            if (value instanceof Number number) {
                return number.longValue();
            }
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            try {
                return Long.valueOf(str);
            } catch (NumberFormatException e) {
                throw new ExcelParseException("无法解析为长整数: " + str);
            }
        };
    }
    
    /**
     * 小数解析器
     */
    static ExcelParser<BigDecimal> decimal() {
        return value -> {
            switch (value) {
                case null -> {
                    return null;
                }
                case BigDecimal bd -> {
                    return bd;
                }
                case Number number -> {
                    return BigDecimal.valueOf(number.doubleValue());
                }
                default -> {
                }
            }
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            try {
                return new BigDecimal(str);
            } catch (NumberFormatException e) {
                throw new ExcelParseException("无法解析为小数: " + str);
            }
        };
    }
    
    /**
     * 布尔值解析器
     */
    static ExcelParser<Boolean> bool() {
        return bool("true", "false", "是", "否", "1", "0");
    }
    
    /**
     * 自定义布尔值解析器
     */
    static ExcelParser<Boolean> bool(String... trueValues) {
        return value -> {
            if (value == null) return null;
            if (value instanceof Boolean b) return b;
            String str = value.toString().trim().toLowerCase();
            if (str.isEmpty()) return null;
            
            for (String trueValue : trueValues) {
                if (trueValue.toLowerCase().equals(str)) {
                    return true;
                }
            }
            return false;
        };
    }
    
    /**
     * 日期时间解析器
     */
    static ExcelParser<LocalDateTime> dateTime() {
        return dateTime("yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyy-MM-dd");
    }
    
    /**
     * 自定义日期时间解析器
     */
    static ExcelParser<LocalDateTime> dateTime(String... patterns) {
        return value -> {
            if (value == null) return null;
            if (value instanceof LocalDateTime ldt) return ldt;
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            
            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    if (pattern.contains("HH:mm")) {
                        return LocalDateTime.parse(str, formatter);
                    } else {
                        return LocalDate.parse(str, formatter).atStartOfDay();
                    }
                } catch (DateTimeParseException ignored) {
                    // 尝试下一个格式
                }
            }
            throw new ExcelParseException("无法解析为日期时间: " + str);
        };
    }
    
    /**
     * 日期解析器
     */
    static ExcelParser<LocalDate> date() {
        return date("yyyy-MM-dd", "yyyy/MM/dd");
    }
    
    /**
     * 自定义日期解析器
     */
    static ExcelParser<LocalDate> date(String... patterns) {
        return value -> {
            if (value == null) return null;
            if (value instanceof LocalDate ld) return ld;
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            
            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return LocalDate.parse(str, formatter);
                } catch (DateTimeParseException ignored) {
                    // 尝试下一个格式
                }
            }
            throw new ExcelParseException("无法解析为日期: " + str);
        };
    }
    
    /**
     * 枚举解析器（按名称）
     */
    static <E extends Enum<E>> ExcelParser<E> enumByName(Class<E> enumClass) {
        return value -> {
            if (value == null) return null;
            if (enumClass.isInstance(value)) return enumClass.cast(value);
            String str = value.toString().trim();
            if (str.isEmpty()) return null;
            try {
                return Enum.valueOf(enumClass, str);
            } catch (IllegalArgumentException e) {
                throw new ExcelParseException("无法解析为枚举 " + enumClass.getSimpleName() + ": " + str);
            }
        };
    }
    
    /**
     * 自定义解析器
     */
    static <T> ExcelParser<T> custom(Function<Object, T> parser) {
        return parser::apply;
    }
    
    /**
     * 可选解析器（解析失败时返回默认值）
     */
    default ExcelParser<T> orDefault(T defaultValue) {
        return value -> {
            try {
                T result = this.apply(value);
                return result != null ? result : defaultValue;
            } catch (Exception e) {
                return defaultValue;
            }
        };
    }
    
    /**
     * 可选解析器（解析失败时返回 null）
     */
    default ExcelParser<T> optional() {
        return value -> {
            try {
                return this.apply(value);
            } catch (Exception e) {
                return null;
            }
        };
    }
    
    /**
     * Excel 解析异常
     */
    class ExcelParseException extends RuntimeException {
        public ExcelParseException(String message) {
            super(message);
        }
        
        public ExcelParseException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
