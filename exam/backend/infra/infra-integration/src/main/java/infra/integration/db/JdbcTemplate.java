package infra.integration.db;

import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

/**
 * JDBC操作封装
 * 用于第三方集成时的数据库操作
 * 示例：
 * DataSource dataSource = integrationService.getDataSourceRequired("api");
 * JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
 * // 查询单个对象
 * Optional<User> user = jdbcTemplate.queryForObject(
 *     "SELECT id, name, email FROM users WHERE id = ?",
 *     rs -> new User(rs.getLong("id"), rs.getString("name"), rs.getString("email")),
 *     userId
 * );
 * // 查询列表
 * List<Map<String, Object>> results = jdbcTemplate.queryForMapList(
 *     "SELECT * FROM products WHERE category = ?",
 *     "electronics"
 * );
 * // 执行更新
 * int affected = jdbcTemplate.execute(
 *     "UPDATE users SET last_login = ? WHERE id = ?",
 *     new Timestamp(System.currentTimeMillis()), userId
 * );
 * // 插入并获取主键
 * Optional<Long> newId = jdbcTemplate.insertAndReturnKey(
 *     "INSERT INTO orders (user_id, amount) VALUES (?, ?)",
 *     userId, amount
 * );
 */
@Slf4j
public class JdbcTemplate {
    private final DataSource dataSource;

    public JdbcTemplate(DataSource dataSource) {
        this.dataSource = Objects.requireNonNull(dataSource, "DataSource不能为空");
    }

    /**
     * 执行查询并返回单个对象
     */
    public <T> Optional<T> queryForObject(String sql, RowMapper<T> mapper, Object... params) {
        List<T> results = queryForList(sql, mapper, params);
        return results.isEmpty() ? Optional.empty() : Optional.of(results.getFirst());
    }

    /**
     * 执行查询并返回列表
     */
    public <T> List<T> queryForList(String sql, RowMapper<T> mapper, Object... params) {
        return execute(sql, params, ps -> {
            List<T> results = new ArrayList<>();
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    results.add(mapper.mapRow(rs));
                }
            }
            return results;
        });
    }

    /**
     * 执行查询并返回Map列表
     */
    public List<Map<String, Object>> queryForMapList(String sql, Object... params) {
        return queryForList(sql, this::mapToMap, params);
    }

    /**
     * 执行查询并返回单个Map
     */
    public Optional<Map<String, Object>> queryForMap(String sql, Object... params) {
        return queryForObject(sql, this::mapToMap, params);
    }

    /**
     * 执行操作（INSERT、UPDATE、DELETE）
     */
    public int execute(String sql, Object... params) {
        return execute(sql, params, PreparedStatement::executeUpdate);
    }

    /**
     * 批量执行更新操作
     */
    public int[] executeBatch(String sql, List<Object[]> batchParams) {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            for (Object[] params : batchParams) {
                setParameters(ps, params);
                ps.addBatch();
            }

            return ps.executeBatch();
        } catch (SQLException e) {
            throw new JdbcException("批量更新失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行INSERT并返回生成的主键
     */
    public Optional<Long> insertAndReturnKey(String sql, Object... params) {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

            setParameters(ps, params);
            ps.executeUpdate();

            try (ResultSet rs = ps.getGeneratedKeys()) {
                return rs.next() ? Optional.of(rs.getLong(1)) : Optional.empty();
            }
        } catch (SQLException e) {
            throw new JdbcException("插入数据并获取主键失败: " + e.getMessage(), e);
        }
    }

    /**
     * 核心执行方法
     */
    private <T> T execute(String sql, Object[] params, SqlFunction<PreparedStatement, T> action) {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement ps = conn.prepareStatement(sql)) {

            setParameters(ps, params);
            return action.apply(ps);

        } catch (SQLException e) {
            log.error("SQL执行失败: {}, 参数: {}", sql, Arrays.toString(params), e);
            throw new JdbcException("SQL执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置PreparedStatement参数
     */
    private void setParameters(PreparedStatement ps, Object... params) throws SQLException {
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
        }
    }

    /**
     * 将ResultSet转换为Map
     */
    private Map<String, Object> mapToMap(ResultSet rs) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        Map<String, Object> map = new LinkedHashMap<>();

        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            String columnName = metaData.getColumnLabel(i);
            Object value = rs.getObject(i);
            map.put(columnName, value);
        }

        return map;
    }

    /**
     * 函数式接口：行映射器
     */
    @FunctionalInterface
    public interface RowMapper<T> {
        T mapRow(ResultSet rs) throws SQLException;
    }

    /**
     * 函数式接口：SQL操作
     */
    @FunctionalInterface
    private interface SqlFunction<T, R> {
        R apply(T t) throws SQLException;
    }

    /**
     * JDBC异常封装
     */
    public static class JdbcException extends RuntimeException {
        public JdbcException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
