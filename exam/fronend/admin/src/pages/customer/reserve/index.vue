<script setup lang="ts">
definePage({
  meta: {
    title: '体检预约',
    menu: true,
    order: 4,
    icon: 'ic:outline-library-books',
  },
})
</script>

<template>
  <p>1. 协助建立体检人档案，关联联系人信息，补充体检相关基础数据（如体检类型、特殊需求）；</p>
  <p>2. 填写客户每次预期体检时间，提交前台进行档期排期；</p>
  <p>3. 实时检查体检档期，提示档期冲突，协助疏导预约瓶颈；</p>
  <p>4. 查看预约排期状态（待确认 / 已排期 / 已取消），跟踪排期进度；</p>
  <p>5. 向客户发送预约确认通知，包含体检时间、地点、注意事项</p>

  <p>客户挖掘阶段 “预约体检发起（客户可通过系统便捷发起预约，系统实时检查体检档期，避免冲突，疏导瓶颈，并向客户发送包含体检时间、地点、注意事项等内容的预约确认通知）”；</p>
</template>
