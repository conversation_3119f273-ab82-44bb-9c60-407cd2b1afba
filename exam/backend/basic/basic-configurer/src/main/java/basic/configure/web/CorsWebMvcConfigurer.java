package basic.configure.web;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 跨域配置
 */
@Slf4j
@AllArgsConstructor
public class CorsWebMvcConfigurer implements WebMvcConfigurer {
    private final CorsConfigProperties config;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns(config.getAllowedOrigins())
                .allowedMethods(config.getAllowedMethods())
                .allowedHeaders(config.getAllowedHeaders())
                .allowCredentials(config.isAllowCredentials())
                .exposedHeaders(config.getExposedHeaders())
                .maxAge(config.getMaxAge());
    }
}
