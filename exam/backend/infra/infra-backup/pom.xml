<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>infra</groupId>
        <artifactId>infra</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>infra-backup</artifactId>
    <description>备份</description>

    <dependencies>
        <!-- 基础依赖-->
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>infra</groupId>
            <artifactId>infra-oss</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 数据库-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>