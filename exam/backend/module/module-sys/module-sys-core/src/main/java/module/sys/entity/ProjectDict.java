package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 检查项目字典
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_project_dict")
public class ProjectDict extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 项目字典名称
    private String name;

    // 项目类型ID
    private Long typeId;

    // 拼音
    private String py;

    // 项目字典英文名称
    private String englishName;

    // 项目字典打印名称
    private String printName;

    // 项目字典第三方接口编码
    private String code;

    // 值类型 C：字符 N：数值
    private String valueType;

    // 值单位
    private String valueUnit;

    // 对应身体部位字典ids
    private String bodyPartIds;

    // 排序值
    private Integer sort;

    // 备注
    private String remark;

    // 禁用
    private Boolean disabled;
}