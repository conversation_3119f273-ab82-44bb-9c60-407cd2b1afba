import { downloadFile, get, post, upload } from '~/utils/request'

const baseUrl = '/sys/feeProject'

/**
 * 收费项目列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 新增收费项目
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑收费项目(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑收费项目(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除收费项目
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 启用收费项目
 */
export function enable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/enable`, ids)
}

/**
 * 禁用收费项目
 */
export function disable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/disable`, ids)
}

/**
 * 导出收费项目
 */
export function exportData(params: any): Promise<any> {
  return downloadFile(`${baseUrl}/export`, { data: params })
}

/**
 * 导入收费项目
 */
export function importData(file: File): Promise<any> {
  return upload(`${baseUrl}/import`, file)
}