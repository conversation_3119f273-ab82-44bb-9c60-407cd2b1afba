package infra.core.web;

import infra.core.text.Str;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Arrays;

/**
 * IpUtil 辅助
 */
public final class IpUtil {
    // 常见代理服务器设置的请求头（按优先级排序）
    private static final String[] PROXY_HEADERS = {
            "X-Forwarded-For",
            "X-Real-IpUtil",
            "Proxy-Client-IpUtil",
            "WL-Proxy-Client-IpUtil",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
    };

    /**
     * 获取客户端真实IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        // 按顺序检查各个代理头
        for (String header : PROXY_HEADERS) {
            String ip = request.getHeader(header);
            if (isValidIp(ip))
                return getMultistageReverseProxyIp(ip);
        }

        return getMultistageReverseProxyIp(request.getRemoteAddr());
    }

    private static boolean isValidIp(String ip) {
        return !Str.isEmpty(ip) && !"unknown".equalsIgnoreCase(ip);
    }

    private static String getMultistageReverseProxyIp(String ip) {
        if (ip != null && ip.indexOf(',') > 0) {
            return Arrays.stream(ip.split(","))
                    .map(String::trim)
                    .filter(IpUtil::isValidIp)
                    .findFirst()
                    .orElse("");
        }

        return ip;
    }
}
