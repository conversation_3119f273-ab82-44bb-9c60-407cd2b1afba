package infra.auth.core;

import infra.auth.annotation.DataPerm;

import java.util.List;
import java.util.Set;

/**
 * 用户
 */
public interface IUser {
    /**
     * 用户ID
     */
    Long getId();

    /**
     * 登录名称
     */
    String getLoginName();

    /**
     * 用户名称
     */
    String getUserName();

    /**
     * 部门ID
     */
    Long getDeptId();

    /**
     * 部门名称
     */
    String getDeptName();

    /**
     * 部门及子部门ID
     */
    List<Long> getDeptAndChildren();

    /**
     * 数据权限
     */
    DataPerm getDataPerm();

    /**
     * 获取功能权限
     */
    Set<String> getPerms();

    /**
     * 是否是超级管理员
     */
    boolean isAdmin();

    /**
     * 检查用户是否拥有指定权限
     *
     * @param perms 权限集合（满足任意一个即可）
     * @return 是否有权限
     */
    default boolean hasPerm(String... perms) {
        if (perms == null || perms.length == 0 || isAdmin()) {
            return true;
        }

        var userPerms = getPerms();
        if (userPerms == null || userPerms.isEmpty()) return false;

        for (var perm : perms) {
            String permPrefix = perm + ":";
            if (userPerms.stream().anyMatch(item -> item.equals(perm) || item.startsWith(permPrefix))) {
                return true;
            }
        }

        return false;
    }
}
