package infra.auth.web;

import infra.auth.core.AuthManager;
import infra.auth.core.IUser;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * 用户上下文过滤器
 * 基于Spring Web Filter实现
 */
@Slf4j
@RequiredArgsConstructor
public class UserContextFilter extends OncePerRequestFilter {
    private final AuthManager authManager;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws IOException, ServletException {

        IUser currentUser = authManager.getCurrentUser();
        if (currentUser != null) {
            UserContext.setCurrentUser(currentUser);
        }

        filterChain.doFilter(request, response);
    }
}
