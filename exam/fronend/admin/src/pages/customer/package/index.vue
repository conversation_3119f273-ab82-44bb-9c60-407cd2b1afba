<script setup lang="ts">
definePage({
  meta: {
    title: '在售套餐',
    menu: true,
    order: 3,
    icon: 'fluent-mdl2:packages',
  },
})
</script>

<template>
  <p>1. 展示当前所有在售体检套餐，包含套餐项目明细、适用人群说明；</p>
  <p>2. 实时同步套餐价格（关联系统内置最新体检项目物价标准），确保价格依据准确；</p>
  <p>3. 支持按套餐类型、价格区间、适用人群筛选查询；</p>
  <p>4. 标记套餐热门程度（如 “高频推荐”“新品套餐”），辅助客户推荐；</p>
  <p>5. 查看套餐历史价格变动记录，跟踪价格调整情况</p>

  <P>客户挖掘阶段 “体检套餐推荐（智能算法匹配个性化套餐）”；</P>
</template>
