package infra.core.file;

import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.CompressionMethod;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * ZIP压缩和解压工具类
 */
@Slf4j
public class Zip {

    /**
     * 创建压缩文件
     *
     * @param sourceFiles 源文件
     * @param targetFile  目标ZIP文件
     * @param password    压缩密码，可为null
     * @return 压缩后的ZIP文件
     * @throws IOException 压缩失败时抛出
     */
    public static File compress(List<File> sourceFiles, File targetFile, String password) throws IOException {
        if (CollectionUtils.isEmpty(sourceFiles)) {
            throw new IOException("压缩源文件列表不能为空");
        }

        if (targetFile == null) {
            throw new IOException("目标文件不能为空");
        }

        // 确保目标文件的父目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new IOException("无法创建目标目录: " + parentDir.getAbsolutePath());
            }
        }

        try (ZipFile zipFile = new ZipFile(targetFile)) {
            // 设置压缩参数
            ZipParameters zipParameters = new ZipParameters();
            zipParameters.setCompressionMethod(CompressionMethod.DEFLATE);
            zipParameters.setCompressionLevel(CompressionLevel.HIGHER);

            // 如果提供了密码，设置AES加密
            if (password != null && !password.trim().isEmpty()) {
                zipParameters.setEncryptFiles(true);
                zipParameters.setEncryptionMethod(EncryptionMethod.AES);
                zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
                zipFile.setPassword(password.toCharArray());
            }

            // 添加文件到ZIP
            zipFile.addFiles(sourceFiles.stream().filter(File::exists).toList(), zipParameters);
            return targetFile;
        } catch (ZipException e) {
            // 清理失败的压缩文件
            if (targetFile.exists()) {
                boolean deleted = targetFile.delete();
                log.debug("清理失败的压缩文件: {}, 删除结果: {}", targetFile.getAbsolutePath(), deleted);
            }
            log.error("ZIP压缩失败", e);
            throw new IOException("ZIP压缩失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建压缩文件（临时文件）
     *
     * @param sourceFiles 源文件
     * @param password    压缩密码，可为null
     * @return 压缩后的临时ZIP文件
     * @throws IOException 压缩失败时抛出
     */
    public static File compress(List<File> sourceFiles, String password) throws IOException {
        File tempFile = File.createTempFile("compress_", ".zip");
        return compress(sourceFiles, tempFile, password);
    }

    /**
     * 解压
     *
     * @param zipFile   ZIP压缩文件
     * @param targetDir 目标解压目录
     * @param password  解压密码，可为null
     * @throws IOException 解压失败时抛出
     */
    public static void decompress(File zipFile, File targetDir, String password) throws IOException {
        if (zipFile == null || !zipFile.exists()) {
            throw new IOException("ZIP文件不存在或为空");
        }

        if (targetDir == null) {
            throw new IOException("目标目录不能为空");
        }

        // 确保目标目录存在
        if (!targetDir.exists()) {
            boolean created = targetDir.mkdirs();
            if (!created) {
                throw new IOException("无法创建目标目录: " + targetDir.getAbsolutePath());
            }
        }

        try (ZipFile zip = new ZipFile(zipFile)) {
            // 如果提供了密码，设置密码
            if (password != null && !password.trim().isEmpty()) {
                zip.setPassword(password.toCharArray());
            }

            // 解压所有文件
            zip.extractAll(targetDir.getAbsolutePath());

        } catch (ZipException e) {
            log.error("ZIP解压失败", e);
            throw new IOException("ZIP解压失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查ZIP文件是否需要密码
     *
     * @param zipFile ZIP压缩文件
     * @return true表示需要密码
     * @throws IOException 检查失败时抛出
     */
    public static boolean isPasswordProtected(File zipFile) throws IOException {
        if (zipFile == null || !zipFile.exists()) {
            throw new IOException("ZIP文件不存在或为空");
        }

        try (ZipFile zip = new ZipFile(zipFile)) {
            return zip.isEncrypted();
        } catch (ZipException e) {
            log.error("检查ZIP文件密码保护状态失败", e);
            throw new IOException("检查ZIP文件密码保护状态失败: " + e.getMessage(), e);
        }
    }
}