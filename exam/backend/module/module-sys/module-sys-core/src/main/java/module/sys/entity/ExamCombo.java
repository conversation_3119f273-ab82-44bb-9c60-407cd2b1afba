package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import module.sys.model.AgeLimit;
import module.sys.model.CustomerLimit;
import module.sys.model.SexLimit;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 体检套餐
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_exam_combo")
public class ExamCombo extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 套餐代码
    private String code;

    // 套餐名称
    private String name;

    // 拼音
    private String py;

    // 套餐价格
    private BigDecimal price;

    // 体检类型字典ID
    private Long examType;

    // 适用性别
    private SexLimit sex;

    // 适用年龄阶段
    private AgeLimit age;

    // 适用客户类型 0：个人团队皆可 1：个人 2：团队
    private CustomerLimit customer;

    // 排序值
    private Integer sort;

    // 备注
    private String remark;

    // 禁用
    private Boolean disabled;
}