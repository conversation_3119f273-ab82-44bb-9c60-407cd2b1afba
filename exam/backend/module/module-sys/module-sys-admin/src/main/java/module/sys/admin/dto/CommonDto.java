package module.sys.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CommonDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AppInfoDto {
        private String appName;
        private boolean watermark;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UserInfoDto {
        private Long id;
        private String userName;
        private String py;
        private String phone;
        private String email;
    }
}
