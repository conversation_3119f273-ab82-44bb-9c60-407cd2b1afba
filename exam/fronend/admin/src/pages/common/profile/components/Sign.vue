<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { ElMessageBox } from 'element-plus'
import SignaturePad from 'signature_pad'
import { getSignImage, setSign } from '~/api/common/auth'
import { useLoading } from '~/composables'
import { tips } from '~/utils'

const { loading, withLoading } = useLoading()
const { loading: uploading, withLoading: withUploading } = useLoading()
const signPad = ref<SignaturePad | null>(null)
const canvasRef = ref<HTMLCanvasElement | null>(null)
const signImg = ref('')
const isSigning = ref(false)
const isDark = useDark()

// 初始化签名板
function initSignPad() {
  if (canvasRef.value) {
    // 适当调整画布尺寸以获得更清晰的签名
    const ratio = Math.max(window.devicePixelRatio || 1, 1)
    canvasRef.value.width = canvasRef.value.offsetWidth * ratio
    canvasRef.value.height = canvasRef.value.offsetHeight * ratio
    canvasRef.value.getContext('2d')?.scale(ratio, ratio)

    signPad.value = new SignaturePad(canvasRef.value, {
      penColor: isDark.value ? 'rgb(255, 255, 255)' : 'rgb(0, 0, 0)',
      backgroundColor: 'rgba(0, 0, 0, 0)', // 使用透明背景
    })
    signPad.value.clear()
  }
}

// 获取已有的签名
async function fetchSign() {
  const signImage = await getSignImage()
  if (signImage) {
    signImg.value = signImage
    isSigning.value = false
  }
  else {
    // 如果没有签名，则直接进入签名模式
    isSigning.value = true
    await nextTick()
    initSignPad()
  }
}

// 处理重新签名
async function handleReSign() {
  isSigning.value = true
  await nextTick()
  initSignPad()
}

// 处理取消签名
function handleCancel() {
  if (signImg.value) {
    isSigning.value = false
  }
  else {
    // 如果之前没有签名，取消则清空画布
    signPad.value?.clear()
  }
}

// 清理画布
function handleClear() {
  signPad.value?.clear()
}

// 处理点击上传签名
async function handleUploadClick() {
  try {
    await ElMessageBox.confirm('为确保签名效果，请上传背景透明的png签名图片', '提示', {
      confirmButtonText: '选择文件',
      cancelButtonText: '取消',
      type: 'info',
    })
    // 如果用户确认，触发文件选择
    const uploadInput = document.createElement('input')
    uploadInput.type = 'file'
    uploadInput.accept = '.png,image/png'
    uploadInput.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        handleImageUpload(file)
      }
    }
    uploadInput.click()
  }
  catch (_) {
  }
}

// 处理图片上传
async function handleImageUpload(file: File) {
  if (!file.type.startsWith('image/')) {
    tips.error('请上传图片文件')
    return false
  }

  if (!file.name.toLowerCase().endsWith('.png') && file.type !== 'image/png') {
    tips.error('只能上传PNG格式的图片文件')
    return false
  }

  await withUploading(async () => {
    try {
      const processedImage = await processUploadedImage(file)
      await nextTick()
      if (canvasRef.value && signPad.value) {
        await drawImageToCanvas(processedImage)
      }
    }
    catch (error) {
      tips.error(`图片处理失败`)
      console.error(error)
    }
  })

  return false
}

// 处理上传的图片
async function processUploadedImage(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const img = new Image()
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('无法创建画布上下文'))
          return
        }

        // 设置画布尺寸
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)

        // 获取图片数据进行处理
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        // const processedData = removeBackground(imageData)
        const processedData = imageData

        // 创建新画布用于输出
        const outputCanvas = document.createElement('canvas')
        const outputCtx = outputCanvas.getContext('2d')
        if (!outputCtx) {
          reject(new Error('无法创建输出画布'))
          return
        }

        outputCanvas.width = canvas.width
        outputCanvas.height = canvas.height

        // 设置白色背景
        outputCtx.fillStyle = 'white'
        outputCtx.fillRect(0, 0, outputCanvas.width, outputCanvas.height)

        // 绘制处理后的图片
        outputCtx.putImageData(processedData, 0, 0)

        resolve(outputCanvas.toDataURL('image/png'))
      }
      img.onerror = () => reject(new Error('图片加载失败'))
      img.src = e.target?.result as string
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsDataURL(file)
  })
}

// 将图片绘制到签名画布
async function drawImageToCanvas(imageSrc: string) {
  if (!canvasRef.value || !signPad.value)
    return

  const img = new Image()
  img.onload = () => {
    const canvas = canvasRef.value!
    const ctx = canvas.getContext('2d')
    if (!ctx)
      return

    // 清空画布
    signPad.value?.clear()

    // 计算缩放比例以适应画布
    const scaleX = canvas.width / img.width
    const scaleY = canvas.height / img.height
    const scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小

    const scaledWidth = img.width * scale
    const scaledHeight = img.height * scale
    const x = (canvas.width - scaledWidth) / 2
    const y = (canvas.height - scaledHeight) / 2

    // 绘制图片
    ctx.drawImage(img, x, y, scaledWidth, scaledHeight)
  }
  img.src = imageSrc
}

// 提交签名
async function handleSubmit() {
  try {
    await ElMessageBox.confirm('确认提交当前签名吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await withLoading(async () => {
      // 如果是暗黑模式，需要生成黑色签名用于提交
      let submitBase64: string
      if (isDark.value) {
        // 创建新画布用于生成黑色签名
        const tempCanvas = document.createElement('canvas')
        const tempCtx = tempCanvas.getContext('2d')
        if (!tempCtx || !canvasRef.value)
          return

        tempCanvas.width = canvasRef.value.width
        tempCanvas.height = canvasRef.value.height

        // 重新绘制签名数据，但使用黑色
        const signData = signPad.value?.toData()
        if (signData) {
          tempCtx.strokeStyle = 'black'
          tempCtx.lineWidth = 2
          tempCtx.lineCap = 'round'
          tempCtx.lineJoin = 'round'

          signData.forEach((stroke) => {
            if (stroke.points.length > 0) {
              tempCtx.beginPath()
              stroke.points.forEach((point, index) => {
                if (index === 0) {
                  tempCtx.moveTo(point.x, point.y)
                }
                else {
                  tempCtx.lineTo(point.x, point.y)
                }
              })
              tempCtx.stroke()
            }
          })
        }

        submitBase64 = tempCanvas.toDataURL('image/png')
      }
      else {
        submitBase64 = signPad.value?.toDataURL('image/png') || ''
      }

      if (!submitBase64) {
        tips.error('无法获取签名数据')
        return
      }

      await setSign(submitBase64)
      signImg.value = submitBase64
      isSigning.value = false
      tips.success('签名设置成功')
    })
  }
  catch (error) {
    // 用户点击取消或API调用失败
    if (error !== 'cancel')
      console.error('签名提交失败:', error)
  }
}

onMounted(fetchSign)
</script>

<template>
  <div class="p-1">
    <!-- 显示已有签名 -->
    <div v-if="signImg && !isSigning" class="h-360px max-w-800px w-full flex items-center justify-center border border-gray-400 rounded-md border-dashed dark:border-gray-600">
      <img :src="signImg" alt="用户签名" class="max-h-full max-w-full">
    </div>

    <!-- 签名画布 -->
    <div v-if="isSigning" v-loading="loading" class="h-360px max-w-800px w-full border rounded-md bg-gray-100 dark:bg-dark-500">
      <canvas ref="canvasRef" class="h-full w-full" />
    </div>

    <!-- 操作按钮 -->
    <div class="mt-6 max-w-780px flex justify-center">
      <div v-if="signImg && !isSigning" class="flex gap-3">
        <el-button type="primary" @click="handleReSign">
          <template #icon>
            <icon icon="radix-icons:reset" />
          </template>
          重新签名
        </el-button>
      </div>
      <div v-else-if="isSigning" class="flex gap-8">
        <div class="flex gap-3">
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            <template #icon>
              <icon icon="mdi:check" />
            </template>
            提交签名
          </el-button>
          <el-button :loading="uploading" @click="handleUploadClick">
            <template #icon>
              <icon icon="material-symbols:upload-rounded" />
            </template>
            上传签名图片
          </el-button>
        </div>
        <div class="flex">
          <el-button @click="handleClear">
            清空画布
          </el-button>
          <el-button v-if="signImg" @click="handleCancel">
            取消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
