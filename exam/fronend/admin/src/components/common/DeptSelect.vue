<script setup lang="ts">
import { getCommonDeptTree } from '~/api/sys/dept'

interface DeptSelectProps {
  /** 数据，如果传递了数据则以传递的数据为准 */
  data?: any[]
  /** 是否显示岗位 */
  post?: boolean
  /** 值属性 */
  valueField?: string
  /** 父级id */
  parent?: number
  /** 是否显示已禁用项 */
  disabled?: boolean
}

const props = withDefaults(defineProps<DeptSelectProps>(), {
  valueField: 'id',
  post: false,
  disabled: false,
})
const attrs = useAttrs()
const modelValue = defineModel()
const modelName = defineModel('name')
const data = ref<any[]>([])

watch(() => props.data, (val) => {
  if (val) {
    data.value = val
  }
})

// 处理name
function handleChange(value: any) {
  const node = data.value?.find(o => o.id === value)
  modelName.value = node?.name || '';
  (attrs.onChange as ((value: any) => void) | undefined)?.(value)
}

// 是否为岗位数据
function isPost(data: any) {
  return data.key.startsWith('p')
}

// 过滤
function filterNode(value: string, data: any) {
  if (!props.post && isPost(data))
    return false

  value = value?.trim()?.toLowerCase()
  if (!value)
    return true

  return data.name.toLowerCase().includes(value) || (data.data && (
    (data.data.py && data.data.py.toLowerCase().includes(value))
    || (data.data.code && data.data.code.toLowerCase().includes(value))
    || (data.data.py && data.data.py.toLowerCase().includes(value))
  ))
}

onMounted(async () => {
  if (props.data) {
    data.value = props.data
  }
  else {
    const res = await getCommonDeptTree({
      post: props.post,
      parent: props.parent,
      disabled: props.disabled,
    })
    data.value = res.data
  }
})
</script>

<template>
  <el-tree-select
    v-model="modelValue" node-key="key" :filter-node-method="filterNode" filterable placeholder="请选择"
    clearable :data="data" :props="{ label: 'name', value: props.valueField, children: 'children' }" check-strictly v-bind="attrs"
    @change="handleChange"
  >
    <template #default="{ node, data: item }">
      <icon v-if="item.data.type === 1" icon="ph:buildings" title="公司" />
      <icon v-else-if="isPost(item)" icon="mdi:account-tie" title="岗位" />
      <icon v-else icon="fluent-mdl2:org" title="科室" />
      <span class="ml-1">{{ node.label }}</span>
    </template>
  </el-tree-select>
</template>
