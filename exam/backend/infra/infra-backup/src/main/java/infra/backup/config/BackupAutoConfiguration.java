package infra.backup.config;

import infra.backup.core.database.DatabaseBackupService;
import infra.oss.core.IStorage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 备份模块自动配置类
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(BackupConfigProperties.class)
public class BackupAutoConfiguration {

    /**
     * 数据库备份服务
     */
    @Bean
    @ConditionalOnMissingBean(DatabaseBackupService.class)
    @ConditionalOnProperty(prefix = "app.backup.database", name = "enable", havingValue = "true")
    public DatabaseBackupService infraDatabaseBackupService(BackupConfigProperties config, IStorage storage) {
        log.info("[配置] 初始化数据库备份服务，已配置备份数据库数量：{}", config.getDatabase().getDs().size());
        return new DatabaseBackupService(config, storage);
    }
}