package infra.cache.core;

import lombok.AllArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 缓存管理器
 * 适配SpringCache
 */
public class CacheManager implements org.springframework.cache.CacheManager {
    private final ICache cache;
    private final ConcurrentMap<String, Cache> cacheMap = new ConcurrentHashMap<>();

    public CacheManager(ICache cache) {
        this.cache = cache;
    }

    @Override
    public Cache getCache(@NonNull String name) {
        return cacheMap.computeIfAbsent(name, cacheName -> new CacheAdapter(cacheName, cache));
    }

    @Override
    @NonNull
    public Collection<String> getCacheNames() {
        return Collections.unmodifiableSet(cacheMap.keySet());
    }

    /**
     * ICache到Spring Cache的适配器
     */
    @AllArgsConstructor
    public static class CacheAdapter implements Cache {
        private final String name;
        private final ICache cache;

        @Override
        @NonNull
        public String getName() {
            return name;
        }

        @Override
        @NonNull
        public Object getNativeCache() {
            return cache;
        }

        @Override
        @Nullable
        public ValueWrapper get(@NonNull Object key) {
            String cacheKey = generateKey(key);
            Object value = cache.get(cacheKey, Object.class);
            return value != null ? new SimpleValueWrapper(value) : null;
        }

        @Override
        @Nullable
        public <T> T get(@NonNull Object key, @Nullable Class<T> type) {
            String cacheKey = generateKey(key);
            return cache.get(cacheKey, type);
        }

        @Override
        @Nullable
        public <T> T get(@NonNull Object key, @NonNull Callable<T> valueLoader) {
            String cacheKey = generateKey(key);

            T cachedValue = cache.get(cacheKey, null);
            if (cachedValue != null) {
                return cachedValue;
            }

            try {
                T newValue = valueLoader.call();
                if (newValue != null) {
                    cache.set(cacheKey, newValue);
                }
                return newValue;
            } catch (Exception e) {
                throw new ValueRetrievalException(key, valueLoader, e);
            }
        }

        @Override
        public void put(@NonNull Object key, @Nullable Object value) {
            String cacheKey = generateKey(key);
            if (value != null) {
                cache.set(cacheKey, value);
            }
        }

        @Override
        @Nullable
        public ValueWrapper putIfAbsent(@NonNull Object key, @Nullable Object value) {
            String cacheKey = generateKey(key);
            Object existingValue = cache.get(cacheKey, Object.class);

            if (existingValue == null && value != null) {
                cache.set(cacheKey, value);
                return null;
            }

            return existingValue != null ? new SimpleValueWrapper(existingValue) : null;
        }

        @Override
        public void evict(@NonNull Object key) {
            String cacheKey = generateKey(key);
            cache.delete(cacheKey);
        }

        @Override
        public boolean evictIfPresent(@NonNull Object key) {
            String cacheKey = generateKey(key);
            boolean exists = cache.exists(cacheKey);
            if (exists) {
                cache.delete(cacheKey);
            }
            return exists;
        }

        @Override
        public void clear() {
            cache.deletePattern(name + ":");
        }

        @Override
        public boolean invalidate() {
            clear();
            return true;
        }

        /**
         * 生成缓存键
         */
        private String generateKey(Object key) {
            return name + ":" + key.toString();
        }

        /**
         * 简单值包装器
         */
        private record SimpleValueWrapper(Object value) implements ValueWrapper {
            @Override
            @Nullable
            public Object get() {
                return value;
            }
        }
    }
}
