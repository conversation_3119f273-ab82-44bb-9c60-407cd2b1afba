<script setup lang="ts">
definePage({
  meta: {
    title: '健康档案',
    menu: true,
    order: 1,
    icon: 'mingcute:user-follow-2-line',
  },
})
</script>

<template>
<p>1. 档案整合：为客户建立专属档案，自动整合历次体检数据、健康评估结果、干预措施，形成完整健康数据链；</p>
<p>2. 动态维护：支持健管人员补充随访记录、用药信息，系统标记更新痕迹，确保档案时效性；</p>
<p>3. 历史回溯：客户与医护人员可查询历史体检记录，对比健康指标变化趋势（如血压年度波动）；</p>
<p>4. 权限管控：按角色设置访问权限（客户仅查看本人档案），保障数据安全</p>

健康管理阶段 “客户档案维护（整合历次数据，动态更新）”“历史检查回溯（快速回溯记录）”；系统技术底座 “角色权限矩阵保障数据安全”
</template>
