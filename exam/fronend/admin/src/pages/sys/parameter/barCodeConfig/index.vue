<script setup lang="ts">
import * as api from '~/api/sys/barCodeConfig'
import { resetForm, tips } from '~/utils'

definePage({
  meta: {
    title: '条码配置',
    perm: 'sys:barCodeConfig',
    keepAlive: true,
    menu: true,
    order: 8,
  },
})

// 状态及数据
const queryFormRef = useTemplateRef('queryFormRef')
const tableRef = useTemplateRef<any>('tableRef')
const formRef = useTemplateRef('formRef')

const canAction = computed(() => tableRef.value?.hasSelected && !tableRef.value?.loading)

// 表单
const query = reactive({
  type: 'name',
  key: '',
})
const initData = {
  prefix: '',
  name: '',
  len: 8,
  startNo: 1,
  remark: '',
  sampleVolume: '',
  sampleColor: '',
  sampleRemark: '',
  disabled: false,
}

// 验证规则
const rules = {
  prefix: [{ required: true, message: '请输入条形码前缀' }],
  name: [{ required: true, message: '请输入条形码名称' }],
  len: [{ required: true, message: '请输入条形码长度' }],
}

// 操作
async function handleBy(ids: number[], fn: any) {
  if (ids && ids.length) {
    await tableRef.value.withLoading(async () => {
      await fn(ids)
      tips.success()
      await tableRef.value.fetchData()
    })
  }
}

// 删除
async function handleRemove(ids: number[]) {
  handleBy(ids, api.remove)
}

// 启用
async function handleEnable(ids: number[]) {
  handleBy(ids, api.enable)
}

// 禁用
async function handleDisable(ids: number[]) {
  handleBy(ids, api.disable)
}
</script>

<template>
  <div class="p-4">
    <table-list ref="tableRef" :fetch-api="api.list" :query-params="query">
      <template #search>
        <el-form
          ref="queryFormRef" :model="query" :inline="true" @submit.prevent="tableRef.search()"
          @reset="resetForm(queryFormRef)"
        >
          <el-form-item prop="type" class="w-26 mr-1!">
            <el-select v-model="query.type">
              <el-option label="名称" value="name" />
              <el-option label="前缀" value="prefix" />
            </el-select>
          </el-form-item>
          <el-form-item prop="key" class="w-60">
            <el-input v-model="query.key" placeholder="请输入搜索关键词" clearable>
              <template #prefix>
                <icon icon="carbon:search" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="tableRef?.loading">
              <template #icon>
                <icon icon="mdi:magnify" />
              </template>
              查询
            </el-button>
            <el-button native-type="reset">
              <template #icon>
                <icon icon="radix-icons:reset" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <template #left-actions>
        <el-button v-if="$hasPerm('sys:barCodeConfig:add')" type="primary" @click="formRef.add()">
          <template #icon>
            <icon icon="ep:plus" />
          </template>
          新增
        </el-button>
        <el-button-group class="ml-2">
          <el-button v-if="$hasPerm('sys:barCodeConfig:enable')" title="启用" :disabled="!canAction" @click="handleEnable(tableRef.selected)">
            <template #icon>
              <icon icon="codicon:debug-start" />
            </template>
            启用
          </el-button>
          <el-popconfirm v-if="$hasPerm('sys:barCodeConfig:disable')" title="确认要禁用选中项吗？" placement="bottom" @confirm="handleDisable(tableRef.selected)">
            <template #reference>
              <el-button title="禁用选中" :disabled="!canAction">
                <template #icon>
                  <icon icon="ant-design:stop-twotone" />
                </template>
                禁用
              </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm v-if="$hasPerm('sys:barCodeConfig:delete')" title="确认要删除选中的项吗？" placement="bottom" @confirm="handleRemove(tableRef.selected)">
            <template #reference>
              <el-button title="删除" :disabled="!canAction">
                <template #icon>
                  <icon icon="ep:delete" />
                </template>
                删除
              </el-button>
            </template>
          </el-popconfirm>
        </el-button-group>
      </template>
      <el-table-column type="selection" align="center" width="50" />
      <el-table-column prop="id" label="ID" align="center" width="100" />
      <el-table-column prop="prefix" label="条形码前缀" align="center" width="120" />
      <el-table-column prop="name" label="条形码名称" show-overflow-tooltip />
      <el-table-column prop="len" label="长度" align="center" width="80" />
      <el-table-column prop="startNo" label="起始编号" align="center" width="120" />
      <el-table-column prop="currentNo" label="当前编号" align="center" width="150" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column prop="disabled" label="状态" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="row.disabled ? 'danger' : 'success'">
            {{ row.disabled ? '禁用' : '启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="space-x-2">
            <el-button v-if="$hasPerm('sys:barCodeConfig:edit')" link @click="formRef.edit(row.id)">
              编辑
            </el-button>
            <el-popconfirm
              v-if="$hasPerm('sys:barCodeConfig:delete')" title="确认要删除该项吗？" placement="left"
              @confirm="handleRemove([row.id])"
            >
              <template #reference>
                <el-button type="warning" link>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </table-list>
  </div>

  <!-- 编辑对话框 -->
  <detail-form
    ref="formRef" class="p-6" title="条码配置" :init-data="initData" :rules="rules" width="700px"
    :get-api="api.getEdit" :add-api="api.add" :edit-api="api.postEdit" @success="tableRef.fetchData()"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="条形码前缀" prop="prefix">
          <el-input v-model="formRef.formData.prefix" :disabled="formRef.isEdit" placeholder="请输入条形码前缀" maxlength="7" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="条形码名称" prop="name">
          <el-input v-model="formRef.formData.name" placeholder="请输入条形码名称" maxlength="31" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="条形码长度" prop="len">
          <el-input-number v-model="formRef.formData.len" :min="1" :max="20" placeholder="不含前缀" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="起始编号" prop="startNo">
          <el-input-number v-model="formRef.formData.startNo" :min="1" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="备注说明" prop="remark">
          <el-input v-model="formRef.formData.remark" type="textarea" :rows="3" placeholder="请输入备注说明" maxlength="255" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-form-item label="样本体积" prop="sampleVolume">
          <el-input v-model="formRef.formData.sampleVolume" placeholder="请输入样本体积" maxlength="15" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="样本颜色" prop="sampleColor">
          <el-input v-model="formRef.formData.sampleColor" placeholder="请输入样本颜色" maxlength="15" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="样本说明" prop="sampleRemark">
          <el-input v-model="formRef.formData.sampleRemark" type="textarea" :rows="3" placeholder="请输入样本说明" maxlength="255" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="状态" prop="disabled">
          <el-radio-group v-model="formRef.formData.disabled">
            <el-radio-button label="启用" :value="false" />
            <el-radio-button label="禁用" :value="true" />
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </detail-form>
</template>
