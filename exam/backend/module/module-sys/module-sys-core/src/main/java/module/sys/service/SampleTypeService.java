package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.SampleType;
import module.sys.mapper.SampleTypeMapper;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 样本类型服务
 */
@Slf4j
@Service
public class SampleTypeService extends ServiceBase<SampleTypeMapper, SampleType> {
    @Override
    protected Function<QueryWrapper<SampleType>, QueryWrapper<SampleType>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<SampleType>, QueryWrapper<SampleType>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(SampleType entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByCode(entity.getCode(), isUpdate ? entity.getId() : null)) {
            throw new BizException("样本类型代码已存在");
        }

        setPinyin(entity);
    }

    /**
     * 检查代码是否已存在
     */
    public boolean existsByCode(String code, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(w -> {
            w.eq("code", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 设置拼音
     */
    private void setPinyin(SampleType value) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(value.getName());
        value.setPy(pinyin.py());
    }
}
