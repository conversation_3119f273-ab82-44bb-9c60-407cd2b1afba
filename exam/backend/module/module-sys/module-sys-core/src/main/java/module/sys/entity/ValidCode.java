package module.sys.entity;

import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 验证码
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_valid_code")
public class ValidCode extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 验证码
    private String code;
    // 附加信息
    private String extra;
    // 过期时间
    private LocalDateTime expiryTime;
    // 是否已使用
    @Builder.Default
    private Boolean used = false;
    // 尝试次数
    @Builder.Default
    private Integer tryCount = 0;
}
