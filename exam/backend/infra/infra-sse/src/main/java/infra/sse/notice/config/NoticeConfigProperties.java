package infra.sse.notice.config;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * SSE通知配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.sse.notice")
public class NoticeConfigProperties {
    /** 是否启用SSE通知 */
    private boolean enabled = false;

    /** API路径(默认为/sse/notice) */
    private String apiPath = "/sse/notice";

    /** 用户连接验证时使用的密钥 */
    @NotBlank
    private String secretKey = "";

    /**
     * 是否启用redis发布/订阅模式(多服务实例时启用)
     */
    private boolean redis = false;

    /**
     * SSE消息推送频道前缀
     */
    private String redisChannelPrefix = "sse:notice:";
}
