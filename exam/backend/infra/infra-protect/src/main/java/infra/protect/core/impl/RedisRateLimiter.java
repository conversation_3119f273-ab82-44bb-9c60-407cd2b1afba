package infra.protect.core.impl;

import infra.protect.core.IRateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;

import java.util.Collections;

/**
 * 基于Redis的分布式限流器
 * 使用滑动窗口算法实现
 */
@Slf4j
public class RedisRateLimiter implements IRateLimiter {
    private final RedissonClient redissonClient;
    private final RScript script;

    public RedisRateLimiter(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
        this.script = redissonClient.getScript(LongCodec.INSTANCE);
    }

    private static final String TRY_ACQUIRE_SCRIPT = """
        local key = KEYS[1]
        local now = tonumber(ARGV[1])
        local window = tonumber(ARGV[2])
        local count = tonumber(ARGV[3])
        
        -- 移除窗口外的时间戳
        redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
        
        -- 获取当前窗口内的请求数
        local current_count = redis.call('ZCARD', key)
        
        -- 判断是否超出限制
        if current_count < count then
            redis.call('ZADD', key, now, now)
            redis.call('EXPIRE', key, window + 1)
            return 1
        else
            return 0
        end
    """;

    private static final String REMAINING_SCRIPT = """
        local key = KEYS[1]
        local now = tonumber(ARGV[1])
        local window = tonumber(ARGV[2])
        local count = tonumber(ARGV[3])
        
        -- 移除窗口外的时间戳
        redis.call('ZREMRANGEBYSCORE', key, 0, now - window)
        
        -- 获取当前窗口内的请求数
        local current_count = redis.call('ZCARD', key)
        
        return count - current_count
        """;

    @Override
    public boolean tryAcquire(String key, int count, int window) {
        try {
            Long result = script.eval(RScript.Mode.READ_WRITE, TRY_ACQUIRE_SCRIPT,
                    RScript.ReturnType.INTEGER, Collections.singletonList(key),
                    System.currentTimeMillis() / 1000, window, count);
            return result == 1L;
        } catch (Exception e) {
            log.error("Redis限流执行失败，key: {}", key, e);
            // 降级策略：出现异常时拒绝请求，保护系统
            return false;
        }
    }


    @Override
    public long remaining(String key, int count, int window) {
        try {
            Long result = script.eval(RScript.Mode.READ_WRITE, REMAINING_SCRIPT,
                    RScript.ReturnType.INTEGER, Collections.singletonList(key),
                    System.currentTimeMillis() / 1000, window, count);
            return Math.max(0, result);
        } catch (Exception e) {
            log.error("获取剩余许可数失败，key: {}", key, e);
            return 0; // 异常时返回0，表示无剩余许可
        }
    }


    @Override
    public void reset(String key) {
        redissonClient.getScoredSortedSet(key).delete();
    }
}
