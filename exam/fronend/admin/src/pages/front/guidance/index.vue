<script setup lang="ts">
definePage({
  meta: {
    title: '检查指引',
    menu: true,
    order: 6,
    icon: 'icon-park-outline:guide-board',
  },
})
</script>

<template>
  <p>1. 客户报到确认：核验客户身份（预约号 / 身份证），关联开单与排期信息，确认客户体检资格；</p>
  <p>2. 指引单生成：根据客户体检项目、排期科室，自动生成个性化指引单（含项目顺序、科室位置）；</p>
  <p>3. 指引单打印：支持单份 / 批量打印（团检），打印后更新指引单状态（未打印 / 已打印）；</p>
  <p>4. 报到记录保存，为检中客户状态追踪提供报到数据</p>

  检前阶段 “指引单下发（自动生成个性化指引单，电子 / 纸质下发）”；前台导检 “客户状态实时追踪（确认客户报到与体检进度）”
</template>
