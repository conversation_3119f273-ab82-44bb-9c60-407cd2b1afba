package module.sys.model;

import com.baomidou.mybatisplus.annotation.EnumValue;
import infra.core.common.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 年龄阶段限制枚举
 */
@Getter
@AllArgsConstructor
public enum AgeLimit implements IDictEnum {
    NORMAL(0, "不限"),
    ADULT(1, "成人"),
    CHILD(2, "儿童");

    @EnumValue
    private final int value;
    private final String name;
}
