<script setup lang="ts">
definePage({
  meta: {
    title: '欢迎页',
  },
})

// 示例：公共通知数据（原型静态数据）
const noticeList = ref([
  {
    type: 'warning',
    title: '国庆期间体检档期调整',
    content: '2024年10月1日-10月3日仅开放上午体检（8:00-12:00），10月4日起恢复正常营业时间，建议提前3天预约。',
    time: '2024-05-30'
  },
  {
    type: 'info',
    title: '体检项目物价标准更新',
    content: '2024年5月1日起执行最新物价标准，新增"幽门螺杆菌分型检测"项目（单价180元），具体可在"前台服务-项目物价核对"中查询。',
    time: '2024-04-28'
  },
  {
    type: 'danger',
    title: '系统维护通知',
    content: '今日22:00-次日6:00将进行系统维护，维护期间暂停所有服务，请提前做好数据备份与工作安排。',
    time: '2024-05-30'
  }
])

// 示例：实时数据概览（原型静态数据）
const statData = ref({
  todayCompleted: 156,
  currentWaiting: 28,
  monthlyGroup: 892,
  reportRate: 92
})

// 流程节点数据
const processSteps = ref([
  { name: '客户建档', desc: '个人/团检信息录入', icon: 'ep:user-filled', color: 'emerald' },
  { name: '开单预约', desc: '选择套餐/排期', icon: 'ep:document', color: 'blue' },
  { name: '检中执行', desc: '结果录入/标本跟踪', icon: 'ep:monitor', color: 'purple' },
  { name: '报告生成', desc: '总检/签发', icon: 'ep:document-checked', color: 'orange' },
  { name: '报告送达', desc: '线上/线下领取', icon: 'ep:message', color: 'pink' }
])


</script>

<template>
  <div class="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 系统概览 -->
    <div class="mb-6 text-center">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
        健康体检全流程管理平台
      </h1>
      <p class="text-gray-700 dark:text-gray-300 text-lg">以"一横五纵"架构为核心，实现体检业务全链条智能化</p>
    </div>

    <!-- 重要通知和统计概览 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- 公共通知 -->
      <div class="lg:col-span-1">
        <el-card class="border-0 shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm h-full">
          <template #header>
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center">
                <icon icon="ep:bell" class="text-white text-lg" />
              </div>
              <span class="font-semibold text-gray-800 dark:text-gray-200 text-lg">重要通知</span>
            </div>
          </template>

          <div class="space-y-3">
            <div
              v-for="(notice, index) in noticeList.slice(0, 2)"
              :key="notice.title"
              class="p-3 rounded-lg transition-all duration-200 hover:shadow-md"
              :class="[
                notice.type === 'warning' ? 'bg-amber-50 dark:bg-amber-900/20 border-l-4 border-amber-400' :
                notice.type === 'info' ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400' :
                'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400'
              ]"
            >
              <div class="flex items-start space-x-3">
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center shadow-sm flex-shrink-0"
                  :class="[
                    notice.type === 'warning' ? 'bg-amber-400' :
                    notice.type === 'info' ? 'bg-blue-400' :
                    'bg-red-400'
                  ]"
                >
                  <icon
                    :icon="notice.type === 'warning' ? 'ep:warning' : notice.type === 'info' ? 'ep:info-filled' : 'ep:warning-filled'"
                    class="text-white text-sm"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="font-medium text-gray-800 dark:text-gray-200 text-sm">{{ notice.title }}</p>
                  <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{{ notice.content }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-500 mt-2">{{ notice.time }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 统计概览 -->
      <div class="lg:col-span-2">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
          <!-- 今日已完成体检 -->
          <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
                  {{ statData.todayCompleted }}
                </div>
                <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
                  今日已完成体检
                </div>
              </div>
              <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
                <icon icon="ep:user-filled" class="text-white text-2xl" />
              </div>
            </div>
            <div class="text-xs text-emerald-600 dark:text-emerald-400 mt-3 bg-emerald-200/50 dark:bg-emerald-800/50 px-2 py-1 rounded-full inline-block">
              较昨日 +12%
            </div>
          </el-card>

          <!-- 当前待检人数 -->
          <el-card class="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-amber-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-3xl font-bold text-amber-600 dark:text-amber-400">
                  {{ statData.currentWaiting }}
                </div>
                <div class="text-sm text-amber-700 dark:text-amber-300 mt-1 font-medium">
                  当前待检人数
                </div>
              </div>
              <div class="w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-2xl flex items-center justify-center shadow-lg">
                <icon icon="ep:clock" class="text-white text-2xl" />
              </div>
            </div>
            <div class="text-xs text-amber-700 dark:text-amber-400 mt-3 bg-amber-200/50 dark:bg-amber-800/50 px-2 py-1 rounded-full inline-block">
              检验科待检较多
            </div>
          </el-card>

          <!-- 本月团检人数 -->
          <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {{ statData.monthlyGroup }}
                </div>
                <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
                  本月团检人数
                </div>
              </div>
              <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
                <icon icon="ep:office-building" class="text-white text-2xl" />
              </div>
            </div>
            <div class="text-xs text-blue-600 dark:text-blue-400 mt-3 bg-blue-200/50 dark:bg-blue-800/50 px-2 py-1 rounded-full inline-block">
              较上月 +8%
            </div>
          </el-card>

          <!-- 报告送达率 -->
          <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {{ statData.reportRate }}%
                </div>
                <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
                  报告送达率
                </div>
              </div>
              <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-2xl flex items-center justify-center shadow-lg">
                <icon icon="ep:message" class="text-white text-2xl" />
              </div>
            </div>
            <div class="text-xs text-purple-700 dark:text-purple-400 mt-3 bg-purple-200/50 dark:bg-purple-800/50 px-2 py-1 rounded-full inline-block">
              线上推送占比68%
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 流程概览 -->
    <el-card class="mb-6 shadow-lg border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
      <template #header>
        <div class="flex items-center space-x-2">
          <div class="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          <span class="font-semibold text-gray-800 dark:text-gray-200">业务流程概览</span>
        </div>
      </template>

      <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
        <div v-for="(step, index) in processSteps" :key="step.name" class="flex items-center">
          <div class="text-center">
            <div
              class="w-14 h-14 rounded-full flex items-center justify-center mx-auto mb-2 shadow-lg"
              :class="[
                step.color === 'emerald' ? 'bg-gradient-to-br from-emerald-400 to-emerald-600' :
                step.color === 'blue' ? 'bg-gradient-to-br from-blue-400 to-blue-600' :
                step.color === 'purple' ? 'bg-gradient-to-br from-purple-400 to-purple-600' :
                step.color === 'orange' ? 'bg-gradient-to-br from-orange-400 to-orange-600' :
                'bg-gradient-to-br from-pink-400 to-pink-600'
              ]"
            >
              <icon :icon="step.icon" class="text-white text-xl" />
            </div>
            <p class="text-sm font-semibold text-gray-800 dark:text-gray-200">{{ step.name }}</p>
            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ step.desc }}</p>
          </div>
          <icon
            v-if="index < processSteps.length - 1"
            icon="ep:arrow-right"
            class="text-gray-400 dark:text-gray-500 mx-4 hidden md:block text-xl"
          />
        </div>
      </div>
    </el-card>


  </div>
</template>
