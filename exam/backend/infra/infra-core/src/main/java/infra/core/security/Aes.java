package infra.core.security;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES 对称加密工具类 (CBC模式)
 * 线程安全：每次加密使用独立 Cipher 实例
 */
public final class Aes {
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16; // 固定16字节
    private static final int AES_256_KEY_LENGTH = 32; // 256位=32字节
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private Aes() {
    }

    /**
     * 生成 AES-256 密钥 (Base64编码)
     */
    public static String generateKey() {
        byte[] key = new byte[AES_256_KEY_LENGTH];
        SECURE_RANDOM.nextBytes(key);
        return Base64.getEncoder().encodeToString(key);
    }

    /**
     * 加密 (返回Base64字符串，格式: IV + 密文)
     *
     * @param plaintext 明文
     * @param base64Key Base64编码的AES密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plaintext, String base64Key) {
        if (plaintext == null || base64Key == null) {
            throw new AesCryptoException("明文和密钥均不可为空");
        }

        try {
            byte[] iv = generateIv();
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return encodeResult(iv, ciphertext);
        } catch (Exception e) {
            throw new AesCryptoException("加密失败", e);
        }
    }

    /**
     * 确定性加密
     * 相同明文总是产生相同密文，但安全性较低
     *
     * @param plaintext 明文
     * @param base64Key Base64编码的AES密钥
     * @return 加密后的Base64字符串
     */
    public static String encryptDeterministic(String plaintext, String base64Key) {
        if (plaintext == null || base64Key == null) {
            throw new AesCryptoException("明文和密钥均不可为空");
        }

        try {
            // 使用基于明文和密钥的确定性 IV
            byte[] deterministicIv = generateDeterministicIv(plaintext, base64Key);
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new IvParameterSpec(deterministicIv));

            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return encodeResult(deterministicIv, ciphertext);
        } catch (Exception e) {
            throw new AesCryptoException("加密失败", e);
        }
    }

    /**
     * 解密
     *
     * @param base64Ciphertext Base64格式的加密数据 (IV + 密文)
     * @param base64Key        Base64编码的AES密钥
     * @return 解密后的明文
     */
    public static String decrypt(String base64Ciphertext, String base64Key) {
        try {
            byte[] combined = Base64.getDecoder().decode(base64Ciphertext);
            byte[] iv = extractIv(combined);
            byte[] ciphertext = extractCiphertext(combined, iv.length);
            SecretKey secretKey = parseKey(base64Key);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));

            byte[] plaintext = cipher.doFinal(ciphertext);
            return new String(plaintext, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new AesCryptoException("解密失败", e);
        }
    }

    // 生成随机IV
    private static byte[] generateIv() {
        byte[] iv = new byte[IV_LENGTH];
        SECURE_RANDOM.nextBytes(iv);
        return iv;
    }

    /**
     * 生成确定性 IV
     */
    private static byte[] generateDeterministicIv(String plaintext, String key) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(plaintext.getBytes(StandardCharsets.UTF_8));
            digest.update(key.getBytes(StandardCharsets.UTF_8));
            byte[] hash = digest.digest();

            // 取前16字节作为IV
            byte[] iv = new byte[IV_LENGTH];
            System.arraycopy(hash, 0, iv, 0, IV_LENGTH);
            return iv;
        } catch (Exception e) {
            throw new AesCryptoException("生成确定性IV失败", e);
        }
    }

    // 解析Base64密钥
    private static SecretKey parseKey(String base64Key) {
        byte[] keyBytes = Base64.getDecoder().decode(base64Key);
        if (keyBytes.length != AES_256_KEY_LENGTH) {
            throw new AesCryptoException("无效密钥长度，必须为256位");
        }
        return new SecretKeySpec(keyBytes, "AES");
    }

    // 编码结果 (IV + 密文)
    private static String encodeResult(byte[] iv, byte[] ciphertext) {
        byte[] combined = new byte[iv.length + ciphertext.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(ciphertext, 0, combined, iv.length, ciphertext.length);
        return Base64.getEncoder().encodeToString(combined);
    }

    // 提取IV
    private static byte[] extractIv(byte[] combined) {
        byte[] iv = new byte[IV_LENGTH];
        System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
        return iv;
    }

    // 提取密文
    private static byte[] extractCiphertext(byte[] combined, int ivLength) {
        byte[] ciphertext = new byte[combined.length - ivLength];
        System.arraycopy(combined, ivLength, ciphertext, 0, ciphertext.length);
        return ciphertext;
    }

    // 自定义加密异常
    public static class AesCryptoException extends RuntimeException {
        public AesCryptoException(String message) {
            super(message);
        }

        public AesCryptoException(String message, Throwable cause) {
            super(message,  cause);
        }
    }
}