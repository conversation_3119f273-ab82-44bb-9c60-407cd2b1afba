import { get, post } from '~/utils/request'

const baseUrl = '/sys/perm'

/**
 * 用户列表
 */
export function userList(params: any): Promise<any> {
  return get<any>(`${baseUrl}/userList`, params)
}

/**
 * 编辑岗位权限(获取)
 */
export function getPostPerm(id: number): Promise<any> {
  return get<any>(`${baseUrl}/post`, {
    id,
  })
}

/**
 * 编辑岗位权限(提交)
 */
export function postPostPerm(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/post`, data, {
    id,
  })
}

/**
 * 用户权限(获取)
 */
export function getUserPerm(id: number): Promise<any> {
  return get<any>(`${baseUrl}/user`, {
    id,
  })
}

/**
 * 用户权限(提交)
 */
export function postUserPerm(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/user`, data, {
    id,
  })
}
