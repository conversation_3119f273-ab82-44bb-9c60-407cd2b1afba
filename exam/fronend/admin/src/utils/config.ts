import type { AppConfig } from '~/types/config'
import { defaultConfig } from '~/types/config'

const config = ref({ ...defaultConfig })
let isLoaded = false

// 从服务端加载配置
export async function loadConfig(): Promise<void> {
  if (!config.value.apiUrl) {
    return Promise.reject(new Error('尚未调用initConfig初始化配置'))
  }

  const res = await fetch(`${config.value.apiUrl}/sys/common/setting`)
  if (res.ok) {
    const json = await res.json()
    if (json.code === 0 && json.data) {
      config.value = { ...config.value, ...json.data }
    }
  }
}

/**
 * 初始化配置，从 config.json 中读取配置，在初始化应用时调用
 */
export async function initConfig(): Promise<AppConfig> {
  if (!isLoaded) {
    const response = await fetch('config.json')
    if (response.ok) {
      const json = await response.json()
      config.value = { ...config.value, ...json }
      // 从服务端加载配置
      await loadConfig()
      isLoaded = true
    }
  }

  return config.value
}

/**
 * 获取配置
 */
export function getConfig(): AppConfig {
  return config.value
}
