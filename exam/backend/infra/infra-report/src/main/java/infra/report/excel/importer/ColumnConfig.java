package infra.report.excel.importer;

import java.util.function.BiConsumer;

/**
 * Excel 导入列配置
 * 
 * @param <T> 目标对象类型
 */
public record ColumnConfig<T>(
        String header,
        ExcelParser<Object> parser,
        BiConsumer<T, Object> setter,
        boolean required,
        Object defaultValue
) {

    /**
     * 创建带解析器的列配置
     */
    public static <T, R> ColumnConfig<T> of(String header, ExcelParser<R> parser, BiConsumer<T, R> setter) {
        @SuppressWarnings("unchecked")
        ExcelParser<Object> objectParser = (ExcelParser<Object>) parser;
        @SuppressWarnings("unchecked")
        BiConsumer<T, Object> objectSetter = (BiConsumer<T, Object>) setter;
        return new ColumnConfig<>(header, objectParser, objectSetter, false, null);
    }
    
    /**
     * 创建必填列配置
     */
    public static <T, R> ColumnConfig<T> required(String header, ExcelParser<R> parser, BiConsumer<T, R> setter) {
        @SuppressWarnings("unchecked")
        ExcelParser<Object> objectParser = (ExcelParser<Object>) parser;
        @SuppressWarnings("unchecked")
        BiConsumer<T, Object> objectSetter = (BiConsumer<T, Object>) setter;
        return new ColumnConfig<>(header, objectParser, objectSetter, true, null);
    }
    
    /**
     * 创建带默认值的列配置
     */
    public static <T, R> ColumnConfig<T> withDefault(String header, ExcelParser<R> parser,
                                                     BiConsumer<T, R> setter, R defaultValue) {
        @SuppressWarnings("unchecked")
        ExcelParser<Object> objectParser = (ExcelParser<Object>) parser;
        @SuppressWarnings("unchecked")
        BiConsumer<T, Object> objectSetter = (BiConsumer<T, Object>) setter;
        return new ColumnConfig<>(header, objectParser, objectSetter, false, defaultValue);
    }
    
    /**
     * 设置为必填
     */
    public ColumnConfig<T> asRequired() {
        return new ColumnConfig<>(header, parser, setter, true, defaultValue);
    }
    
    /**
     * 设置默认值
     */
    public ColumnConfig<T> defaultValue(Object defaultValue) {
        return new ColumnConfig<>(header, parser, setter, required, defaultValue);
    }
    
    /**
     * 解析并设置值
     */
    public void parseAndSet(T target, Object cellValue, int rowIndex) {
        try {
            Object parsedValue;
            
            if (cellValue == null || cellValue.toString().trim().isEmpty()) {
                if (required) {
                    throw new ExcelParser.ExcelParseException(
                            String.format("第%d行，列'%s'为必填项，不能为空", rowIndex + 1, header));
                }
                parsedValue = defaultValue;
            } else {
                parsedValue = parser.apply(cellValue);
                if (parsedValue == null && required) {
                    throw new ExcelParser.ExcelParseException(
                            String.format("第%d行，列'%s'为必填项，解析结果为空", rowIndex + 1, header));
                }
            }
            
            if (parsedValue != null) {
                setter.accept(target, parsedValue);
            }
        } catch (Exception e) {
            if (e instanceof ExcelParser.ExcelParseException) {
                throw e;
            }
            throw new ExcelParser.ExcelParseException(
                    String.format("第%d行，列'%s'解析失败: %s", rowIndex + 1, header, e.getMessage()), e);
        }
    }
}
