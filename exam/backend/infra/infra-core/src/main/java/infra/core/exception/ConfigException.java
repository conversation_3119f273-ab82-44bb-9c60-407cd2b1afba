package infra.core.exception;

/**
 * 配置异常
 * <p>
 * 用于处理配置相关的异常情况，如配置缺失、格式错误、加载失败等。
 * 此异常为运行时异常，通常在应用启动或配置刷新时抛出。
 */
public class ConfigException extends RuntimeException {
    /**
     * 使用异常消息创建配置异常
     *
     * @param message 异常消息
     */
    public ConfigException(String message) {
        super(message);
    }

    /**
     * 使用异常消息和原因创建配置异常
     *
     * @param message 异常消息
     * @param cause   原始异常
     */
    public ConfigException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 使用原因创建配置异常
     *
     * @param cause 原始异常
     */
    public ConfigException(Throwable cause) {
        super(cause);
    }
}
