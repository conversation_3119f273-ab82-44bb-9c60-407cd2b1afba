package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.PermDto;
import module.sys.admin.dto.UserDto;
import module.sys.entity.Dept;
import module.sys.entity.Post;
import module.sys.entity.UserDept;
import module.sys.service.*;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import module.sys.entity.UserPerm;

@Tag(name = "权限管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/perm")
public class PermController {
    private final UserService userService;
    private final PostService postService;
    private final DeptService deptService;
    private final UserDeptService userDeptService;
    private final AuthUserService authUserService;

    @Operation(summary = "账户列表")
    @GetMapping("/userList")
    @Perm("sys:perm")
    public Result<PageResult<UserDto.InfoDto>> list(
            String type,
            String key,
            Long deptId,
            Long postId,
            PageParam pageParam) {

        // 根据部门或岗位过滤用户ID
        List<Long> userIds;
        if (deptId != null) {
            List<Long> deptIds = deptService.getAllChildrenIds(deptId, true);
            if (!deptIds.isEmpty()) {
                userIds = userDeptService.getByDepts(deptIds)
                        .stream()
                        .map(UserDept::getUserId)
                        .toList();
            } else {
                return Result.okData(PageResult.empty());
            }
        } else if (postId != null) {
            userIds = userDeptService.getByPosts(List.of(postId))
                    .stream()
                    .map(UserDept::getUserId)
                    .toList();
        } else {
            userIds = null;
        }

        if (userIds != null && userIds.isEmpty()) {
            return Result.okData(PageResult.empty());
        }

        var result = userService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("userName".equals(type)) {
                    w.and(w1 -> w1.like("user_name", keyTrim)
                            .or().like("py", keyTrim));
                }
                w.like("loginName".equals(type), "login_name", keyTrim);
                w.like("phone".equals(type), "phone", keyTrim);
                w.like("email".equals(type), "email", keyTrim);
            }
            w.in(userIds != null, "id", userIds);
        }, pageParam);

        return Result.okData(result.to(UserDto.InfoDto.class));
    }

    @Operation(summary = "获取岗位权限")
    @GetMapping("/post")
    @Perm("sys:perm:post")
    public Result<PermDto.EditPostPerm> getPostPerm(Long id) {
        var postOpt = postService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (postOpt.isEmpty()) {
            return Result.fail("岗位不存在");
        }
        Set<String> perms = postService.getPostPerms(id);
        return Result.okData(new PermDto.EditPostPerm(
                postOpt.get().getPerm(),
                perms
        ));
    }

    @Operation(summary = "设置岗位权限")
    @PostMapping("/post")
    @AuditLog(code = "sys:perm:post", value = "设置岗位权限")
    @Perm("sys:perm:post")
    public Result<Void> setPostPerm(@RequestParam Long id, @RequestBody PermDto.EditPostPerm perm) {
        var postOpt = postService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (postOpt.isEmpty()) {
            return Result.fail("岗位不存在");
        }

        postService.updatePostPerms(id, perm.getPerms());
        var post = postOpt.get();
        post.setPerm(perm.getData());
        postService.update(post);

        // 清除岗位所有用户权限缓存数据
        var users = userDeptService.getByPosts(List.of(id));
        users.forEach(item -> authUserService.clearAuthCache(item.getUserId()));
        return Result.OK;
    }

    @Operation(summary = "获取用户权限")
    @GetMapping("/user")
    @Perm("sys:perm:user")
    public Result<PermDto.UserPermInfo> getUserPerm(@RequestParam Long id) {
        var result = new PermDto.UserPermInfo();

        // 获取用户所有岗位
        List<UserDept> userDepts = userDeptService.getDepts(id)
                .stream().filter(w -> w.getPostId() != null)
                .sorted((a, b) -> {
                    if (a.getMain() && !b.getMain()) return -1;
                    if (!a.getMain() && b.getMain()) return 1;
                    return Long.compare(a.getId(), b.getId());
                })
                .toList();

        // 岗位仅限数据
        if (!userDepts.isEmpty()) {
            result.setPosts(
                    userDepts.stream().map(item ->
                            deptService.getById(item.getDeptId()).map(Dept::getName).orElse("") + "-" +
                                    postService.getById(item.getPostId()).map(Post::getName).orElse("")
                    ).toList()
            );

            List<Long> postIds = userDepts.stream()
                    .map(UserDept::getPostId)
                    .toList();

            result.setPostPerms(postService.getPostsPerms(postIds));
        }

        // 获取用户特殊权限
        var userPerms = userService.getSpecialPerms(id);
        result.setAllows(userPerms.stream().filter(w -> w.getType() == UserPerm.UserPermType.ALLOW).map(UserPerm::getPerm).collect(Collectors.toSet()));
        result.setDenys(userPerms.stream().filter(w -> w.getType() == UserPerm.UserPermType.DENY).map(UserPerm::getPerm).collect(Collectors.toSet()));

        return Result.okData(result);
    }

    @Operation(summary = "设置用户权限")
    @PostMapping("/user")
    @AuditLog(code = "sys:perm:user", value = "设置用户权限")
    @Perm("sys:perm:user")
    public Result<Void> setUserPerm(@RequestParam Long id, @RequestBody PermDto.EditUserPerm perm) {
        userService.updateSpecialPerms(id, perm.getAllows(), perm.getDenys());
        authUserService.clearAuthCache(id);
        return Result.OK;
    }
}
