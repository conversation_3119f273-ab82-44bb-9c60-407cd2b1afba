import { get, post } from '~/utils/request'

const baseUrl = '/sys/job'

/**
 * 任务列表
 */
export function list(): Promise<any> {
  return get<any>(`${baseUrl}/list`)
}

/**
 * 执行任务
 */
export function run(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/run`, ids)
}

/**
 * 启用任务
 */
export function enable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/enable`, ids)
}

/**
 * 禁用任务
 */
export function disable(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/disable`, ids)
}

/**
 * 日志列表
 */
export function log(params: any): Promise<any> {
  return get<any>(`${baseUrl}/log`, params)
}
