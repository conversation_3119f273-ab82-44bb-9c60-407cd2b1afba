package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.core.text.Chinese;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.ExamCombo;
import module.sys.mapper.ExamComboMapper;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * 体检套餐服务
 */
@Slf4j
@Service
public class ExamComboService extends ServiceBase<ExamComboMapper, ExamCombo> {
    @Override
    protected Function<QueryWrapper<ExamCombo>, QueryWrapper<ExamCombo>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<ExamCombo>, QueryWrapper<ExamCombo>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(ExamCombo entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);
        setPinyin(entity);
    }

    /**
     * 设置拼音
     */
    private void setPinyin(ExamCombo examCombo) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(examCombo.getName());
        examCombo.setPy(pinyin.py());
    }
}
