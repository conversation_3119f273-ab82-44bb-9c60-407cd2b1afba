package module.sys.service;

import infra.domain.service.ServiceBase;
import infra.task.core.IJob;
import infra.task.core.IJobService;
import infra.task.core.JobRunFlag;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Job;
import module.sys.entity.JobLog;
import module.sys.mapper.JobMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Optional;

@Slf4j
@Service
public class JobService extends ServiceBase<JobMapper, Job> implements IJobService {
    @Autowired
    private JobLogService jobLogService;

    /**
     * 注册任务
     */
    @Override
    @Transactional
    public List<JobRunFlag> register(List<IJob> jobs) {
        List<JobRunFlag> runFlags = new ArrayList<>();

        for (IJob job : jobs) {
            Optional<Job> existingJob = getByJobId(job.getJobId());
            Job entity;

            if (existingJob.isPresent()) {
                entity = existingJob.get();
                // 检查配置是否有变化
                if (!job.getCron().equals(entity.getCron()) ||
                        !job.getCronDesc().equals(entity.getCronDesc()) ||
                        !job.getSort().equals(entity.getSort()) ||
                        !job.getJobDesc().equals(entity.getJobDesc())) {

                    entity.setCron(job.getCron());
                    entity.setCronDesc(job.getCronDesc());
                    entity.setSort(job.getSort());
                    entity.setJobDesc(job.getJobDesc());
                    update(entity);
                }
            } else {
                entity = new Job();
                entity.setJobId(job.getJobId());
                entity.setCron(job.getCron());
                entity.setJobDesc(job.getJobDesc());
                entity.setCronDesc(job.getCronDesc());
                entity.setSort(job.getSort());
                add(entity);
            }

            runFlags.add(getJobRunFlag(entity));
        }

        return runFlags;
    }

    /**
     * 尝试执行任务
     */
    @Override
    @Transactional
    public JobRunFlag tryStart(String jobId) {
        Job entity = getByJobId(jobId).orElseThrow(() -> new RuntimeException("任务不存在"));

        // 检查任务是否超时
        if (entity.getStatus() == Job.JobStatus.RUNNING) {
            LocalDateTime nextTime = calculateNextRunTime(entity.getCron(), entity.getRunLastTime());
            for (int i = 0; i < 3; i++) {
                nextTime = calculateNextRunTime(entity.getCron(), nextTime);
            }
            if (LocalDateTime.now().isAfter(nextTime)) {
                entity.setStatus(Job.JobStatus.WAITING);
            }
        }

        JobRunFlag runFlag = getJobRunFlag(entity);
        if (runFlag.nextRunTime() == null) {
            entity.setStatus(Job.JobStatus.RUNNING);
            entity.setRunCount(entity.getRunCount() + 1);
            entity.setRunLastTime(LocalDateTime.now());

            boolean updateSuccess = update(entity);
            // 并发锁冲突，其它实例已执行，重新获取
            if (!updateSuccess) {
                entity = getByJobId(jobId).orElseThrow(() -> new RuntimeException("任务不存在"));
                return getJobRunFlag(entity);
            }
        }

        return runFlag;
    }

    /**
     * 运行任务结束
     */
    @Override
    @Transactional
    public JobRunFlag runEnd(String jobId, boolean success, String msg) {
        Job entity = getByJobId(jobId).orElseThrow(() -> new RuntimeException("任务不存在"));

        if (entity.getStatus() == Job.JobStatus.RUNNING) {
            entity.setStatus(Job.JobStatus.WAITING);
            entity.setNextRunTime(calculateNextRunTime(entity.getCron(), entity.getRunLastTime()));
        }

        if (success) {
            entity.setRunSuccess(entity.getRunSuccess() + 1);
        } else {
            entity.setRunFail(entity.getRunFail() + 1);
        }

        update(entity);

        // 记录执行日志
        JobLog jobLog = new JobLog();
        jobLog.setJobId(jobId);
        jobLog.setStartTime(entity.getRunLastTime());
        jobLog.setEndTime(LocalDateTime.now());
        jobLog.setSuccess(success);
        jobLog.setUseTime(calculateDuration(entity.getRunLastTime(), LocalDateTime.now()));
        jobLog.setMsg(msg);
        jobLogService.add(jobLog);

        return getJobRunFlag(entity);
    }


    /**
     * 根据jobId获取job
     */
    public Optional<Job> getByJobId(String jobId) {
        return getFirst(w -> w.eq("job_id", jobId));
    }

    /**
     * 获取任务运行标识
     */
    private JobRunFlag getJobRunFlag(Job job) {
        LocalDateTime now = LocalDateTime.now();

        if (job.getStatus() == Job.JobStatus.STOP) {
            LocalDateTime nextTime = calculateNextRunTime(job.getCron(), now);
            return new JobRunFlag(job.getJobId(), nextTime);
        }

        if (job.getStatus() == Job.JobStatus.RUNNING) {
            LocalDateTime nextTime = calculateNextRunTime(job.getCron(), job.getRunLastTime());
            return new JobRunFlag(job.getJobId(), nextTime);
        }

        LocalDateTime nextTime = calculateNextRunTime(job.getCron(), job.getRunLastTime() == null ? LocalDateTime.MIN : job.getRunLastTime());
        return new JobRunFlag(job.getJobId(), now.isAfter(nextTime) ? null : nextTime);
    }

    /**
     * 计算下次运行时间
     */
    private LocalDateTime calculateNextRunTime(String cron, LocalDateTime baseTime) {
        try {
            CronExpression cronExpression = CronExpression.parse(cron);
            return cronExpression.next(baseTime);
        } catch (Exception e) {
            log.error("解析cron表达式失败: {}", cron, e);
            return baseTime.plusMinutes(1);
        }
    }

    /**
     * 计算两个时间间隔
     */
    private long calculateDuration(LocalDateTime start, LocalDateTime end) {
        return  Math.max(Duration.between(start, end).toMillis(), 1);
    }
}
