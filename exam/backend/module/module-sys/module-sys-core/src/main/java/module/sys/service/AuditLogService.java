package module.sys.service;

import infra.audit.core.OpInfo;
import infra.audit.core.IAuditLogger;
import infra.core.common.ObjectUtil;
import infra.core.text.JSON;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.AuditLog;
import module.sys.mapper.AuditLogMapper;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 审计日志服务
 */
@Slf4j
@Service
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
public class AuditLogService extends ServiceBase<AuditLogMapper, AuditLog> implements IAuditLogger {
    @Async("auditTaskExecutor")
    @Override
    public void log(OpInfo opInfo) {
        AuditLog auditLog = ObjectUtil.copyTo(opInfo, new AuditLog(), "data");
        if (opInfo.getData() != null) {
            auditLog.setData(JSON.toJson(opInfo.getData()));
        }
        add(auditLog);
    }

    /**
     * 清理xx以前的日志
     * @param before xx以前
     */
    public int clearBefore(LocalDateTime before) {
        return deleteBatch(w -> w.lt("start_time", before));
    }
}
