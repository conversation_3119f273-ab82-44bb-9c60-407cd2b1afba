package infra.auth.core;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Token管理器
 */
public interface ITokenManager {
    /**
     * 创建Token
     */
    void createToken(Long userId, String accessToken, String refreshToken, HttpServletRequest request);

    /**
     * 验证Token
     */
    boolean validAccessToken(Long userId, String accessToken, HttpServletRequest request);

    /**
     * 刷新Token
     */
    boolean useRefreshToken(Long userId, String refreshToken, HttpServletRequest request);

    /**
     * 登出
     */
    void logout(Long userId, String accessToken);
}
