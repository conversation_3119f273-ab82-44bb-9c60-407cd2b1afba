package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 样本类型
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_sample_type")
public class SampleType extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 样本类型名称
    private String name;

    // 样本类型缩写
    private String shortName;

    // 拼音
    private String py;

    // 样本类型代码
    private String code;

    // 样本采集科室id
    private Long deptId;

    // 排序值
    @Builder.Default
    private Integer sort = 0;

    // 禁用
    private Boolean disabled;
}