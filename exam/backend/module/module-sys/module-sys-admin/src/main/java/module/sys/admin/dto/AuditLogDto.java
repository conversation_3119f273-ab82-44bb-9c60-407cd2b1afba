package module.sys.admin.dto;

import jakarta.validation.constraints.*;
import lombok.*;
import java.time.LocalDateTime;

/**
 * 系统审核日志Dto
 */
public class AuditLogDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 
        private String module;
        // 
        private String code;
        // 
        private String detail;
        // 
        private Long userId;
        // 
        private String loginName;
        // 
        private String userName;
        // 
        private String url;
        // 
        private String ip;
        // 
        private String data;
        // 
        private Boolean success;
        // 
        private String error;
        // 
        private LocalDateTime startTime;
        // 
        private Long useTime;
    }    
}