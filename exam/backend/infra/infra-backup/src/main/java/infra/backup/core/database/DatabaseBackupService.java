package infra.backup.core.database;

import infra.backup.config.BackupConfigProperties;
import infra.backup.core.database.impl.MySqlBackupService;
import infra.backup.exception.BackupException;
import infra.core.file.Zip;
import infra.oss.core.IStorage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 数据库备份服务
 */
@Slf4j
public class DatabaseBackupService {

    private final BackupConfigProperties backupConfigProperties;
    private final IStorage storage;
    private final List<IDatabaseBackup> databaseBackups;

    private static final DateTimeFormatter PATH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");
    private static final DateTimeFormatter FILE_FORMATTER = DateTimeFormatter.ofPattern("ddHHmm");

    public DatabaseBackupService(BackupConfigProperties backupConfigProperties, IStorage storage) {
        this.backupConfigProperties = backupConfigProperties;
        this.storage = storage;

        // 初始化数据库备份实现
        this.databaseBackups = initializeDatabaseBackups();
    }

    /**
     * 初始化数据库备份实现
     */
    private List<IDatabaseBackup> initializeDatabaseBackups() {
        // 目前只支持MySQL，未来可以根据配置动态加载其他数据库备份服务
        MySqlBackupService mySqlBackupService = new MySqlBackupService();
        return List.of(mySqlBackupService);
    }

    /**
     * 执行数据库备份
     */
    public String backupDatabase() {
        log.info("开始执行数据库备份任务");

        BackupConfigProperties.DatabaseConfig databaseConfig = backupConfigProperties.getDatabase();
        if (!Boolean.TRUE.equals(databaseConfig.getEnable())) {
            log.info("数据库备份未启用");
            return "数据库备份未启用";
        }

        if (CollectionUtils.isEmpty(databaseConfig.getDs())) {
            log.info("未配置备份数据库连接信息");
            return "未配置备份数据库连接信息";
        }

        LocalDateTime now = LocalDateTime.now();
        for (BackupConfigProperties.ConnectionConfig connectionConfig : databaseConfig.getDs()) {
            backupSingleDatabase(connectionConfig, now);
        }

        log.info("数据库备份任务完成");
        return "数据库备份任务完成";
    }

    /**
     * 备份单个数据库
     */
    private void backupSingleDatabase(BackupConfigProperties.ConnectionConfig connectionConfig, LocalDateTime backupTime) {
        log.info("开始备份数据库: {}", connectionConfig.getUrl());

        File sqlFile = null;
        File compressedFile = null;

        try {
            // 执行数据库备份到临时文件
            sqlFile = backupToFile(connectionConfig);

            // 使用ZIP压缩备份文件
            compressedFile = compressBackup(sqlFile, backupTime);

            // 上传到OSS
            String backupPath = generateBackupPath(backupTime);
            try (FileInputStream uploadStream = new FileInputStream(compressedFile)) {
                storage.upload(uploadStream, backupPath);
            }

            log.info("数据库备份成功，压缩文件大小: {} bytes，上传路径: {}",
                    compressedFile.length(), backupPath);

        } catch (Exception e) {
            log.error("数据库备份失败: {}", connectionConfig.getUrl(), e);
            throw new BackupException("数据库备份失败: " + e.getMessage(), e);
        } finally {
            // 清理临时文件
            cleanupTempFile(sqlFile);
            cleanupTempFile(compressedFile);
        }
    }

    /**
     * 备份数据库到临时文件
     *
     * @param connectionConfig 连接配置
     * @return 备份文件，调用方负责删除
     * @throws BackupException 备份失败时抛出
     */
    private File backupToFile(BackupConfigProperties.ConnectionConfig connectionConfig) {
        for (IDatabaseBackup databaseBackup : databaseBackups) {
            if (databaseBackup.supports(connectionConfig.getUrl())) {
                return databaseBackup.backup(connectionConfig);
            }
        }

        throw new BackupException("不支持的数据库类型: " + connectionConfig.getUrl());
    }

    /**
     * 压缩备份文件
     * 使用ZIP格式，支持密码保护
     */
    private File compressBackup(File sqlFile, LocalDateTime backupTime) {
        BackupConfigProperties.DatabaseConfig databaseConfig = backupConfigProperties.getDatabase();
        String password = StringUtils.hasText(databaseConfig.getCompressPassword()) ?
                databaseConfig.getCompressPassword() : null;

        try {
            return Zip.compress(List.of(sqlFile), password);
        } catch (Exception e) {
            throw new BackupException("压缩备份文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成备份文件路径
     * 路径格式：{ossPath}/{yyyyMM}/{ddHHmm}.bak
     */
    private String generateBackupPath(LocalDateTime backupTime) {
        BackupConfigProperties.DatabaseConfig databaseConfig = backupConfigProperties.getDatabase();
        String basePath = StringUtils.hasText(databaseConfig.getOssPath()) ?
                databaseConfig.getOssPath() : "backup/database";

        // 确保路径以/结尾
        if (!basePath.endsWith("/")) {
            basePath += "/";
        }

        // 生成路径：basePath + yyyyMM/ + ddHHmm.bak
        String yearMonth = backupTime.format(PATH_FORMATTER);
        String fileName = backupTime.format(FILE_FORMATTER) + ".bak";

        return basePath + yearMonth + "/" + fileName;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            boolean deleted = file.delete();
            if (deleted) {
                log.debug("清理临时文件: {}", file.getAbsolutePath());
            } else {
                log.warn("无法删除临时文件: {}", file.getAbsolutePath());
            }
        }
    }
}