package basic.configure.web;

import infra.oss.config.OssConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web相关配置
 */
@AutoConfiguration
@ConditionalOnWebApplication
@EnableConfigurationProperties(CorsConfigProperties.class)
@Slf4j
public class WebAutoConfiguration {
    /**
     * 注册全局异常处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public GlobalExceptionHandler basicGlobalExceptionHandler() {
        log.info("[配置] 注册全局异常处理器");
        return new GlobalExceptionHandler();
    }

    /**
     * 注册CORS跨域配置
     */
    @Bean
    @ConditionalOnMissingBean(name = "corsWebMvcConfigurer")
    @ConditionalOnProperty(prefix = "app.cors", name = "enabled", havingValue = "true")
    public WebMvcConfigurer basicCorsWebMvcConfigurer(CorsConfigProperties config) {
        log.info("[配置] 启用CORS跨域配置");
        return new CorsWebMvcConfigurer(config);
    }

    /**
     * 配置 LocalOSS Web支持
     */
    @Bean
    @ConditionalOnProperty(prefix = "app.oss", name = "provider", havingValue = "local")
    public WebMvcConfigurer basicLocalOssWebConfigurer(OssConfigProperties config) {
        log.info("[配置] 配置Local OSS Web支持");
        return new LocalOssWebConfigurer(config);
    }

    /**
     * 配置UTF-8
     */
    @Bean
    public CharacterEncodingFilter basicCharacterEncodingFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        filter.setEncoding("UTF-8");
        filter.setForceEncoding(true);
        return filter;
    }
}
