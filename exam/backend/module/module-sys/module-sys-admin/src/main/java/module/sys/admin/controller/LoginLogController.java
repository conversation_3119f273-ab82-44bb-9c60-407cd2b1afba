package module.sys.admin.controller;

import infra.auth.annotation.Perm;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.LoginLog;
import module.sys.service.LoginLogService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@Tag(name = "登录日志")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/loginLog")
public class LoginLogController {

    private final LoginLogService loginLogService;

    @Operation(summary = "查询登录日志列表")
    @GetMapping("/list")
    @Perm("sys:loginLog")
    public Result<PageResult<LoginLog>> list(String type,
                                             String key,
                                             Boolean status,
                                             LocalDate startTime,
                                             LocalDate endTime,
                                             PageParam pageParam) {
        PageResult<LoginLog> result = loginLogService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("userName".equals(type)) {
                    w.and(w1 -> w1.like("user_name", keyTrim).or().like("login_name", keyTrim));
                }
                w.like("ip".equals(type), "ip", keyTrim);
            }
            w.eq(status != null, "success", status);
            w.ge(startTime != null, "login_time", startTime);
            w.le(endTime != null, "login_time", endTime);
        }, pageParam);
        return Result.okData(result);
    }
}
