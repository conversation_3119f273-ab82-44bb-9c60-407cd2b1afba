<script setup lang="ts">
import { ref } from 'vue'

definePage({
  meta: {
    title: '业务管理',
    menu: true,
    order: -2,
    icon: 'hugeicons:analysis-text-link',
  },
})

// 业务管理统计数据
const deptStats = ref({
  decision: {
    reports: 28,
    insights: 156,
    efficiency: 92.3,
    alerts: 5
  },
  staff: {
    totalStaff: 89,
    highPerformers: 23,
    avgScore: 87.5,
    trainingNeeded: 12
  },
  project: {
    totalProjects: 245,
    activePackages: 68,
    newThisMonth: 8,
    revenue: 2.8
  },
  inspection: {
    specimens: 1256,
    outsourced: 89,
    accuracy: 98.7,
    pending: 23
  },
  finance: {
    monthlyRevenue: 485.6,
    costControl: 89.2,
    pendingApprovals: 15,
    savings: 12.3
  }
})

// 功能模块数据
const modules = ref([
  { name: '决策支持', desc: '运营数据可视化呈现，成本效益精细化分析', icon: 'solar:chart-outline', path: 'decision', color: 'indigo' },
  { name: '人员绩效', desc: '绩效数据统计，绩效规则设置和应用', icon: 'icon-park-outline:file-staff-one', path: 'staff', color: 'emerald' },
  { name: '项目套餐', desc: '检查项目管理，套餐配置和校验', icon: 'la:project-diagram', path: 'project', color: 'blue' },
  { name: '检验配置', desc: '标本管理配置，检验结果校验', icon: 'hugeicons:configuration-01', path: 'inspection', color: 'orange' },
  { name: '财物管控', desc: '耗材成本管控，营收确费管控', icon: 'icon-park-outline:finance', path: 'finance', color: 'purple' }
])

// 部门工作负荷数据
const deptWorkload = ref([
  { dept: '检验科', workload: 85, efficiency: 92, staff: 12, status: 'normal' },
  { dept: '放射科', workload: 78, efficiency: 88, staff: 8, status: 'normal' },
  { dept: '内科', workload: 92, efficiency: 85, staff: 15, status: 'high' },
  { dept: '外科', workload: 68, efficiency: 90, staff: 10, status: 'low' },
  { dept: '超声科', workload: 88, efficiency: 94, staff: 9, status: 'normal' }
])

// 财务概览数据
const financeOverview = ref([
  { category: '体检套餐', revenue: 285.6, percentage: 58.8, trend: 'up' },
  { category: '单项检查', revenue: 128.4, percentage: 26.4, trend: 'stable' },
  { category: '增值服务', revenue: 45.2, percentage: 9.3, trend: 'up' },
  { category: '其他收入', revenue: 26.4, percentage: 5.5, trend: 'down' }
])

// 待处理事项
const pendingTasks = ref([
  { id: 1, type: '绩效审核', content: '8月份绩效数据待审核', priority: 'high', dueDate: '2024-08-23' },
  { id: 2, type: '成本分析', content: '检验科耗材成本异常', priority: 'high', dueDate: '2024-08-23' },
  { id: 3, type: '套餐调整', content: '高血压专项套餐价格调整', priority: 'medium', dueDate: '2024-08-24' },
  { id: 4, type: '财务审批', content: '超限额耗材申请待审批', priority: 'medium', dueDate: '2024-08-24' },
  { id: 5, type: '配置更新', content: '检验结果校验规则更新', priority: 'low', dueDate: '2024-08-25' }
])
</script>

<template>
  <div class="p-4 bg-gradient-to-br from-slate-50 to-gray-50 dark:from-gray-900 dark:to-gray-800 min-h-screen transition-colors duration-300">
    <!-- 统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <!-- 决策报告数 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-indigo-600 dark:text-indigo-400">
              {{ deptStats.decision.reports }}
            </div>
            <div class="text-sm text-indigo-700 dark:text-indigo-300 mt-1 font-medium">
              决策报告数
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-indigo-400 to-indigo-600 dark:from-indigo-500 dark:to-indigo-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="solar:chart-outline" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 在职人员 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
              {{ deptStats.staff.totalStaff }}
            </div>
            <div class="text-sm text-emerald-700 dark:text-emerald-300 mt-1 font-medium">
              在职人员
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-600 dark:from-emerald-500 dark:to-emerald-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="icon-park-outline:file-staff-one" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 活跃套餐 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {{ deptStats.project.activePackages }}
            </div>
            <div class="text-sm text-blue-700 dark:text-blue-300 mt-1 font-medium">
              活跃套餐
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="la:project-diagram" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 检验准确率 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-orange-600 dark:text-orange-400">
              {{ deptStats.inspection.accuracy }}%
            </div>
            <div class="text-sm text-orange-700 dark:text-orange-300 mt-1 font-medium">
              检验准确率
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-orange-400 to-orange-600 dark:from-orange-500 dark:to-orange-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="hugeicons:configuration-01" class="text-white text-xl" />
          </div>
        </div>
      </el-card>

      <!-- 月度营收 -->
      <el-card class="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-gray-800 dark:to-gray-700 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {{ deptStats.finance.monthlyRevenue }}万
            </div>
            <div class="text-sm text-purple-700 dark:text-purple-300 mt-1 font-medium">
              月度营收
            </div>
          </div>
          <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-2xl flex items-center justify-center shadow-lg">
            <icon icon="icon-park-outline:finance" class="text-white text-xl" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：核心功能模块和部门工作负荷 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 核心功能模块 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <span class="font-medium text-gray-800 dark:text-gray-200">核心功能模块</span>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div v-for="module in modules" :key="module.name" class="cursor-pointer">
              <div
                class="flex items-center space-x-3 p-4 rounded-xl transition-all duration-300 hover:shadow-lg transform hover:-translate-y-1"
                :class="`bg-gradient-to-r from-${module.color}-50 to-${module.color}-100 dark:from-gray-700 dark:to-gray-600 hover:from-${module.color}-100 hover:to-${module.color}-200 dark:hover:from-gray-600 dark:hover:to-gray-500`"
              >
                <div
                  class="w-12 h-12 rounded-xl flex items-center justify-center shadow-md"
                  :class="`bg-gradient-to-br from-${module.color}-400 to-${module.color}-600`"
                >
                  <icon :icon="module.icon" class="text-white text-lg" />
                </div>
                <div>
                  <p class="font-semibold text-gray-800 dark:text-gray-200">{{ module.name }}</p>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{{ module.desc }}</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 部门工作负荷 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="font-medium text-gray-800 dark:text-gray-200">部门工作负荷</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">实时监控</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="dept in deptWorkload" :key="dept.dept" class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div class="flex items-center space-x-4">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-16">{{ dept.dept }}</span>
                <div class="flex items-center space-x-4">
                  <div class="text-center">
                    <div class="text-xs text-gray-500 dark:text-gray-400">工作负荷</div>
                    <div class="text-sm font-semibold text-gray-800 dark:text-gray-200">{{ dept.workload }}%</div>
                  </div>
                  <div class="text-center">
                    <div class="text-xs text-gray-500 dark:text-gray-400">效率</div>
                    <div class="text-sm font-semibold text-gray-800 dark:text-gray-200">{{ dept.efficiency }}%</div>
                  </div>
                  <div class="text-center">
                    <div class="text-xs text-gray-500 dark:text-gray-400">人员</div>
                    <div class="text-sm font-semibold text-gray-800 dark:text-gray-200">{{ dept.staff }}人</div>
                  </div>
                </div>
              </div>
              <el-tag
                :type="dept.status === 'high' ? 'danger' : dept.status === 'low' ? 'info' : 'success'"
                size="small"
              >
                {{ dept.status === 'high' ? '高负荷' : dept.status === 'low' ? '低负荷' : '正常' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧：财务概览和待处理事项 -->
      <div class="space-y-6">
        <!-- 财务概览 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:money" class="text-success" />
              <span class="font-medium text-gray-800 dark:text-gray-200">财务概览</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="item in financeOverview" :key="item.category" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ item.category }}</span>
                <div class="flex items-center space-x-1">
                  <icon
                    :icon="item.trend === 'up' ? 'ep:arrow-up' : item.trend === 'down' ? 'ep:arrow-down' : 'ep:minus'"
                    :class="[
                      item.trend === 'up' ? 'text-green-500' :
                      item.trend === 'down' ? 'text-red-500' :
                      'text-gray-400'
                    ]"
                    class="text-xs"
                  />
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ item.revenue }}万</div>
                <div class="text-xs text-gray-500 dark:text-gray-500">{{ item.percentage }}%</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 待处理事项 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:warning" class="text-warning" />
              <span class="font-medium text-gray-800 dark:text-gray-200">待处理事项</span>
            </div>
          </template>

          <div class="space-y-3">
            <div v-for="task in pendingTasks.slice(0, 5)" :key="task.id" class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ task.type }}</span>
                  <el-tag
                    :type="task.priority === 'high' ? 'danger' : task.priority === 'medium' ? 'warning' : 'info'"
                    size="small"
                  >
                    {{ task.priority === 'high' ? '紧急' : task.priority === 'medium' ? '普通' : '一般' }}
                  </el-tag>
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ task.content }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">{{ task.dueDate }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 业务管理概览 -->
        <el-card class="dark:bg-gray-800 border-0 shadow-lg">
          <template #header>
            <div class="flex items-center space-x-2">
              <icon icon="ep:data-analysis" class="text-primary" />
              <span class="font-medium text-gray-800 dark:text-gray-200">业务管理概览</span>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">运营效率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="deptStats.decision.efficiency"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ deptStats.decision.efficiency }}%</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">成本控制</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="deptStats.finance.costControl"
                  :show-text="false"
                  class="w-20"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">{{ deptStats.finance.costControl }}%</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <span class="text-gray-700 dark:text-gray-300">绩效达标率</span>
              <div class="flex items-center space-x-2">
                <el-progress
                  :percentage="Math.round((deptStats.staff.highPerformers / deptStats.staff.totalStaff) * 100)"
                  :show-text="false"
                  class="w-20"
                  color="#10b981"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ Math.round((deptStats.staff.highPerformers / deptStats.staff.totalStaff) * 100) }}%
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>
