package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * 检验条码设置Dto
 */
public class BarCodeConfigDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EditDto {
        // 条形码前缀
        @NotBlank(message = "条形码前缀不能为空")
        private String prefix;
        
        // 条形码名称
        @NotBlank(message = "条形码名称不能为空")
        private String name;
        
        // 条形码长度(不含前缀)
        @NotNull(message = "条形码长度不能为空")
        private Integer len;
        
        // 起始编号
        private Integer startNo;
        
        // 备注说明
        private String remark;
        
        // 样本体积
        private String sampleVolume;
        
        // 样本颜色
        private String sampleColor;
        
        // 样本说明
        private String sampleRemark;
        
        // 禁用
        private Boolean disabled;
    }
}