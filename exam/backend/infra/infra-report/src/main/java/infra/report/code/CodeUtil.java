package infra.report.code;

import jakarta.servlet.http.HttpServletResponse;

import java.awt.image.BufferedImage;
import java.io.IOException;

/**
 * 二维码/条形码生成工具类
 */
public class CodeUtil {
    private static final QRCodeGenerator QR_GENERATOR = new QRCodeGenerator();
    private static final BarcodeGenerator CODE128_GENERATOR = BarcodeGenerator.code128();
    private static final BarcodeGenerator CODE39_GENERATOR = BarcodeGenerator.code39();
    private static final BarcodeGenerator EAN13_GENERATOR = BarcodeGenerator.ean13();

    // ==================== 二维码生成方法 ====================

    /**
     * 生成二维码
     */
    public static BufferedImage generateQRCode(String data) {
        return QR_GENERATOR.generateImage(data, CodeRenderOptions.defaultQRCode());
    }

    /**
     * 生成二维码（自定义配置）
     */
    public static BufferedImage generateQRCode(String data, CodeRenderOptions config) {
        return QR_GENERATOR.generateImage(data, config);
    }

    /**
     * 输出二维码到HTTP响应
     */
    public static void generateQRCodeToResponse(String data, HttpServletResponse response) throws IOException {
        generateQRCodeToResponse(data, CodeRenderOptions.defaultQRCode(), response);
    }

    /**
     * 输出二维码到HTTP响应（自定义配置）
     */
    public static void generateQRCodeToResponse(String data, CodeRenderOptions config, HttpServletResponse response) throws IOException {
        response.setContentType("image/" + config.getFormat().toLowerCase());
        response.setHeader("Content-Disposition", "inline; filename=qrcode." + config.getFormat().toLowerCase());
        QR_GENERATOR.generateToStream(data, config, response.getOutputStream());
    }

    // ==================== 条码生成方法 ====================

    /**
     * 生成Code128条码
     */
    public static BufferedImage generateCode128(String data) {
        return CODE128_GENERATOR.generateImage(data, CodeRenderOptions.defaultBarcode());
    }

    /**
     * 生成Code128条码（自定义配置）
     */
    public static BufferedImage generateCode128(String data, CodeRenderOptions config) {
        return CODE128_GENERATOR.generateImage(data, config);
    }

    /**
     * 生成Code39条码
     */
    public static BufferedImage generateCode39(String data) {
        return CODE39_GENERATOR.generateImage(data, CodeRenderOptions.defaultBarcode());
    }

    /**
     * 生成Code39条码（自定义配置）
     */
    public static BufferedImage generateCode39(String data, CodeRenderOptions config) {
        return CODE39_GENERATOR.generateImage(data, config);
    }

    /**
     * 生成EAN13条码
     */
    public static BufferedImage generateEAN13(String data) {
        return EAN13_GENERATOR.generateImage(data, CodeRenderOptions.defaultBarcode());
    }

    /**
     * 生成EAN13条码（自定义配置）
     */
    public static BufferedImage generateEAN13(String data, CodeRenderOptions config) {
        return EAN13_GENERATOR.generateImage(data, config);
    }

    /**
     * 输出Code128条码到HTTP响应
     */
    public static void generateCode128ToResponse(String data, HttpServletResponse response) throws IOException {
        generateBarcodeToResponse(data, CodeRenderOptions.defaultBarcode(), CODE128_GENERATOR, response);
    }

    /**
     * 输出Code39条码到HTTP响应
     */
    public static void generateCode39ToResponse(String data, HttpServletResponse response) throws IOException {
        generateBarcodeToResponse(data, CodeRenderOptions.defaultBarcode(), CODE39_GENERATOR, response);
    }

    /**
     * 输出EAN13条码到HTTP响应
     */
    public static void generateEAN13ToResponse(String data, HttpServletResponse response) throws IOException {
        generateBarcodeToResponse(data, CodeRenderOptions.defaultBarcode(), EAN13_GENERATOR, response);
    }

    /**
     * 输出条码到HTTP响应（通用方法）
     */
    public static void generateBarcodeToResponse(String data, CodeRenderOptions options,
                                                 BarcodeGenerator generator, HttpServletResponse response) throws IOException {
        response.setContentType("image/" + options.getFormat().toLowerCase());
        response.setHeader("Content-Disposition", "inline; filename=barcode." + options.getFormat().toLowerCase());
        generator.generateToStream(data, options, response.getOutputStream());
    }
}
