export function useLoading(initialValue = false) {
  const loading = ref(initialValue)

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    // 注意：避免加loading状态判断，应用中可能存在一个loading状态多个请求的情况
    try {
      loading.value = true
      return await fn()
    }
    finally {
      loading.value = false
    }
  }

  return { loading, setLoading, withLoading }
}
