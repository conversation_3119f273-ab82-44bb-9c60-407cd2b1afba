package module.sys.admin.controller;

import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.SampleTypeDto;
import module.sys.entity.SampleType;
import module.sys.service.SampleTypeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "样本类型")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/sampleType")
public class SampleTypeController {
    private final SampleTypeService sampleTypeService;

    @Operation(summary = "样本类型列表")
    @GetMapping("/list")
    @Perm("health:sampleType")
    public Result<PageResult<SampleType>> list(
            String type,
            String key,
            Long deptId,
            PageParam pageParam) {

        var result = sampleTypeService.getPage(w -> {
            if (!Str.isEmpty(key)) {
                String keyTrim = key.trim();
                if ("name".equals(type)) {
                    w.and(w1 -> w1.like("name", keyTrim)
                            .or().like("short_name", keyTrim)
                            .or().like("py", keyTrim));
                }
                w.like("code".equals(type), "code", keyTrim);
            }
            w.eq(deptId != null, "dept_id", deptId);
        }, pageParam, FilterOptions.DISABLE_ALL_FILTER);

        return Result.okData(result);
    }

    @Operation(summary = "新增样本类型")
    @PostMapping("/add")
    @AuditLog(code = "health:sampleType:add", value = "样本类型-新增")
    @Perm("health:sampleType:add")
    public Result<Long> add(@RequestBody @Valid SampleTypeDto.EditDto dto) {
        SampleType entity = SampleType.builder()
                .name(dto.getName())
                .shortName(dto.getShortName())
                .code(dto.getCode())
                .deptId(dto.getDeptId())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return sampleTypeService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑样本类型")
    @GetMapping("/edit")
    @Perm("health:sampleType:edit")
    public Result<SampleTypeDto.EditDto> getEdit(Long id) {
        var entityOpt = sampleTypeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new SampleTypeDto.EditDto())
        );
    }

    @Operation(summary = "编辑样本类型")
    @PostMapping("/edit")
    @AuditLog(code = "health:sampleType:edit", value = "样本类型-编辑")
    @Perm("health:sampleType:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid SampleTypeDto.EditDto dto) {
        var entityOpt = sampleTypeService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("样本类型不存在");
        }

        SampleType entity = entityOpt.get();
        entity.setName(dto.getName());
        entity.setShortName(dto.getShortName());
        entity.setCode(dto.getCode());
        entity.setDeptId(dto.getDeptId());
        entity.setSort(dto.getSort());
        entity.setDisabled(dto.getDisabled());

        return sampleTypeService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除样本类型")
    @PostMapping("/delete")
    @AuditLog(code = "health:sampleType:delete", value = "样本类型-删除")
    @Perm("health:sampleType:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            sampleTypeService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:sampleType:enable", value = "样本类型-启用")
    @Perm("health:sampleType:enable")
    public Result<Void> enableSampleType(@RequestBody List<Long> ids) {
        sampleTypeService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:sampleType:disable", value = "样本类型-禁用")
    @Perm("health:sampleType:disable")
    public Result<Void> disableSampleType(@RequestBody List<Long> ids) {
        sampleTypeService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    @Operation(summary = "获取样本类型字典")
    @GetMapping("/dict")
    @Perm
    public Result<List<IDictEnum.EnumDictItem<Long>>> getDict() {
        return Result.okData(sampleTypeService.getList().stream()
                .map(w -> new IDictEnum.EnumDictItem<>(w.getId(), w.getName()))
                .toList());
    }
}
