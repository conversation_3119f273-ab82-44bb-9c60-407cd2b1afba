package module.sys.service;

import infra.core.exception.BizException;
import infra.core.text.Str;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.IdBuilder;
import module.sys.mapper.IdBuilderMapper;
import module.sys.model.IdRecord;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Id Builder 服务
 */
@Slf4j
@Service
public class IdBuilderService extends ServiceBase<IdBuilderMapper, IdBuilder> {
    @Override
    protected void beforeSave(IdBuilder entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        // 为提高取号性能，更新时不做检查，在数据库层面做唯一约束，前端也在编辑时不允许修改
        if (!isUpdate && existsByCode(entity.getCode(), null)) {
            throw new BizException("指定关键字已存在");
        }

        if (entity.getCurrentNo() == null || entity.getCurrentNo() + 1 < entity.getStartNo()) {
            entity.setCurrentNo(Long.valueOf(entity.getStartNo()) - 1);
        }
    }

    /**
     * 根据code查询
     */
    public Optional<IdBuilder> getByCode(String code) {
        if (Str.isEmpty(code))
            return Optional.empty();

        return getFirst(w -> w.eq("code", code));
    }

    /**
     * 检查code是否已存在
     */
    public boolean existsByCode(String code, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(w -> {
            w.eq("code", code);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

//    /**
//     * 获取下一个可用id(不实际使用)
//     */
//    public IdRecord next(String code) {
//        var opt = getByCode(code);
//        if (opt.isEmpty()) {
//            throw new BizException("未配置指定关键字的ID生成器");
//        }
//
//        var builder = opt.get();
//        var nextNo = builder.getCurrentNo() + 1;
//        return new IdRecord(nextNo, String.format("%0" + Math.max(builder.getLen(), 0) + "d", nextNo));
//    }

    /**
     * 使用申请(直接使用)
     */
    public IdRecord useApply(String code) {
        var opt = getByCode(code);
        if (opt.isEmpty()) {
            throw new BizException("未配置指定关键字的ID生成器");
        }
        var builder = opt.get();

        int maxRetries = 5;
        int retryCount = 0;
        while (true) {
            if (retryCount > 0) {
                log.info("并发冲突，第{}次申ID, KEY: {}", retryCount, builder.getCode());
                builder = getById(builder.getId()).orElseThrow();
            }

            Long nextNo = builder.getCurrentNo() + 1;
            builder.setCurrentNo(nextNo);
            boolean updated = update(builder);

            if (!updated) {
                retryCount++;
                if (retryCount >= maxRetries) {
                    log.warn("申请ID失败，ID KEY:{}", builder.getCode());
                    throw new BizException("申请ID失败，系统繁忙请稍后再试");
                }
            } else {
                return new IdRecord(nextNo, String.format("%0" + Math.max(builder.getLen(), 0) + "d", nextNo));
            }
        }
    }
}