import { downloadFile, get, post, upload as uploadRequest } from '~/utils/request'

const baseUrl = '/sys/upfile'

/**
 * 文件列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

// 文件上传地址
export const uploadUrl = `${baseUrl}/upload`

/**
 * 文件上传
 */
export function upload(file: File | File[], data?: Record<string, any>): Promise<any> {
  return uploadRequest(uploadUrl, file, data)
}

/**
 * 更新文件描述
 */
export function updateInfo(data: any): Promise<any> {
  return post<any>(`${baseUrl}/updateInfo`, data)
}

/**
 * 删除文件
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 物理删除文件
 */
export function removePhysical(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/deletePhysical`, ids)
}

/**
 * 文件下载
 */
export function download(id: number): Promise<any> {
  return downloadFile(`${baseUrl}/download`, {
    data: [id],
  })
}
