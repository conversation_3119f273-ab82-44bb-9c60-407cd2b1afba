package module.sys.entity;

import infra.domain.entity.IdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 岗位权限
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_post_perm")
public class PostPerm extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 岗位Id
    private Long postId;
    // 权限
    private String perm;
}
