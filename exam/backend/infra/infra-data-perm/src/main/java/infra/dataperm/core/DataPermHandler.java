package infra.dataperm.core;

import infra.auth.annotation.DataPerm;
import infra.core.exception.BizException;
import infra.dataperm.annotation.DeptData;
import infra.dataperm.annotation.UserData;
import infra.auth.web.UserContext;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.handler.MultiDataPermissionHandler;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.relational.ParenthesedExpressionList;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.schema.Column;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据权限处理器
 * 参考： <a href="https://baomidou.com/plugins/data-permission/">...</a>
 */
@Slf4j
@RequiredArgsConstructor
public class DataPermHandler implements MultiDataPermissionHandler {
    // 实体注解缓存
    private static final Map<String, EntityAnnotation> ENTITY_ANNOTATION_CACHE = new ConcurrentHashMap<>();
    // 是否禁用数据权限
    private static final ScopedValue<Boolean> DISABLE_SCOPED = ScopedValue.newInstance();

    /**
     * 禁用数据权限
     */
    public static <R, X extends Throwable> R disableRun(ScopedValue.CallableOp<? extends R, X> op) throws X {
        return ScopedValue.where(DISABLE_SCOPED, true).call(op);
    }

    /**
     * 禁用数据权限，无值返回
     */
    public static void disableRun(Runnable runnable) {
        ScopedValue.where(DISABLE_SCOPED, true).run(runnable);
    }

    /**
     * 获取数据权限条件表达式
     */
    @Override
    public Expression getSqlSegment(Table table, Expression where, String mappedStatementId) {
        // 禁用数据权限
        if (Boolean.TRUE.equals(DISABLE_SCOPED.orElse(false))) {
            log.debug("数据权限已被禁用，跳过处理: {}", mappedStatementId);
            return null;
        }

        // 获取实体数据权限注解
        var permAnnotation = getEntityAnnotation(mappedStatementId);
        if (permAnnotation.dept == null && permAnnotation.user == null) {
            return null;
        }

        var user = UserContext.getCurrentUser();
        // 有数据权限标识，但用户未登录
        if (user == null) {
            return falseCondition();
        }

        // 有所有权限
        if (user.isAdmin() || user.getDataPerm() == DataPerm.ALL) {
            return null;
        }

        // 构建条件表达式
        return switch (user.getDataPerm()) {
            case SELF ->
                    permAnnotation.user == null ? null : buildSelfCondition(permAnnotation.user.value(), user.getId());
            case DEPT ->
                    permAnnotation.dept == null ? null : buildDeptCondition(permAnnotation.dept.value(), user.getDeptId());
            case DEPT_CHILD ->
                    permAnnotation.dept == null ? null : buildDeptAndChildCondition(permAnnotation.dept.value(), user.getDeptAndChildren());
            default -> throw new BizException("未知的数据权限策略: " + user.getDataPerm());
        };
    }

    /**
     * 获取实体的注解
     */
    private EntityAnnotation getEntityAnnotation(String mappedStatementId) {
        return ENTITY_ANNOTATION_CACHE.computeIfAbsent(mappedStatementId, key -> {
            try {
                log.debug("获取实体数据权限注解: {}", key);

                String mapperClassName = key.substring(0, key.lastIndexOf("."));
                Class<?> mapperClass = Class.forName(mapperClassName);

                // 获取 Mapper 接口的泛型参数类型（实体类）
                Type[] genericInterfaces = mapperClass.getGenericInterfaces();

                for (Type genericInterface : genericInterfaces) {
                    if (genericInterface instanceof ParameterizedType parameterizedType) {
                        // 定位 BaseMapper<T> 的泛型参数
                        if (parameterizedType.getRawType().equals(BaseMapper.class)) {

                            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                            if (actualTypeArguments.length > 0) {
                                var clazz = Class.forName(actualTypeArguments[0].getTypeName());
                                log.debug("获取数据权限实体类型: {}", clazz.getName());
                                return new EntityAnnotation(clazz.getAnnotation(DeptData.class), clazz.getAnnotation(UserData.class));
                            }
                        }
                    }
                }

                throw new BizException("未找到实体类: " + mapperClassName);
            } catch (ClassNotFoundException e) {
                throw new BizException("解析实体类失败: " + mappedStatementId, e);
            }
        });
    }

    /**
     * 获取自己数据条件
     */
    private Expression buildSelfCondition(String fieldName, Long userId) {
        if (fieldName == null || userId == null) {
            return null;
        }
        return appendCondition(fieldName, userId);
    }

    /**
     * 获取部门数据条件
     */
    private Expression buildDeptCondition(String fieldName, Long deptId) {
        if (fieldName == null || deptId == null) {
            return null;
        }
        return appendCondition(fieldName, deptId);
    }

    /**
     * 获取部门及子部门数据条件
     */
    private Expression buildDeptAndChildCondition(String fieldName, List<Long> deptAndChildIds) {
        if (fieldName == null || deptAndChildIds == null || deptAndChildIds.isEmpty()) {
            return null;
        }
        return appendInCondition(fieldName, deptAndChildIds);
    }

    /**
     * 构建假条件
     */
    private Expression falseCondition() {
        return new EqualsTo(new LongValue(1), new LongValue(0));
    }

    /**
     * 添加条件
     */
    private Expression appendCondition(String column, Long value) {
        return new EqualsTo(new Column(column), new LongValue(value));
    }

    /**
     * 添加条件(In条件)
     */
    private Expression appendInCondition(String column, List<Long> values) {
        var itemsList = new ParenthesedExpressionList<>(values.stream().map(LongValue::new).toList());

        return new InExpression(new Column(column), itemsList);
    }

    /**
     * 实体注解
     */
    public record EntityAnnotation(DeptData dept, UserData user) {
    }
}
