package module.sys.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

public class UpfileDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UploadResult {
        private Long id;
        private String fileName;
        private String objectKey;
        private String fullPath;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UpdateInfoDto {
        private Long id;
        private String info;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UpFileQuery {
        private String type;
        private String key;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }
} 