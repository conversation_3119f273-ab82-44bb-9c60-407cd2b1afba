<script setup lang="ts">
definePage({
  meta: {
    title: '人员绩效',
    menu: true,
    order: 2,
    icon: 'icon-park-outline:file-staff-one',
  },
})
</script>

<template>
<p>1. 绩效数据统计：统计医师 / 技师工作量（日均接诊量、检查项目数）、工作质量（报告审核准确率、异常结果处理及时率）、工作效率（单项检查时长），生成个人 / 科室绩效对比表；</p>
<p>2. 绩效规则设置：设置绩效核算规则（如报告准确率权重≥40%、超时长操作扣分项），关联决策支持模块数据自动计算绩效得分；</p>
<p>3. 绩效应用：基于绩效数据提供人员培训、岗位调整建议，导出绩效报告用于考核</p>

检查科室 “操作流程标准化约束（记录检查时长，标记超时长操作）”🔶21-80；前台导检 “预约资源动态调配（关联人员工作量）”🔶21-71；决策支持 “运营数据可视化（辅助人力调配）”🔶21-92
</template>
