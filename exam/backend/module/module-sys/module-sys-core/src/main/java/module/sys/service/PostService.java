package module.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.auth.annotation.DataPerm;
import infra.core.exception.BizException;
import infra.core.text.Chinese;
import infra.core.text.Str;
import infra.domain.service.FilterOptions;
import infra.domain.service.ServiceBase;
import lombok.extern.slf4j.Slf4j;
import module.sys.entity.Post;
import module.sys.entity.PostPerm;
import module.sys.mapper.PostMapper;
import module.sys.mapper.PostPermMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PostService extends ServiceBase<PostMapper, Post> {
    @Autowired
    private PostPermMapper postPermMapper;

    @Override
    protected Function<QueryWrapper<Post>, QueryWrapper<Post>> bizFilter() {
        return w -> w.eq("disabled", false);
    }

    @Override
    protected Function<QueryWrapper<Post>, QueryWrapper<Post>> defaultOrderBy() {
        return w -> w.orderByAsc("sort")
                .orderByAsc("id");
    }

    @Override
    protected void beforeSave(Post entity, boolean isUpdate) {
        super.beforeSave(entity, isUpdate);

        if (existsByCode(entity.getCode(), entity.getDeptId(),
                isUpdate ? entity.getId() : null)) {
            throw new BizException("岗位编码已存在");
        }

        setPinyin(entity);
    }

    /**
     * 检查部门下的岗位编码是否已存在
     */
    public boolean existsByCode(String code, Long deptId, Long excludeId) {
        if (Str.isEmpty(code)) {
            return false;
        }

        return exists(w -> {
            w.eq("code", code)
                    .eq("dept_id", deptId);
            if (excludeId != null) {
                w.ne("id", excludeId);
            }
        });
    }

    /**
     * 获取指定部门下的岗位
     */
    public List<Post> getListByDept(Long deptId, FilterOptions filterOptions) {
        return getList(w -> w.eq("dept_id", deptId), filterOptions);
    }

    /**
     * 获取岗位的权限集合
     */
    public Set<String> getPostPerms(Long postId) {
        Optional<Post> postOpt = getById(postId, FilterOptions.DISABLE_ALL_FILTER);
        if (postOpt.isEmpty()) {
            return Collections.emptySet();
        }

        QueryWrapper<PostPerm> wrapper = new QueryWrapper<>();
        wrapper.eq("post_id", postId);
        List<PostPerm> perms = postPermMapper.selectList(wrapper);

        return perms.stream()
                .map(PostPerm::getPerm)
                .collect(Collectors.toSet());
    }

    /**
     * 获取多个岗位的权限集合
     */
    public Set<String> getPostsPerms(Collection<Long> postIds) {
        if (postIds == null || postIds.isEmpty()) {
            return Collections.emptySet();
        }

        List<Long> enabledPostIds = getList(wrapper ->
                wrapper.eq("disabled", false).in("id", postIds))
                .stream().map(Post::getId)
                .toList();

        if (enabledPostIds.isEmpty()) {
            return Collections.emptySet();
        }

        QueryWrapper<PostPerm> wrapper = new QueryWrapper<>();
        wrapper.in("post_id", enabledPostIds);
        List<PostPerm> perms = postPermMapper.selectList(wrapper);

        return perms.stream()
                .map(PostPerm::getPerm)
                .collect(Collectors.toSet());
    }

    /**
     * 更新岗位权限
     */
    @Transactional
    public void updatePostPerms(Long postId, Set<String> perms) {
        // 验证岗位是否存在
        Optional<Post> postOpt = getById(postId, FilterOptions.DISABLE_ALL_FILTER);
        if (postOpt.isEmpty()) {
            throw new BizException("岗位不存在");
        }

        // 删除原有权限
        QueryWrapper<PostPerm> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("post_id", postId);
        postPermMapper.delete(deleteWrapper);

        // 批量创建新权限
        if (perms != null && !perms.isEmpty()) {
            List<PostPerm> postPerms = perms.stream()
                    .map(perm -> {
                        PostPerm postPerm = new PostPerm();
                        postPerm.setPostId(postId);
                        postPerm.setPerm(perm);
                        return postPerm;
                    })
                    .toList();

            postPermMapper.insert(postPerms);
        }
    }

    /**
     * 获取部门下的所有岗位
     */
    public List<Post> getDeptPosts(Long deptId) {
        return getList(w -> w.eq("dept_id", deptId));
    }

    /**
     * 获取岗位的数据权限级别
     */
    public DataPerm getDataPerm(Long postId) {
        Optional<Post> postOpt = getById(postId);
        if (postOpt.isEmpty()) {
            throw new BizException("岗位不存在或已停用");
        }

        return postOpt.get().getPerm();
    }

    /**
     * 设置拼音
     */
    private void setPinyin(Post post) {
        Chinese.Pinyin pinyin = Chinese.getPinyin(post.getName());
        post.setPy(pinyin.py());
    }
}
