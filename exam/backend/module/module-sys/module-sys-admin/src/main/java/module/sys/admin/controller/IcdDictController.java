package module.sys.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import infra.audit.core.AuditLog;
import infra.auth.annotation.Perm;
import infra.core.common.IDictEnum;
import infra.core.common.ObjectUtil;
import infra.core.common.Result;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.domain.service.FilterOptions;
import infra.report.excel.exporter.ExcelExporter;
import infra.report.excel.importer.ExcelImporter;
import infra.report.excel.importer.ExcelParser;
import infra.report.excel.importer.ImportResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import module.sys.admin.dto.IcdDictDto;
import module.sys.entity.IcdDict;
import module.sys.model.SexLimit;
import module.sys.service.IcdDictService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Tag(name = "ICD编码")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys/icdDict")
public class IcdDictController {
    private final IcdDictService icdDictService;

    @Operation(summary = "ICD编码列表")
    @GetMapping("/list")
    @Perm("health:icdDict")
    public Result<PageResult<IcdDict>> list(
            String type,
            String key,
            PageParam pageParam) {

        var result = icdDictService.getPage(w -> applyQueryCondition(w, type, key), pageParam, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(result);
    }

    @Operation(summary = "新增ICD编码")
    @PostMapping("/add")
    @AuditLog(code = "health:icdDict:add", value = "ICD编码-新增")
    @Perm("health:icdDict:add")
    public Result<Long> add(@RequestBody @Valid IcdDictDto.EditDto dto) {
        IcdDict entity = IcdDict.builder()
                .icdNo(dto.getIcdNo())
                .name(dto.getName())
                .isContagion(dto.getIsContagion())
                .isChronic(dto.getIsChronic())
                .sex(dto.getSex())
                .sort(dto.getSort())
                .disabled(dto.getDisabled())
                .build();

        return icdDictService.add(entity) ?
                Result.okData(entity.getId()) :
                Result.FAIL_LONG;
    }

    @Operation(summary = "编辑ICD编码")
    @GetMapping("/edit")
    @Perm("health:icdDict:edit")
    public Result<IcdDictDto.EditDto> getEdit(Long id) {
        var entityOpt = icdDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        return Result.okData(
                ObjectUtil.copyTo(entityOpt.orElse(null), new IcdDictDto.EditDto())
        );
    }

    @Operation(summary = "编辑ICD编码")
    @PostMapping("/edit")
    @AuditLog(code = "health:icdDict:edit", value = "ICD编码-编辑")
    @Perm("health:icdDict:edit")
    public Result<Void> edit(@RequestParam Long id, @RequestBody @Valid IcdDictDto.EditDto dto) {
        var entityOpt = icdDictService.getById(id, FilterOptions.DISABLE_ALL_FILTER);
        if (entityOpt.isEmpty()) {
            return Result.fail("ICD编码不存在");
        }

        IcdDict entity = entityOpt.get();
        entity.setIcdNo(dto.getIcdNo());
        entity.setName(dto.getName());
        entity.setIsContagion(dto.getIsContagion());
        entity.setIsChronic(dto.getIsChronic());
        entity.setSex(dto.getSex());
        entity.setSort(dto.getSort());
        entity.setDisabled(dto.getDisabled());

        return icdDictService.update(entity) ? Result.OK : Result.FAIL;
    }

    @Operation(summary = "删除ICD编码")
    @PostMapping("/delete")
    @AuditLog(code = "health:icdDict:delete", value = "ICD编码-删除")
    @Perm("health:icdDict:delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            icdDictService.deleteByIds(ids);
        }
        return Result.OK;
    }

    @Operation(summary = "导出ICD编码")
    @PostMapping("/export")
    @AuditLog(code = "health:icdDict:export", value = "ICD编码-导出")
    @Perm("health:icdDict:export")
    public void export(String type,
                       String key,
                       HttpServletResponse response) throws IOException {

        ExcelExporter.<IcdDict>create()
                .sheet("ICD编码")
                .columns(cols ->
                        cols.add("ICD编码", IcdDict::getIcdNo)
                                .add("疾病名称", IcdDict::getName)
                                .add("是否传染病", entity -> entity.getIsContagion() ? "是" : "否")
                                .add("是否慢性病", entity -> entity.getIsChronic() ? "是" : "否")
                                .add("性别", entity -> entity.getSex().getName())
                                .add("排序", IcdDict::getSort)
                                .add("状态", entity -> entity.getDisabled() ? "禁用" : "启用")
                )
                .fromService(icdDictService, w -> applyQueryCondition(w, type, key), FilterOptions.DISABLE_ALL_FILTER)
                .export(response, "ICD编码");
    }

    @Operation(summary = "导入ICD编码")
    @PostMapping("/import")
    @AuditLog(code = "health:icdDict:import", value = "ICD编码-导入")
    @Perm("health:icdDict:import")
    @Transactional
    public Result<Void> importData(@RequestParam("files") MultipartFile file) {
        try {
            ImportResult<IcdDict> parseResult = ExcelImporter.create(IcdDict::new)
                    .columns(cols -> {
                        cols.required("ICD编码", ExcelParser.string(), IcdDict::setIcdNo);
                        cols.required("疾病名称", ExcelParser.string(), IcdDict::setName);
                        cols.withDefault("是否传染病", "是"::equals, IcdDict::setIsContagion, false);
                        cols.withDefault("是否慢性病", "是"::equals, IcdDict::setIsChronic, false);
                        cols.withDefault("性别", value -> IDictEnum.fromName(SexLimit.class, value.toString()), IcdDict::setSex, SexLimit.NORMAL);
                        cols.withDefault("排序", ExcelParser.integer(), IcdDict::setSort, 0);
                        cols.withDefault("状态", "禁用"::equals, IcdDict::setDisabled, false);
                    })
                    .importFrom(file);

            // 解析有错误
            if (parseResult.hasErrors()) {
                return Result.fail(parseResult.errors()
                        .getFirst().message());
            }
            if (parseResult.successData().isEmpty())
                return Result.OK;

            icdDictService.addBatch(parseResult.successData());
            return Result.OK;
        } catch (Exception e) {
            throw new RuntimeException("导入失败: " + e.getMessage(), e);
        }
    }

    @Operation(summary = "启用")
    @PostMapping("/enable")
    @AuditLog(code = "health:icdDict:enable", value = "ICD编码-启用")
    @Perm("health:icdDict:enable")
    public Result<Void> enableIcd(@RequestBody List<Long> ids) {
        icdDictService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", true)
                        .set("disabled", false));
        return Result.OK;
    }

    @Operation(summary = "禁用")
    @PostMapping("/disable")
    @AuditLog(code = "health:icdDict:disable", value = "ICD编码-禁用")
    @Perm("health:icdDict:disable")
    public Result<Void> disableIcd(@RequestBody List<Long> ids) {
        icdDictService.updateBatch(wrapper ->
                wrapper.in("id", ids)
                        .eq("disabled", false)
                        .set("disabled", true));
        return Result.OK;
    }

    /**
     * 条件处理
     */
    private QueryWrapper<IcdDict> applyQueryCondition(QueryWrapper<IcdDict> wrapper, String type, String key) {
        if (!Str.isEmpty(key)) {
            String keyTrim = key.trim();
            if ("name".equals(type)) {
                wrapper.and(w -> w.like("name", keyTrim).or().like("py", keyTrim));
            }
            wrapper.like("icdNo".equals(type), "icd_no", keyTrim);
        }
        return wrapper;
    }
}
