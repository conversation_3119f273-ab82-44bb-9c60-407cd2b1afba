package infra.sse.notice.web;

import infra.auth.annotation.Perm;
import infra.auth.core.AuthManager;
import infra.auth.core.ITokenGetter;
import infra.auth.web.UserContext;
import infra.core.common.Result;
import infra.core.security.Aes;
import infra.core.text.Str;
import infra.domain.query.PageParam;
import infra.domain.query.PageResult;
import infra.sse.core.SseConnectionManager;
import infra.sse.notice.config.NoticeConfigProperties;
import infra.sse.notice.core.INoticeService;
import infra.sse.notice.model.NoticeMessage;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * SSE通知控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@ConditionalOnBean({SseConnectionManager.class, INoticeService.class})
public class NoticeController {

    private final SseConnectionManager connectionManager;
    private final INoticeService noticeService;
    private final AuthManager authManager;
    private final ITokenGetter tokenGetter;
    private final NoticeConfigProperties config;

    /**
     * 建立SSE连接
     */
    @GetMapping("${app.sse.notice.api-path:/sse/notice}/connect")
    public SseEmitter connect(String t) {
        if (Str.isEmpty(t)) {
            return null;
        }

        Long userId = validateToken(t);
        if (userId == null) {
            return null;
        }

        return connectionManager.createConnection(userId);
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("${app.sse.notice.api-path:/sse/notice}/unreadCount")
    @Perm
    public Result<Long> getUnreadCount() {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.FAIL_LONG;

        int count = noticeService.getUnreadCount(user.getId());
        return Result.okData((long) count);
    }

    /**
     * 标记消息为已读
     */
    @Perm
    @PostMapping("${app.sse.notice.api-path:/sse/notice}/markRead")
    public Result<Void> markAsRead(@RequestBody List<Long> ids) {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.FAIL;

        noticeService.markAsRead(user.getId(), ids);
        return Result.OK;
    }

    /**
     * 标记所有消息为已读
     */
    @PostMapping("${app.sse.notice.api-path:/sse/notice}/markAllRead")
    public Result<Void> markAllAsRead() {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.FAIL;

        noticeService.markAllAsRead(user.getId());
        return Result.OK;
    }

    /**
     * 获取最新n条消息
     */
    @Perm
    @GetMapping("${app.sse.notice.api-path:/sse/notice}/top")
    public Result<List<NoticeMessage>> getTop(int top) {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.fail("未获取到用户信息");

        return Result.okData(noticeService.getList(user.getId(), Math.min(Math.max(top, 1), 50)));
    }

    /**
     * 分页查询通知消息
     */
    @Perm
    @GetMapping("${app.sse.notice.api-path:/sse/notice}/page")
    public Result<PageResult<NoticeMessage>> getPage(NoticeMessage query, PageParam pageParam) {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.fail("未获取到用户信息");

        PageResult<NoticeMessage> result = noticeService.getPage(user.getId(), query, pageParam);
        return Result.okData(result);
    }

    /**
     * 删除消息
     */
    @Perm
    @PostMapping("${app.sse.notice.api-path:/sse/notice}/delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.FAIL;

        noticeService.delete(user.getId(), ids);
        return Result.OK;
    }

    /**
     * 删除所有消息
     */
    @Perm
    @PostMapping("${app.sse.notice.api-path:/sse/notice}/deleteAll")
    public Result<Void> deleteAll() {
        var user = UserContext.getCurrentUser();
        if (user == null)
            return Result.FAIL;

        noticeService.deleteAll(user.getId());
        return Result.OK;
    }

    /**
     * 为已登录用户颁发一个临时token，用于建立sse连接
     */
    @Perm
    @GetMapping("${app.sse.notice.api-path:/sse/notice}/getToken")
    public Result<String> getToken(HttpServletRequest request) {
        var user = UserContext.getCurrentUser();
        if (user == null) {
            return Result.fail("未获取到用户信息");
        }

        try {
            String token = tokenGetter.getToken(request);
            token = Str.urlEncode(Aes.encrypt(token, config.getSecretKey()));

            return Result.okData(token);
        } catch (Exception e) {
            log.error("生成SSE临时token失败", e);
            return Result.fail("获取SSE连接Token失败");
        }
    }

    /**
     * 验证临时token
     * 如果token过期
     */
    private Long validateToken(String token) {
        try {
            String decryptToken = Aes.decrypt(token, config.getSecretKey());
            // 验证用户身份
            var user = authManager.getCurrentUser(decryptToken);
            if (user == null) {
                log.debug("SSE Token验证失败：无效的用户token");
                return null;
            }

            return user.getId();
        } catch (Exception e) {
            log.warn("SSE Token验证失败", e);
            return null;
        }
    }
}
