<script setup lang="ts">
definePage({
  meta: {
    title: '接口管理',
    menu: true,
    order: 7,
    icon: 'nonicons:interface-16',
  },
})
</script>

<template>
<p>1. 院内接口配置：配置医院 HIS、LIS、PACS 系统接口参数（地址、密钥、传输格式），实时检测接口连接状态，异常时触发弹窗 + 短信提醒；</p>
<p>2. 外送接口配置：配置外送检验机构接口，设置外送结果回填规则（如外送机构上传后自动同步至院内系统）；</p>
<p>3. 接口权限控制：按角色设置接口操作权限（运维人员可修改参数，信息管理人员仅查看日志）；</p>
<p>4. 接口日志：记录数据同步日志（时间、数据量、成功 / 失败状态），支持按接口类型查询</p>

系统技术底座 “深度融合医院现有 HIS、LIS、PACS 等系统接口”🔶21-2；检中阶段 “标本外送回填（外送机构上传结果自动回填）”🔶21-31
</template>
