import { get, post } from '~/utils/request'

const baseUrl = '/sys/barCodeConfig'

/**
 * 条码配置列表
 */
export function list(params: any) {
  return get(`${baseUrl}/list`, params)
}

/**
 * 新增条码配置
 */
export function add(data: any) {
  return post(`${baseUrl}/add`, data)
}

/**
 * 编辑条码配置(获取)
 */
export function getEdit(id: number) {
  return get(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑条码配置(提交)
 */
export function postEdit(id: number, data: any) {
  return post(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除条码配置
 */
export function remove(ids: number[]) {
  return post(`${baseUrl}/delete`, ids)
}

/**
 * 启用条码配置
 */
export function enable(ids: number[]) {
  return post(`${baseUrl}/enable`, ids)
}

/**
 * 禁用条码配置
 */
export function disable(ids: number[]) {
  return post(`${baseUrl}/disable`, ids)
}
