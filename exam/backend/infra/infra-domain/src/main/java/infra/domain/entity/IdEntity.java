package infra.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 含Id的主键实体
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public abstract class IdEntity implements IEntity, Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;
}