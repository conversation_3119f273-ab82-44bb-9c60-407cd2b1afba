package module.sys.model;

import com.baomidou.mybatisplus.annotation.EnumValue;
import infra.core.common.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户类别限制
 */
@Getter
@AllArgsConstructor
public enum CustomerLimit implements IDictEnum {
    NORMAL(0, "不限"),
    ADULT(1, "个人"),
    CHILD(2, "团队");

    @EnumValue
    private final int value;
    private final String name;
}
