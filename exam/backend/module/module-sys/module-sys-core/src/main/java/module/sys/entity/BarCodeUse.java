package module.sys.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import infra.domain.entity.IdEntity;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 检验条码使用
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_bar_code_use")
public class BarCodeUse extends IdEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    // 配置id
    private Long configId;

    // 条形码前缀
    private String prefix;

    // 编号
    private Long no;

    // 关联客户id
    private Long customerId;

    // 关联客户名称
    private String customerName;

    // 创建时间
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}