package infra.protect.core;

/**
 * 限流器接口
 */
public interface IRateLimiter {

    /**
     * 尝试获取许可
     *
     * @param key    限流key
     * @param count  允许的请求数
     * @param window 时间窗口（秒）
     * @return 是否获取成功
     */
    boolean tryAcquire(String key, int count, int window);

    /**
     * 获取剩余许可数
     *
     * @param key    限流key
     * @param count  允许的请求数
     * @param window 时间窗口（秒）
     * @return 剩余许可数
     */
    long remaining(String key, int count, int window);

    /**
     * 重置限流计数
     *
     * @param key 限流key
     */
    void reset(String key);
}