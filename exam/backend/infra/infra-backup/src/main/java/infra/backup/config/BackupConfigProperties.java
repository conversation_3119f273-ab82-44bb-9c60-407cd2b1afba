package infra.backup.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 备份配置属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
@ConfigurationProperties(prefix = "app.backup")
public class BackupConfigProperties {
    
    /**
     * 数据库备份配置
     */
    private DatabaseConfig database = new DatabaseConfig();

    /**
     * 数据库备份配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatabaseConfig {
        
        /**
         * 是否开启备份
         */
        private Boolean enable = false;

        /**
         * 要备份的数据库连接参数列表
         */
        private List<ConnectionConfig> ds;

        /**
         * 压缩密码
         */
        private String compressPassword;

        /**
         * OSS存储路径，最终备份路径规则为 /OSS存储路径/年月/日时分.bak
         */
        private String ossPath;
    }

    /**
     * 备份数据库连接参数配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConnectionConfig {
        /**
         * 数据库连接URL
         */
        private String url;

        /**
         * 数据库用户名
         */
        private String username;

        /**
         * 数据库密码
         */
        private String password;

        /**
         * 排除的表名列表
         */
        private List<String> excludeTables;
    }
}
