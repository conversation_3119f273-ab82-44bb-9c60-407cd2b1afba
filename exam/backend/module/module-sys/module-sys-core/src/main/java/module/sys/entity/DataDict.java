package module.sys.entity;

import infra.domain.entity.EntityBase;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;

/**
 * 数据字典
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@TableName("sys_data_dict")
public class DataDict extends EntityBase {
    @Serial
    private static final long serialVersionUID = 1L;

    // 名称
    private String name;
    // 编号
    private String code;
    // 说明
    private String info;
    // 排序值
    @Builder.Default
    private Integer sort = 0;
}
