# MySQL备份使用示例

## 系统要求

确保系统已安装MySQL客户端工具：

```bash
# Ubuntu/Debian
sudo apt-get install mysql-client

# CentOS/RHEL
sudo yum install mysql

# macOS
brew install mysql-client
```

验证安装：
```bash
mysqldump --version
```

## 配置示例

```yaml
app:
  backup:
    database:
      enable: true
      ossPath: "backup/database"
      compressPassword: "your-secure-password"  # 可选，ZIP压缩密码
      ds:
        - name: "main-db"
          url: "*****************************************"
          username: "backup_user"
          password: "backup_password"
          excludeTables:
            - "temp_table"
            - "log_table"
            - "cache_table"
```

## 数据库用户权限

备份用户需要以下权限：

```sql
-- 创建备份用户
CREATE USER 'backup_user'@'%' IDENTIFIED BY 'backup_password';

-- 授予必要权限
GRANT SELECT ON your_database.* TO 'backup_user'@'%';
GRANT SHOW VIEW ON your_database.* TO 'backup_user'@'%';
GRANT TRIGGER ON your_database.* TO 'backup_user'@'%';
GRANT LOCK TABLES ON your_database.* TO 'backup_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 备份文件结构

生成的备份文件包含：

1. **数据库结构**：所有表的CREATE TABLE语句
2. **表数据**：所有表的INSERT语句
3. **存储过程**：PROCEDURE和FUNCTION定义
4. **触发器**：TRIGGER定义
5. **事件**：EVENT定义
6. **视图**：VIEW定义

## 还原示例

解压备份文件后，可直接执行SQL还原：

```bash
# 解压备份文件
unzip 111430.bak

# 还原数据库
mysql -h localhost -u root -p your_database < mysql_backup_xxx.sql
```

## 备份文件路径

```
backup/database/202408/111430.bak
├── 202408/           # 年月目录
│   ├── 111430.bak   # 11日14点30分的备份
│   ├── 111500.bak   # 11日15点00分的备份
│   └── 120930.bak   # 12日09点30分的备份
└── 202409/           # 下个月的备份
    └── 010800.bak
```