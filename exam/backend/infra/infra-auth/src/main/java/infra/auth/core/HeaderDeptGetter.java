package infra.auth.core;

import infra.core.text.Str;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;

/**
 * 从请求头中获取部门信息
 */
@AllArgsConstructor
public class HeaderDeptGetter implements IDeptGetter {
    private final String headerName;

    @Override
    public Long getDept(HttpServletRequest request) {
        String value = request.getHeader(headerName);
        if (Str.isEmpty(value)) {
            return null;
        }

        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}