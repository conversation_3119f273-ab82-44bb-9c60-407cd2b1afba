import { downloadFile, get, post, upload } from '~/utils/request'

const baseUrl = '/sys/dict'

/**
 * 数据字典列表
 */
export function list(params: any): Promise<any> {
  return get<any>(`${baseUrl}/list`, params)
}

/**
 * 新增数据字典
 */
export function add(data: any): Promise<any> {
  return post<any>(`${baseUrl}/add`, data)
}

/**
 * 编辑数据字典(获取)
 */
export function getEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/edit`, {
    id,
  })
}

/**
 * 编辑数据字典(提交)
 */
export function postEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/edit`, data, {
    id,
  })
}

/**
 * 删除数据字典
 */
export function remove(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/delete`, ids)
}

/**
 * 数据字典列表值
 */
export function listValue(id: number): Promise<any> {
  return get<any>(`${baseUrl}/valueList`, {
    id,
  })
}

/**
 * 新增数据字典值
 */
export function addValue(data: any): Promise<any> {
  return post<any>(`${baseUrl}/valueAdd`, data)
}

/**
 * 编辑数据字典值(获取)
 */
export function getValueEdit(id: number): Promise<any> {
  return get<any>(`${baseUrl}/valueEdit`, {
    id,
  })
}

/**
 * 编辑数据字典值(提交)
 */
export function postValueEdit(id: number, data: any): Promise<any> {
  return post<any>(`${baseUrl}/valueEdit`, data, {
    id,
  })
}

/**
 * 删除数据字典值
 */
export function removeValue(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/valueDelete`, ids)
}

/**
 * 导出数据字典值
 */
export function exportValue(dictId: number): Promise<any> {
  return downloadFile(`${baseUrl}/valueExport`, { params: { dictId } })
}

/**
 * 导入数据字典值
 */
export function importValue(dictId: number, file: File): Promise<any> {
  return upload(`${baseUrl}/valueImport`, file, { dictId })
}

/**
 * 启用字典值
 */
export function enableValue(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/valueEnable`, ids)
}

/**
 * 禁用字典值
 */
export function disableValue(ids: number[]): Promise<any> {
  return post<any>(`${baseUrl}/valueDisable`, ids)
}
