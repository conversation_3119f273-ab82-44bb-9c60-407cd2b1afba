package infra.domain.service;

/**
 * 过滤选项
 *
 * @param disableBizFilter      禁用基础业务过滤
 * @param disableDataPermFilter 禁用数据权限过滤
 */
public record FilterOptions(boolean disableBizFilter, boolean disableDataPermFilter) {
    /**
     * 默认选项
     */
    public static final FilterOptions DEFAULTS = new FilterOptions(false, false);

    /**
     * 禁用业务过滤
     */
    public static final FilterOptions DISABLE_BIZ_FILTER = new FilterOptions(true, false);

    /**
     * 禁用数据权限过滤
     */
    public static final FilterOptions DISABLE_DATA_PERM_FILTER = new FilterOptions(false, true);

    /**
     * 禁用所有过滤
     */
    public static final FilterOptions DISABLE_ALL_FILTER = new FilterOptions(true, true);

    /**
     * 生成缓存键
     */
    public String toCacheKey() {
        if (!disableBizFilter && !disableDataPermFilter) {
            return "";
        }
        return "f" + (disableBizFilter ? "1" : "0") + (disableDataPermFilter ? "2" : "0");
    }

}
