package basic.configure.doc;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

/**
 * Swagger 配置
 */
@AutoConfiguration
@ConditionalOnProperty(prefix = "app.doc", name = "swagger", havingValue = "true")
@Slf4j
public class SwaggerAutoConfiguration {
    @Bean
    public OpenAPI openAPI() {
        log.info("[配置] 已启用 Swagger 文档，访问页面为：swagger-ui.html");

        return new OpenAPI().info(new Info()
                .title("API")
                .version("v1"));
    }
}
