package module.sys.admin.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import module.sys.model.SliderVerifyData;

import java.util.List;

public class AuthDto {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserLoginDto {
        @NotBlank(message = "登录账号不能为空")
        private String loginName;
        @NotBlank(message = "登录密码不能为空")
        private String password;
        @NotNull(message = "滑块验证数据不能为空")
        private SliderVerifyData sliderData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefreshTokenDto {
        @NotBlank(message = "刷新token不能为空")
        private String refreshToken;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChangePasswordDto {
        @NotBlank(message = " 原密码不能为空")
        private String oldPassword;
        @NotBlank(message = "新密码不能为空")
        private String newPassword;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UserInfoDto {
        private Long id;
        private String loginName;
        private String userName;
        private Boolean needChangePassword;
        private List<DeptPostDto> deptPosts;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DeptPostDto {
        private Long deptId;
        private String deptName;
        private Long postId;
        private String postName;
        private String postCode;
        private boolean main;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UserBaseDto {
        private String loginName;
        private String userName;
        private String phone;
        private String email;
    }

}
