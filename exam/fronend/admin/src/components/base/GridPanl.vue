<script setup lang="ts">
interface GridPanlProps {
  /** 数据 */
  data?: any[]
  /** 默认插槽中要显示的项目的属性名称(默认为name) */
  labelField?: string
  /** 用于Key的属性名称(默认为id) */
  keyField?: string
  /** 是否支持选中 */
  selectable?: boolean
  /** 单元格高度  */
  cellWidth?: number
  /** 单元格高度  */
  cellHeight?: number
  /** 高亮项  */
  highlights?: any[]
}

const props = withDefaults(defineProps<GridPanlProps>(), {
  data: () => [],
  keyField: 'id',
  labelField: 'name',
  cellWidth: 160,
  cellHeight: 40,
  selectable: false,
  highlights: () => [],
})

const emits = defineEmits<{
  itemClick: [any]
}>()

defineSlots<{
  default: (props: { item: any, index: number, highlight: boolean }) => any
}>()

const modelValue = defineModel()

// 样式计算属性
const gridStyle = computed(() => `grid-template-columns: repeat(auto-fit,minmax(${props.cellWidth}px,1fr))`)
const cellStyle = computed(() => `height:${props.cellHeight}px`)

// 是否高亮
const isHighlighted = (item: any) => props.highlights.includes(item)

// 点击事件
function handleItemClick(item: any) {
  if (props.selectable) {
    const newValue = modelValue.value === item ? null : item
    modelValue.value = newValue
  }
  emits('itemClick', item)
}
</script>

<template>
  <div class="grid gap-[1px] border border-gray-200 border-solid dark:border-gray-600" :style="gridStyle">
    <div
      v-for="(item, index) in data" :key="item[props.keyField] || index"
      class="flex cursor-pointer items-center justify-center overflow-y-auto p-5 outline-1 outline-gray-200 outline-solid transition-colors duration-300 hover:bg-gray-100 dark:outline-gray-700 dark:hover:bg-gray-800"
      :style="cellStyle" @click="handleItemClick(item)"
    >
      <slot :item="item" :index="index" :highlight="isHighlighted(item)">
        <el-text :type="isHighlighted(item) ? 'primary' : ''">
          {{ (item as any)[props.labelField] }}
        </el-text>
      </slot>
    </div>
  </div>
</template>
