package infra.protect.annotation;

import java.lang.annotation.*;

/**
 * 限流注解
 * <p>
 * 支持基于IP、用户的限流配置
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {
    /**
     * 限流类型
     */
    LimitType type() default LimitType.IP;

    /**
     * 限流次数
     */
    int count() default 0;

    /**
     * 时间窗口
     */
    int window() default 0;

    /**
     * 限流失败时的提示信息
     */
    String message() default "";

    /**
     * 限流类型枚举
     */
    enum LimitType {
        /**
         * 基于IP限流
         */
        IP,
        /**
         * 基于用户限流
         */
        USER
    }
}