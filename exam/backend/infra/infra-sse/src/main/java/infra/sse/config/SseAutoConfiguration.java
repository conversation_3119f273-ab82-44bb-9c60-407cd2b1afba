package infra.sse.config;

import infra.sse.core.SseConnectionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * SSE自动配置
 */
@Slf4j
@AutoConfiguration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnProperty(name = "app.sse.enabled", havingValue = "true")
@EnableConfigurationProperties(SseConfigProperties.class)
@EnableAsync
public class SseAutoConfiguration {

    @Bean
    public SseConnectionManager infraSseConnectionManager(SseConfigProperties config) {
        log.info("[配置] 初始化SSE连接管理器");
        return new SseConnectionManager(config);
    }
}
